<?php
function create_post_type() {

  $default_supports = array('title', 'editor', 'excerpt', 'author', 'thumbnail', 'revisions', 'custom-fields', 'comments', 'page-attributes');
  $minimal_supports = array('title', 'author', 'thumbnail', 'revisions', 'page-attributes');
  $bare_support = array('title', 'author', 'revisions', 'page-attributes');

  $post_types = array(
    'portfolio' => array(
      'labels' => array('name' => __('Portfolios'), 'singular_name' => __('Portfolio'), 'add_new_item' => __('Add new Portfolio'), 'edit_item' => 'Edit Portfolio', 'menu_name' => 'Portfolios'),
      'public'    => true,
      'has_archive' => true,
      'rewrite'   => array('slug' => 'our-work', 'with_front' => true),
      'supports'    => $default_supports,
      'taxonomies'  => array('post_tag'),
      'exclude_from_search' => false,
      'show_in_rest' => true,
      'show_in_graphql' => true,
      'graphql_single_name' => 'portfolio',
      'graphql_plural_name' => 'portfolios'
    ),
    'client' => array(
      'labels' => array('name' => __('Clients'), 'singular_name' => __('Client'), 'add_new_item' => __('Add new Client'), 'edit_item' => 'Edit Client', 'menu_name' => 'Clients'),
      'public'    => true,
      'has_archive' => false,
      'rewrite'   => array('slug' => 'client', 'with_front' => false),
      'supports'    => $bare_support,
      'taxonomies'  => array(),
      'exclude_from_search' => true,
      'show_in_graphql' => true,
      'graphql_single_name' => 'client',
      'graphql_plural_name' => 'clients'
    ),
    'client_preview' => array(
      'labels' => array('name' => __('Client previews'), 'singular_name' => __('Client preview'), 'add_new_item' => __('Add new Client preview'), 'edit_item' => 'Edit Client preview', 'menu_name' => 'Client previews'),
      'menu_icon' => 'dashicons-visibility',
      'public'    => true,
      'has_archive' => false,
      'rewrite'   => array('slug' => 'client-preview', 'with_front' => true),
      'supports'    => $minimal_supports,
      'taxonomies'  => array('category'),
      'exclude_from_search' => true,
      'show_in_graphql' => true,
      'graphql_single_name' => 'clientPreview',
      'graphql_plural_name' => 'clientPreviews'
    )
  );

  foreach($post_types as $name => $params) {
    register_post_type($name, $params);
  }

}
add_action('init', 'create_post_type');
