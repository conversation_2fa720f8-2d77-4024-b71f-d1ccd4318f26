<?php

namespace Roots\Sage\Extras;

use Roots\Sage\Setup;

/**
 * Add <body> classes
 */
function body_class($classes) {
  // Add page slug if it doesn't exist
  if (is_single() || is_page() && !is_front_page()) {
    if (!in_array(basename(get_permalink()), $classes)) {
      $classes[] = basename(get_permalink());
    }
  }

  // Add class if sidebar is active
  if (Setup\display_sidebar()) {
    $classes[] = 'sidebar-primary';
  }

  // Add class for Portfolio item: GRID
  if( is_single() && get_field('grid_enable') ) {
    $classes[] = 'single-folio-grid';
  }

  return $classes;
}
add_filter('body_class', __NAMESPACE__ . '\\body_class');

/**
 * Clean up the_excerpt()
 */
function excerpt_more() {
  return ' &hellip; <a href="' . get_permalink() . '">' . __('Continued', 'sage') . '</a>';
}
add_filter('excerpt_more', __NAMESPACE__ . '\\excerpt_more');

// allow to upload SVG files
function custom_mime_types( $mimes ){
  $mimes['svg'] = 'image/svg+xml';
  return $mimes;
}
add_filter('upload_mimes', __NAMESPACE__ . '\\custom_mime_types');

function fix_svg_thumb_display() {
  echo '<style type="text/css">
    table.media .media-icon img[src$=".svg"], img[src$=".svg"].attachment-post-thumbnail {
      width: 100% !important;
      height: auto !important;
    }
  </style>';
}
add_action('admin_head', __NAMESPACE__ . '\\fix_svg_thumb_display');

function saint_gdpr_extra_settings() {
  echo 'jojo';
}
add_action('gdpr_extra_settings', __NAMESPACE__ . '\\saint_gdpr_extra_settings',99);

/**
 * GraphQL: add extra custom fields to Post object
 */
add_action( 'graphql_register_types', function() {
  $post_type = ['Page', 'Portfolio', 'ClientPreview'];
  foreach($post_type as $type) {
    register_graphql_field( $type, 'postPassword', [
      'type' => 'String',
      'description' => __( $type.' Password', 'sage' ),
      'resolve' => function( $post, $args, $context, $info ) {
        $page = get_post($post->ID);
        if(!$page) return null;
        $pass = $page->post_password;
        return $pass ?: null;
      }
  ]);
  }
});