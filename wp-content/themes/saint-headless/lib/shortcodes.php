<?php
add_action('init', function(){
    add_shortcode( 'gdpr_button', function($atts, $content) {
        extract(shortcode_atts( array(
            'action' => 'preferences'
        ), $atts ));
        $html = '';
        switch($action) {
            case 'preferences':
                $html = '<button type="button" class="cd-btn" data-cc="show-preferencesModal">Show my preferences</button>';
                break;
            case 'consent':
                $html = '<button type="button" class="cd-btn" data-cc="show-consentModal">View my Consent </button>';
            default:
                break;
        }
        return $html;
    } );
});