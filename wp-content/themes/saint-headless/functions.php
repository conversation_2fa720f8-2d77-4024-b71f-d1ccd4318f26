<?php
/**
 * Sage includes
 *
 * The $sage_includes array determines the code library included in your theme.
 * Add or remove files to the array as needed. Supports child theme overrides.
 *
 * Please note that missing files will produce a fatal error.
 *
 * @link https://github.com/roots/sage/pull/1042
 */

if( !is_admin() ){
//   error_reporting(E_ALL);
//   ini_set('display_errors', '1');
}

$sage_includes = [
  'lib/assets.php',    // Scripts and stylesheets
  'lib/post-types.php',
  'lib/extras.php',    // Custom functions
  'lib/setup.php',     // Theme setup
  'lib/shortcodes.php', // Shortcodes
];

foreach ($sage_includes as $file) {
  if (!$filepath = locate_template($file)) {
    trigger_error(sprintf(__('Error locating %s for inclusion', 'sage'), $file), E_USER_ERROR);
  }

  require_once $filepath;
}
unset($file, $filepath);
