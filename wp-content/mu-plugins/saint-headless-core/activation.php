<?php
/**
 * Plugin activation functionality.
 *
 * <AUTHOR>
 * @package SAINT_Headless_Core
 * @since 1.0.0
 */

namespace SAINT_Headless_Core;

/**
 * Plugin activation callback.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
function activation_callback() {
	do_action( 'saint_headless_core_activate' );

	// Save current plugin version.
	update_option( 'saint_headless_core_version', SAINT_HEADLESS_CORE_VERSION );
}
