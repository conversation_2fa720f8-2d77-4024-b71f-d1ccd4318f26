<?php
/**
 * Settings functionality.
 *
 * <AUTHOR>
 * @package SAINT_Headless_Core
 * @since 1.0.0
 */

namespace SAINT_Headless_Core;

/**
 * Register custom headless settings.
 *
 * <AUTHOR>
 * @since 2.0.0
 */
function register_settings() {
	if ( ! defined( 'SAINT_HEADLESS_CORE_OPTION_NAME' ) ) {
		return;
	}

	$option_name = SAINT_HEADLESS_CORE_OPTION_NAME;

	register_setting(
		"{$option_name}_group",
		$option_name,
		[
			'description'       => esc_html__( 'Headless Config Settings', 'saint-headless-core' ),
			'sanitize_callback' => __NAMESPACE__ . '\sanitize_settings',
			'type'              => 'array',
		]
	);

}
add_action( 'init', __NAMESPACE__ . '\register_settings' );

/**
 * Sanitize headless settings.
 *
 * <AUTHOR>
 * @since 2.0.0
 * @param  array $input Settings inputs.
 * @return array        Sanitized inputs.
 */
function sanitize_settings( $input ) {
	$sanitized_input = [];

	if ( empty( $input ) ) {
		return $sanitized_input;
	}

	foreach ( $input as $key => $value ) {
		if ( 'error_404_page' === $key ) {
			if ( is_nan( (float)$value ) ) {
				continue;
			}

			$sanitized_input[ $key ] = absint( $value );
			continue;
		}

		$sanitized_input[ $key ] = sanitize_text_field( $value );
	}

	return $sanitized_input;
}

/**
 * Add headless settings page link.
 *
 * <AUTHOR>
 * @since 2.0.0
 */
function add_settings_link() {
	add_options_page(
		esc_html__( 'Headless Config', 'saint-headless-core' ),
		esc_html__( 'Headless Config', 'saint-headless-core' ),
		'edit_posts',
		'headless-config',
		__NAMESPACE__ . '\display_settings_page'
	);
}
add_action( 'admin_menu', __NAMESPACE__ . '\add_settings_link' );

/**
 * Display headless settings page.
 *
 * <AUTHOR>
 * @since 2.0.0
 */
function display_settings_page() {
	if ( ! defined( 'SAINT_HEADLESS_CORE_OPTION_NAME' ) ) {
		return;
	}
	?>

	<div class="wrap">
		<h2><?php esc_html_e( 'Headless Config', 'saint-headless-core' ); ?></h2>

		<div>
			<form method="post" action="options.php" enctype="multipart/form-data">

				<?php
					settings_fields( SAINT_HEADLESS_CORE_OPTION_NAME . '_group' );
					do_settings_sections( 'headless-config' );
					submit_button();
				?>

			</form>
		</div>
	</div>

	<?php
}

/**
 * Register headless settings fields.
 *
 * <AUTHOR>
 * @since 2.0.0
 */
function add_settings_fields() {
	// Custom page options.
	add_settings_section(
		'pages',
		esc_html__( 'Headless Site Options', 'saint-headless-core' ),
		null,
		'headless-config'
	);

	// Frontend URL.
	add_settings_field(
		'frontend_url',
		esc_html__( 'Front End URL', 'saint-headless-core' ),
		__NAMESPACE__ . '\display_frontend_url_input',
		'headless-config',
		'pages'
	);

	// Error 404 page.
	add_settings_field(
		'error_404_page',
		esc_html__( '404 Page', 'saint-headless-core' ),
		__NAMESPACE__ . '\display_error_404_page_input',
		'headless-config',
		'pages'
	);
}
add_action( 'admin_init', __NAMESPACE__ . '\add_settings_fields' );

/**
 * Display FrontEnd URL input.
 *
 * <AUTHOR>
 * @since 2.0.0
 */
function display_frontend_url_input() {
	if ( ! defined( 'SAINT_HEADLESS_CORE_OPTION_NAME' ) ) {
		return;
	}

	$field_id      = 'frontend_url';
	$option_name   = SAINT_HEADLESS_CORE_OPTION_NAME;
	$options       = get_option( $option_name );
	$field_val = $options[ $field_id ] ?? '';
	?>

	<div>
	<p style="margin: 0 0 1rem 0; font-style: italic;"><?php esc_html_e( 'Optional. Select a custom 404 page. The content entered on this page will appear on the headless frontend.', 'saint-headless-core' ); ?></p>
		<input class="regular-text" type="text" id="<?= esc_attr( $field_id ); ?>" name="<?= esc_attr( "{$option_name}[$field_id]" ); ?>" value="<?= $field_val; ?>">
	</div>

	<?php
}

/**
 * Display Error 404 Page input.
 *
 * <AUTHOR>
 * @since 2.0.0
 */
function display_error_404_page_input() {
	if ( ! defined( 'SAINT_HEADLESS_CORE_OPTION_NAME' ) ) {
		return;
	}

	$field_id      = 'error_404_page';
	$option_name   = SAINT_HEADLESS_CORE_OPTION_NAME;
	$options       = get_option( $option_name );
	$selected_page = $options[ $field_id ] ?? '';
	$pages         = get_posts(
		[
			'numberposts' => -1,
			'post_type'   => 'page',
			'post_status' => 'publish',
		]
	);
	?>

	<div>
	<p style="margin: 0 0 1rem 0; font-style: italic;"><?php esc_html_e( 'Optional. Select a custom 404 page. The content entered on this page will appear on the headless frontend.', 'saint-headless-core' ); ?></p>
	<select id="<?php echo esc_attr( $field_id ); ?>" name="<?php echo esc_attr( "{$option_name}[$field_id]" ); ?>">
		<option><?php esc_html_e( '-- Select page --', 'saint-headless-core' ); ?></option>

		<?php	foreach ( $pages as $page ) : ?>

			<option value="<?php echo esc_attr( $page->ID ); ?>" <?php echo $selected_page === $page->ID ? 'selected="selected"' : ''; ?>><?php echo esc_attr( $page->post_title ); ?></option>

		<?php endforeach; ?>

	</select>
	</div>

	<?php
}

/**
 * Migrate settings from ACF on upgrade.
 *
 * <AUTHOR>
 * @since 2.0.0
 */
function migrate_settings() {
	if ( ! defined( 'SAINT_HEADLESS_CORE_VERSION' ) || ! defined( 'SAINT_HEADLESS_CORE_OPTION_NAME' ) ) {
		return;
	}

	$option_name   = 'saint_headless_core_version';
	$saved_version = get_option( $option_name );

	if ( SAINT_HEADLESS_CORE_VERSION === $saved_version ) {
		return;
	}

	update_option( $option_name, SAINT_HEADLESS_CORE_VERSION );

	// Retrieve old ACF settings.
	$error_404_page = get_option( 'options_error_404_page' );
	$error_404_page = is_nan( $error_404_page ) ? null : absint( $error_404_page );

	if ( ! $error_404_page ) {
		return;
	}

	update_option( SAINT_HEADLESS_CORE_OPTION_NAME, [ 'error_404_page' => $error_404_page ] );
}
add_action( 'plugins_loaded', __NAMESPACE__ . '\migrate_settings' );
