<?php
/**
 * Plugin Name: SAINT Headless (Core)
 * Plugin URI: https://saintdesign.co.uk
 * Description: The core WordPress plugin for the Next.js WordPress Starter.
 * Author: JJ <dev@saintdesign>
 * Author URI: https://saintdesign
 * Version: 1.0.0
 * Requires at least: 5.6
 * Requires PHP: 7.4
 * License: GPL-2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 *
 * @package SAINT_Headless_Core
 */

namespace SAINT_Headless_Core;

if ( ! defined( 'ABSPATH' ) ) {
	return;
}

// Define constants.
define( 'SAINT_HEADLESS_CORE_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'SAINT_HEADLESS_CORE_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'SAINT_HEADLESS_CORE_VERSION', '2.1.4' );
define( 'SAINT_HEADLESS_CORE_OPTION_NAME', 'headless_config' );

/**
 * Define current Front End url to proxy all links from WP backend
*/
if(!defined('HEADLESS_FRONTEND_URL')) {

	$options = get_option(SAINT_HEADLESS_CORE_OPTION_NAME);
	$url = $options['frontend_url'];
	if( !$url || $url == '' ) $url = 'https://brasseriebarco.com/';
	define('HEADLESS_FRONTEND_URL', $url);
}

/**
 * Define secrets for Preview and Authentication
 */
define( 'PREVIEW_SECRET_TOKEN', 'saint-secret-phrase-hard2guess-letmein2024');
define( 'GRAPHQL_JWT_AUTH_SECRET_KEY', '[+:40J04<j9:$!mvbC_-N:k)%+Z<}7IT24x*u$HeZ6yaWK}4OaoBq-ZZ.$^fDuib' );

// Register de/activation hooks.
register_activation_hook( __FILE__, __NAMESPACE__ . '\activation_callback' );
register_deactivation_hook( __FILE__, __NAMESPACE__ . '\deactivation_callback' );

require_once 'inc/editor.php';
require_once 'inc/links.php';
require_once 'inc/settings.php';
require_once 'inc/wp-graphql.php';
require_once 'inc/revalidation.php';
