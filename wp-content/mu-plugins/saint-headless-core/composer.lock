{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "16fc6fb42dbd300dee0fc01b9d2953dc", "packages": [], "packages-dev": [{"name": "composer/ca-bundle", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b", "reference": "4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.3.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-10-28T20:44:15+00:00"}, {"name": "composer/composer", "version": "2.3.5", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "50c47b1f907cfcdb8f072b88164d22b527557ae1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/50c47b1f907cfcdb8f072b88164d22b527557ae1", "reference": "50c47b1f907cfcdb8f072b88164d22b527557ae1", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "composer/metadata-minifier": "^1.0", "composer/pcre": "^2 || ^3", "composer/semver": "^3.0", "composer/spdx-licenses": "^1.2", "composer/xdebug-handler": "^2.0.2 || ^3.0.3", "justinrainbow/json-schema": "^5.2.11", "php": "^7.2.5 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "react/promise": "^2.8", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.2", "symfony/console": "^5.4.1 || ^6.0", "symfony/filesystem": "^5.4 || ^6.0", "symfony/finder": "^5.4 || ^6.0", "symfony/polyfill-php73": "^1.24", "symfony/polyfill-php80": "^1.24", "symfony/process": "^5.4 || ^6.0"}, "require-dev": {"phpstan/phpstan": "^1.4.1", "phpstan/phpstan-deprecation-rules": "^1", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1", "phpstan/phpstan-symfony": "^1.1", "symfony/phpunit-bridge": "^6.0"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.3-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/composer/issues", "source": "https://github.com/composer/composer/tree/2.3.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-04-13T14:43:00+00:00"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/composer/metadata-minifier.git", "reference": "c549d23829536f0d0e984aaabbf02af91f443207"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"composer/composer": "^2", "phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "support": {"issues": "https://github.com/composer/metadata-minifier/issues", "source": "https://github.com/composer/metadata-minifier/tree/1.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-04-07T13:37:33+00:00"}, {"name": "composer/pcre", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "e300eb6c535192decd27a85bc72a9290f0d6b3bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/e300eb6c535192decd27a85bc72a9290f0d6b3bd", "reference": "e300eb6c535192decd27a85bc72a9290f0d6b3bd", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-25T20:21:48+00:00"}, {"name": "composer/semver", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/3953f23262f2bff1919fc82183ad9acb13ff62c9", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-04-01T19:23:25+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.6", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "a30d487169d799745ca7280bc90fdfa693536901"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/a30d487169d799745ca7280bc90fdfa693536901", "reference": "a30d487169d799745ca7280bc90fdfa693536901", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/spdx-licenses/issues", "source": "https://github.com/composer/spdx-licenses/tree/1.5.6"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-11-18T10:14:14+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "ced299686f41dce890debac69273b47ffe98a40c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/ced299686f41dce890debac69273b47ffe98a40c", "reference": "ced299686f41dce890debac69273b47ffe98a40c", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-25T21:32:43+00:00"}, {"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}, "time": "2022-02-04T12:51:07+00:00"}, {"name": "eftec/bladeone", "version": "3.52", "source": {"type": "git", "url": "https://github.com/EFTEC/BladeOne.git", "reference": "a19bf66917de0b29836983db87a455a4f6e32148"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/EFTEC/BladeOne/zipball/a19bf66917de0b29836983db87a455a4f6e32148", "reference": "a19bf66917de0b29836983db87a455a4f6e32148", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16.1", "phpunit/phpunit": "^5.7", "squizlabs/php_codesniffer": "^3.5.4"}, "suggest": {"eftec/bladeonehtml": "Extension to create forms", "ext-mbstring": "This extension is used if it's active"}, "type": "library", "autoload": {"psr-4": {"eftec\\bladeone\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The standalone version Blade Template Engine from Laravel in a single php file", "homepage": "https://github.com/EFTEC/BladeOne", "keywords": ["blade", "php", "template", "templating", "view"], "support": {"issues": "https://github.com/EFTEC/BladeOne/issues", "source": "https://github.com/EFTEC/BladeOne/tree/3.52"}, "time": "2021-04-17T13:49:01+00:00"}, {"name": "gettext/gettext", "version": "v4.8.6", "source": {"type": "git", "url": "https://github.com/php-gettext/Gettext.git", "reference": "bbeb8f4d3077663739aecb4551b22e720c0e9efe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Gettext/zipball/bbeb8f4d3077663739aecb4551b22e720c0e9efe", "reference": "bbeb8f4d3077663739aecb4551b22e720c0e9efe", "shasum": ""}, "require": {"gettext/languages": "^2.3", "php": ">=5.4.0"}, "require-dev": {"illuminate/view": "^5.0.x-dev", "phpunit/phpunit": "^4.8|^5.7|^6.5", "squizlabs/php_codesniffer": "^3.0", "symfony/yaml": "~2", "twig/extensions": "*", "twig/twig": "^1.31|^2.0"}, "suggest": {"illuminate/view": "Is necessary if you want to use the Blade extractor", "symfony/yaml": "Is necessary if you want to use the Yaml extractor/generator", "twig/extensions": "Is necessary if you want to use the Twig extractor", "twig/twig": "Is necessary if you want to use the Twig extractor"}, "type": "library", "autoload": {"psr-4": {"Gettext\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "PHP gettext manager", "homepage": "https://github.com/oscarotero/Gettext", "keywords": ["JS", "gettext", "i18n", "mo", "po", "translation"], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/Gettext/issues", "source": "https://github.com/php-gettext/Gettext/tree/v4.8.6"}, "funding": [{"url": "https://paypal.me/oscarotero", "type": "custom"}, {"url": "https://github.com/oscarotero", "type": "github"}, {"url": "https://www.patreon.com/misteroom", "type": "patreon"}], "time": "2021-10-19T10:44:53+00:00"}, {"name": "gettext/languages", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/php-gettext/Languages.git", "reference": "ed56dd2c7f4024cc953ed180d25f02f2640e3ffa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-gettext/Languages/zipball/ed56dd2c7f4024cc953ed180d25f02f2640e3ffa", "reference": "ed56dd2c7f4024cc953ed180d25f02f2640e3ffa", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5 || ^8.4"}, "bin": ["bin/export-plural-rules"], "type": "library", "autoload": {"psr-4": {"Gettext\\Languages\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "gettext languages with plural rules", "homepage": "https://github.com/php-gettext/Languages", "keywords": ["cldr", "i18n", "internationalization", "l10n", "language", "languages", "localization", "php", "plural", "plural rules", "plurals", "translate", "translations", "unicode"], "support": {"issues": "https://github.com/php-gettext/Languages/issues", "source": "https://github.com/php-gettext/Languages/tree/2.9.0"}, "funding": [{"url": "https://paypal.me/mlocati", "type": "custom"}, {"url": "https://github.com/mlocati", "type": "github"}], "time": "2021-11-11T17:30:39+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.12", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "ad87d5a5ca981228e0e205c2bc7dfb8e24559b60"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/ad87d5a5ca981228e0e205c2bc7dfb8e24559b60", "reference": "ad87d5a5ca981228e0e205c2bc7dfb8e24559b60", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/justinrainbow/json-schema/issues", "source": "https://github.com/justinrainbow/json-schema/tree/5.2.12"}, "time": "2022-04-13T08:02:27+00:00"}, {"name": "mck89/peast", "version": "v1.13.11", "source": {"type": "git", "url": "https://github.com/mck89/peast.git", "reference": "78c57966f3da5f223636ea0417d71ac6ff61e47f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mck89/peast/zipball/78c57966f3da5f223636ea0417d71ac6ff61e47f", "reference": "78c57966f3da5f223636ea0417d71ac6ff61e47f", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.13.11-dev"}}, "autoload": {"psr-4": {"Peast\\": "lib/Peast/", "Peast\\test\\": "test/Peast/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Peast is PHP library that generates AST for JavaScript code", "support": {"issues": "https://github.com/mck89/peast/issues", "source": "https://github.com/mck89/peast/tree/v1.13.11"}, "time": "2022-01-11T17:58:18+00:00"}, {"name": "mustache/mustache", "version": "v2.14.1", "source": {"type": "git", "url": "https://github.com/bobthecow/mustache.php.git", "reference": "579ffa5c96e1d292c060b3dd62811ff01ad8c24e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/mustache.php/zipball/579ffa5c96e1d292c060b3dd62811ff01ad8c24e", "reference": "579ffa5c96e1d292c060b3dd62811ff01ad8c24e", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "~1.11", "phpunit/phpunit": "~3.7|~4.0|~5.0"}, "type": "library", "autoload": {"psr-0": {"Mustache": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "A Mustache implementation in PHP.", "homepage": "https://github.com/bobthecow/mustache.php", "keywords": ["mustache", "templating"], "support": {"issues": "https://github.com/bobthecow/mustache.php/issues", "source": "https://github.com/bobthecow/mustache.php/tree/v2.14.1"}, "time": "2022-01-21T06:08:36+00:00"}, {"name": "nb/oxymel", "version": "v0.1.0", "source": {"type": "git", "url": "https://github.com/nb/oxymel.git", "reference": "cbe626ef55d5c4cc9b5e6e3904b395861ea76e3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nb/oxymel/zipball/cbe626ef55d5c4cc9b5e6e3904b395861ea76e3c", "reference": "cbe626ef55d5c4cc9b5e6e3904b395861ea76e3c", "shasum": ""}, "require": {"php": ">=5.2.4"}, "type": "library", "autoload": {"psr-0": {"Oxymel": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://extrapolate.me/"}], "description": "A sweet XML builder", "homepage": "https://github.com/nb/oxymel", "keywords": ["xml"], "support": {"issues": "https://github.com/nb/oxymel/issues", "source": "https://github.com/nb/oxymel/tree/master"}, "time": "2013-02-24T15:01:54+00:00"}, {"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": ""}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}, "time": "2019-12-27T09:44:58+00:00"}, {"name": "phpcompatibility/phpcompatibility-paragonie", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie.git", "reference": "ddabec839cc003651f2ce695c938686d1086cf43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityParagonie/zipball/ddabec839cc003651f2ce695c938686d1086cf43", "reference": "ddabec839cc003651f2ce695c938686d1086cf43", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "paragonie/random_compat": "dev-master", "paragonie/sodium_compat": "dev-master"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A set of rulesets for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by the Paragonie polyfill libraries.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "paragonie", "phpcs", "polyfill", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie"}, "time": "2021-02-15T10:24:51+00:00"}, {"name": "phpcompatibility/phpcompatibility-wp", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityWP.git", "reference": "d55de55f88697b9cdb94bccf04f14eb3b11cf308"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityWP/zipball/d55de55f88697b9cdb94bccf04f14eb3b11cf308", "reference": "d55de55f88697b9cdb94bccf04f14eb3b11cf308", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "phpcs", "standards", "wordpress"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}, "time": "2021-12-30T16:37:40+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "react/promise", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/234f8fd1023c9158e2314fa9d7d0e6a83db42910", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.9.0"}, "funding": [{"url": "https://github.com/WyriHaximus", "type": "github"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-11T10:27:51+00:00"}, {"name": "rmccue/requests", "version": "v1.8.1", "source": {"type": "git", "url": "https://github.com/WordPress/Requests.git", "reference": "82e6936366eac3af4d836c18b9d8c31028fe4cd5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/Requests/zipball/82e6936366eac3af4d836c18b9d8c31028fe4cd5", "reference": "82e6936366eac3af4d836c18b9d8c31028fe4cd5", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5", "requests/test-server": "dev-master", "squizlabs/php_codesniffer": "^3.5", "wp-coding-standards/wpcs": "^2.0"}, "type": "library", "autoload": {"psr-0": {"Requests": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "homepage": "http://ryanmccue.info"}], "description": "A HTTP library written in PHP, for human beings.", "homepage": "http://github.com/WordPress/Requests", "keywords": ["curl", "fsockopen", "http", "idna", "ipv6", "iri", "sockets"], "support": {"issues": "https://github.com/WordPress/Requests/issues", "source": "https://github.com/WordPress/Requests/tree/v1.8.1"}, "time": "2021-06-04T09:56:25+00:00"}, {"name": "seld/jsonlint", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "4211420d25eba80712bff236a98960ef68b866b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/4211420d25eba80712bff236a98960ef68b866b7", "reference": "4211420d25eba80712bff236a98960ef68b866b7", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.5", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^8.5.13"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "support": {"issues": "https://github.com/Seldaek/jsonlint/issues", "source": "https://github.com/Seldaek/jsonlint/tree/1.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "time": "2022-04-01T13:37:23+00:00"}, {"name": "seld/phar-utils", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "9f3452c93ff423469c0d56450431562ca423dcee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/9f3452c93ff423469c0d56450431562ca423dcee", "reference": "9f3452c93ff423469c0d56450431562ca423dcee", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "support": {"issues": "https://github.com/Seldaek/phar-utils/issues", "source": "https://github.com/Seldaek/phar-utils/tree/1.2.0"}, "time": "2021-12-10T11:20:11+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.6.2", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "5e4e71592f69da17871dba6e80dd51bce74a351a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/5e4e71592f69da17871dba6e80dd51bce74a351a", "reference": "5e4e71592f69da17871dba6e80dd51bce74a351a", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards"], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}, "time": "2021-12-12T21:44:58+00:00"}, {"name": "symfony/console", "version": "v5.4.7", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "900275254f0a1a2afff1ab0e11abd5587a10e1d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/900275254f0a1a2afff1ab0e11abd5587a10e1d6", "reference": "900275254f0a1a2afff1ab0e11abd5587a10e1d6", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-31T17:09:19+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.7", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "3a4442138d80c9f7b600fb297534ac718b61d37f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/3a4442138d80c9f7b600fb297534ac718b61d37f", "reference": "3a4442138d80c9f7b600fb297534ac718b61d37f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-04-01T12:33:59+00:00"}, {"name": "symfony/finder", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "231313534dded84c7ecaa79d14bc5da4ccb69b7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/231313534dded84c7ecaa79d14bc5da4ccb69b7d", "reference": "231313534dded84c7ecaa79d14bc5da4ccb69b7d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-26T16:34:36+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "30885182c981ab175d4d034db0f6f469898070ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/30885182c981ab175d4d034db0f6f469898070ab", "reference": "30885182c981ab175d4d034db0f6f469898070ab", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-10-20T20:35:02+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "81b86b50cf841a64252b439e738e97f4a34e2783"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/81b86b50cf841a64252b439e738e97f4a34e2783", "reference": "81b86b50cf841a64252b439e738e97f4a34e2783", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-23T21:10:46+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8590a5f561694770bdcd3f9b5c69dde6945028e8", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-19T12:13:01+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "0abb51d2f102e00a4eefcf46ba7fec406d245825"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/0abb51d2f102e00a4eefcf46ba7fec406d245825", "reference": "0abb51d2f102e00a4eefcf46ba7fec406d245825", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-30T18:21:41+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/cc5db0e22b3cb4111010e48785a97f670b350ca5", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-06-05T21:20:04+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "4407588e0d3f1f52efb65fbe92babe41f37fe50c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/4407588e0d3f1f52efb65fbe92babe41f37fe50c", "reference": "4407588e0d3f1f52efb65fbe92babe41f37fe50c", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-04T08:16:47+00:00"}, {"name": "symfony/process", "version": "v5.4.7", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "38a44b2517b470a436e1c944bf9b9ba3961137fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/38a44b2517b470a436e1c944bf9b9ba3961137fb", "reference": "38a44b2517b470a436e1c944bf9b9ba3961137fb", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-18T16:18:52+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "24d9dc654b83e91aa59f9d167b131bc3b5bea24c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/24d9dc654b83e91aa59f9d167b131bc3b5bea24c", "reference": "24d9dc654b83e91aa59f9d167b131bc3b5bea24c", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-13T20:07:29+00:00"}, {"name": "symfony/string", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "92043b7d8383e48104e411bc9434b260dbeb5a10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/92043b7d8383e48104e411bc9434b260dbeb5a10", "reference": "92043b7d8383e48104e411bc9434b260dbeb5a10", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "wp-cli/cache-command", "version": "v2.0.9", "source": {"type": "git", "url": "https://github.com/wp-cli/cache-command.git", "reference": "05378440d8c6d4d2a1a5e5cbc1ba92a5e4bf1c40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/cache-command/zipball/05378440d8c6d4d2a1a5e5cbc1ba92a5e4bf1c40", "reference": "05378440d8c6d4d2a1a5e5cbc1ba92a5e4bf1c40", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["cache", "cache add", "cache decr", "cache delete", "cache flush", "cache get", "cache incr", "cache replace", "cache set", "cache type", "transient", "transient delete", "transient get", "transient set", "transient type", "transient list"]}, "autoload": {"files": ["cache-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Manages object and transient caches.", "homepage": "https://github.com/wp-cli/cache-command", "support": {"issues": "https://github.com/wp-cli/cache-command/issues", "source": "https://github.com/wp-cli/cache-command/tree/v2.0.9"}, "time": "2022-01-13T01:13:50+00:00"}, {"name": "wp-cli/checksum-command", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/wp-cli/checksum-command.git", "reference": "ec59a24af2ca97b770a4709b0a1c241eeb4b4cff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/checksum-command/zipball/ec59a24af2ca97b770a4709b0a1c241eeb4b4cff", "reference": "ec59a24af2ca97b770a4709b0a1c241eeb4b4cff", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["core verify-checksums", "plugin verify-checksums"]}, "autoload": {"files": ["checksum-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Verifies file integrity by comparing to published checksums.", "homepage": "https://github.com/wp-cli/checksum-command", "support": {"issues": "https://github.com/wp-cli/checksum-command/issues", "source": "https://github.com/wp-cli/checksum-command/tree/v2.1.2"}, "time": "2022-01-13T03:47:56+00:00"}, {"name": "wp-cli/config-command", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/wp-cli/config-command.git", "reference": "cdabbc47dae464a93b10361b9a18e84cf4e72fe2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/config-command/zipball/cdabbc47dae464a93b10361b9a18e84cf4e72fe2", "reference": "cdabbc47dae464a93b10361b9a18e84cf4e72fe2", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5", "wp-cli/wp-config-transformer": "^1.2.1"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["config", "config edit", "config delete", "config create", "config get", "config has", "config list", "config path", "config set", "config shuffle-salts"]}, "autoload": {"files": ["config-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.alainschlesser.com"}], "description": "Generates and reads the wp-config.php file.", "homepage": "https://github.com/wp-cli/config-command", "support": {"issues": "https://github.com/wp-cli/config-command/issues", "source": "https://github.com/wp-cli/config-command/tree/v2.1.3"}, "time": "2022-01-13T01:09:44+00:00"}, {"name": "wp-cli/core-command", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/wp-cli/core-command.git", "reference": "a75d052ea000b4f0ec14106c110836b376e95a4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/core-command/zipball/a75d052ea000b4f0ec14106c110836b376e95a4f", "reference": "a75d052ea000b4f0ec14106c110836b376e95a4f", "shasum": ""}, "require": {"composer/semver": "^1.4 || ^2 || ^3", "wp-cli/wp-cli": "^2.5.1"}, "require-dev": {"wp-cli/checksum-command": "^1 || ^2", "wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^3.1.4"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["core", "core check-update", "core download", "core install", "core is-installed", "core multisite-convert", "core multisite-install", "core update", "core update-db", "core version"]}, "autoload": {"files": ["core-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Downloads, installs, updates, and manages a WordPress installation.", "homepage": "https://github.com/wp-cli/core-command", "support": {"issues": "https://github.com/wp-cli/core-command/issues", "source": "https://github.com/wp-cli/core-command/tree/v2.1.1"}, "time": "2022-01-21T21:29:11+00:00"}, {"name": "wp-cli/cron-command", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/wp-cli/cron-command.git", "reference": "bb9fd9645e9a5276d024a59affeda3e6aa8530be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/cron-command/zipball/bb9fd9645e9a5276d024a59affeda3e6aa8530be", "reference": "bb9fd9645e9a5276d024a59affeda3e6aa8530be", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/server-command": "^2.0", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["cron", "cron test", "cron event", "cron event delete", "cron event list", "cron event run", "cron event schedule", "cron schedule", "cron schedule list", "cron event unschedule"]}, "autoload": {"files": ["cron-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Tests, runs, and deletes WP-Cron events; manages WP-Cron schedules.", "homepage": "https://github.com/wp-cli/cron-command", "support": {"issues": "https://github.com/wp-cli/cron-command/issues", "source": "https://github.com/wp-cli/cron-command/tree/v2.1.0"}, "time": "2022-01-22T00:03:27+00:00"}, {"name": "wp-cli/db-command", "version": "v2.0.20", "source": {"type": "git", "url": "https://github.com/wp-cli/db-command.git", "reference": "709f58c30d178afcdecaf56068ca9f5e985ed6b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/db-command/zipball/709f58c30d178afcdecaf56068ca9f5e985ed6b9", "reference": "709f58c30d178afcdecaf56068ca9f5e985ed6b9", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["db", "db clean", "db create", "db drop", "db reset", "db check", "db optimize", "db prefix", "db repair", "db cli", "db query", "db export", "db import", "db search", "db tables", "db size", "db columns"]}, "autoload": {"files": ["db-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Performs basic database operations using credentials stored in wp-config.php.", "homepage": "https://github.com/wp-cli/db-command", "support": {"issues": "https://github.com/wp-cli/db-command/issues", "source": "https://github.com/wp-cli/db-command/tree/v2.0.20"}, "time": "2022-01-25T03:11:39+00:00"}, {"name": "wp-cli/embed-command", "version": "v2.0.11", "source": {"type": "git", "url": "https://github.com/wp-cli/embed-command.git", "reference": "00a901a66aecb4da94a8dace610eb1135fc82386"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/embed-command/zipball/00a901a66aecb4da94a8dace610eb1135fc82386", "reference": "00a901a66aecb4da94a8dace610eb1135fc82386", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["embed", "embed fetch", "embed provider", "embed provider list", "embed provider match", "embed handler", "embed handler list", "embed cache", "embed cache clear", "embed cache find", "embed cache trigger"]}, "autoload": {"files": ["embed-command.php"], "psr-4": {"WP_CLI\\Embeds\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://pascalbirchler.com/"}], "description": "Inspects oEmbed providers, clears embed cache, and more.", "homepage": "https://github.com/wp-cli/embed-command", "support": {"issues": "https://github.com/wp-cli/embed-command/issues", "source": "https://github.com/wp-cli/embed-command/tree/v2.0.11"}, "time": "2022-01-13T01:19:27+00:00"}, {"name": "wp-cli/entity-command", "version": "v2.2.1", "source": {"type": "git", "url": "https://github.com/wp-cli/entity-command.git", "reference": "d7d08b05c67651abde5d570851e46498a164cb34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/entity-command/zipball/d7d08b05c67651abde5d570851e46498a164cb34", "reference": "d7d08b05c67651abde5d570851e46498a164cb34", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/cache-command": "^1 || ^2", "wp-cli/db-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/media-command": "^1.1 || ^2", "wp-cli/super-admin-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["comment", "comment approve", "comment count", "comment create", "comment delete", "comment exists", "comment generate", "comment get", "comment list", "comment meta", "comment meta add", "comment meta delete", "comment meta get", "comment meta list", "comment meta patch", "comment meta pluck", "comment meta update", "comment recount", "comment spam", "comment status", "comment trash", "comment unapprove", "comment unspam", "comment untrash", "comment update", "menu", "menu create", "menu delete", "menu item", "menu item add-custom", "menu item add-post", "menu item add-term", "menu item delete", "menu item list", "menu item update", "menu list", "menu location", "menu location assign", "menu location list", "menu location remove", "network meta", "network meta add", "network meta delete", "network meta get", "network meta list", "network meta patch", "network meta pluck", "network meta update", "option", "option add", "option delete", "option get", "option list", "option patch", "option pluck", "option update", "post", "post create", "post delete", "post edit", "post exists", "post generate", "post get", "post list", "post meta", "post meta add", "post meta delete", "post meta get", "post meta list", "post meta patch", "post meta pluck", "post meta update", "post term", "post term add", "post term list", "post term remove", "post term set", "post update", "post-type", "post-type get", "post-type list", "site", "site activate", "site archive", "site create", "site deactivate", "site delete", "site empty", "site list", "site mature", "site option", "site private", "site public", "site spam", "site unarchive", "site unmature", "site unspam", "taxonomy", "taxonomy get", "taxonomy list", "term", "term create", "term delete", "term generate", "term get", "term list", "term meta", "term meta add", "term meta delete", "term meta get", "term meta list", "term meta patch", "term meta pluck", "term meta update", "term recount", "term update", "user", "user add-cap", "user add-role", "user create", "user delete", "user generate", "user get", "user import-csv", "user list", "user list-caps", "user meta", "user meta add", "user meta delete", "user meta get", "user meta list", "user meta patch", "user meta pluck", "user meta update", "user remove-cap", "user remove-role", "user reset-password", "user session", "user session destroy", "user session list", "user set-role", "user spam", "user term", "user term add", "user term list", "user term remove", "user term set", "user unspam", "user update"]}, "autoload": {"files": ["entity-command.php"], "psr-4": {"": "src/", "WP_CLI\\": "src/WP_CLI"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Manage WordPress comments, menus, options, posts, sites, terms, and users.", "homepage": "https://github.com/wp-cli/entity-command", "support": {"issues": "https://github.com/wp-cli/entity-command/issues", "source": "https://github.com/wp-cli/entity-command/tree/v2.2.1"}, "time": "2022-01-24T20:49:29+00:00"}, {"name": "wp-cli/eval-command", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/wp-cli/eval-command.git", "reference": "5213040ec2167b2748f2689ff6fe24b92a064a90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/eval-command/zipball/5213040ec2167b2748f2689ff6fe24b92a064a90", "reference": "5213040ec2167b2748f2689ff6fe24b92a064a90", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["eval", "eval-file"]}, "autoload": {"files": ["eval-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Executes arbitrary PHP code or files.", "homepage": "https://github.com/wp-cli/eval-command", "support": {"issues": "https://github.com/wp-cli/eval-command/issues", "source": "https://github.com/wp-cli/eval-command/tree/v2.1.2"}, "time": "2022-01-13T01:19:34+00:00"}, {"name": "wp-cli/export-command", "version": "v2.0.11", "source": {"type": "git", "url": "https://github.com/wp-cli/export-command.git", "reference": "8dd137e0c739a59bb3d3de684a219dbb34473e11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/export-command/zipball/8dd137e0c739a59bb3d3de684a219dbb34473e11", "reference": "8dd137e0c739a59bb3d3de684a219dbb34473e11", "shasum": ""}, "require": {"nb/oxymel": "~0.1.0", "wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/import-command": "^1 || ^2", "wp-cli/media-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["export"]}, "autoload": {"files": ["export-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Exports WordPress content to a WXR file.", "homepage": "https://github.com/wp-cli/export-command", "support": {"issues": "https://github.com/wp-cli/export-command/issues", "source": "https://github.com/wp-cli/export-command/tree/v2.0.11"}, "time": "2021-12-13T16:02:15+00:00"}, {"name": "wp-cli/extension-command", "version": "v2.1.4", "source": {"type": "git", "url": "https://github.com/wp-cli/extension-command.git", "reference": "6401d7ea51084fac40010c2fb305be640675f385"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/extension-command/zipball/6401d7ea51084fac40010c2fb305be640675f385", "reference": "6401d7ea51084fac40010c2fb305be640675f385", "shasum": ""}, "require": {"composer/semver": "^1.4 || ^2 || ^3", "wp-cli/wp-cli": "^2.5.1"}, "require-dev": {"wp-cli/cache-command": "^2.0", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/scaffold-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["plugin", "plugin activate", "plugin deactivate", "plugin delete", "plugin get", "plugin install", "plugin is-installed", "plugin list", "plugin path", "plugin search", "plugin status", "plugin toggle", "plugin uninstall", "plugin update", "theme", "theme activate", "theme delete", "theme disable", "theme enable", "theme get", "theme install", "theme is-installed", "theme list", "theme mod", "theme mod get", "theme mod set", "theme mod remove", "theme path", "theme search", "theme status", "theme update", "theme mod list"]}, "autoload": {"files": ["extension-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.alainschlesser.com"}], "description": "Manages plugins and themes, including installs, activations, and updates.", "homepage": "https://github.com/wp-cli/extension-command", "support": {"issues": "https://github.com/wp-cli/extension-command/issues", "source": "https://github.com/wp-cli/extension-command/tree/v2.1.4"}, "time": "2022-01-25T02:07:46+00:00"}, {"name": "wp-cli/i18n-command", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/wp-cli/i18n-command.git", "reference": "bcb1a8159679cafdf1da884dbe5830122bae2c4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/i18n-command/zipball/bcb1a8159679cafdf1da884dbe5830122bae2c4d", "reference": "bcb1a8159679cafdf1da884dbe5830122bae2c4d", "shasum": ""}, "require": {"eftec/bladeone": "3.52", "gettext/gettext": "^4.8", "mck89/peast": "^1.13.11", "wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/scaffold-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "suggest": {"ext-mbstring": "Used for calculating include/exclude matches in code extraction"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["i18n", "i18n make-pot", "i18n make-json"]}, "autoload": {"files": ["i18n-command.php"], "psr-4": {"WP_CLI\\I18n\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://pascalbirchler.com/"}], "description": "Provides internationalization tools for WordPress projects.", "homepage": "https://github.com/wp-cli/i18n-command", "support": {"issues": "https://github.com/wp-cli/i18n-command/issues", "source": "https://github.com/wp-cli/i18n-command/tree/v2.3.0"}, "time": "2022-04-06T15:32:48+00:00"}, {"name": "wp-cli/import-command", "version": "v2.0.8", "source": {"type": "git", "url": "https://github.com/wp-cli/import-command.git", "reference": "a092e3abcca843f1fabf2e9b706a912ae075355f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/import-command/zipball/a092e3abcca843f1fabf2e9b706a912ae075355f", "reference": "a092e3abcca843f1fabf2e9b706a912ae075355f", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/export-command": "^1 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["import"]}, "autoload": {"files": ["import-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Imports content from a given WXR file.", "homepage": "https://github.com/wp-cli/import-command", "support": {"issues": "https://github.com/wp-cli/import-command/issues", "source": "https://github.com/wp-cli/import-command/tree/v2.0.8"}, "time": "2021-12-03T22:12:30+00:00"}, {"name": "wp-cli/language-command", "version": "v2.0.12", "source": {"type": "git", "url": "https://github.com/wp-cli/language-command.git", "reference": "bbdba69179fc8df597928587111500c8ade40a38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/language-command/zipball/bbdba69179fc8df597928587111500c8ade40a38", "reference": "bbdba69179fc8df597928587111500c8ade40a38", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["language", "language core", "language core activate", "language core is-installed", "language core install", "language core list", "language core uninstall", "language core update", "language plugin", "language plugin is-installed", "language plugin install", "language plugin list", "language plugin uninstall", "language plugin update", "language theme", "language theme is-installed", "language theme install", "language theme list", "language theme uninstall", "language theme update"]}, "autoload": {"files": ["language-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Installs, activates, and manages language packs.", "homepage": "https://github.com/wp-cli/language-command", "support": {"issues": "https://github.com/wp-cli/language-command/issues", "source": "https://github.com/wp-cli/language-command/tree/v2.0.12"}, "time": "2022-01-13T01:28:25+00:00"}, {"name": "wp-cli/maintenance-mode-command", "version": "v2.0.8", "source": {"type": "git", "url": "https://github.com/wp-cli/maintenance-mode-command.git", "reference": "e65505c973ea9349257a4f33ac9edc78db0b189a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/maintenance-mode-command/zipball/e65505c973ea9349257a4f33ac9edc78db0b189a", "reference": "e65505c973ea9349257a4f33ac9edc78db0b189a", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["maintenance-mode", "maintenance-mode activate", "maintenance-mode deactivate", "maintenance-mode status", "maintenance-mode is-active"]}, "autoload": {"files": ["maintenance-mode-command.php"], "psr-4": {"WP_CLI\\MaintenanceMode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://thrijith.com"}], "description": "Activates, deactivates or checks the status of the maintenance mode of a site.", "homepage": "https://github.com/wp-cli/maintenance-mode-command", "support": {"issues": "https://github.com/wp-cli/maintenance-mode-command/issues", "source": "https://github.com/wp-cli/maintenance-mode-command/tree/v2.0.8"}, "time": "2022-01-13T01:25:44+00:00"}, {"name": "wp-cli/media-command", "version": "v2.0.12", "source": {"type": "git", "url": "https://github.com/wp-cli/media-command.git", "reference": "7cbf32c7fa68631619f85a9bea0fb80fca119ba6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/media-command/zipball/7cbf32c7fa68631619f85a9bea0fb80fca119ba6", "reference": "7cbf32c7fa68631619f85a9bea0fb80fca119ba6", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^2.0", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["media", "media import", "media regenerate", "media image-size"]}, "autoload": {"files": ["media-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Imports files as attachments, regenerates thumbnails, or lists registered image sizes.", "homepage": "https://github.com/wp-cli/media-command", "support": {"issues": "https://github.com/wp-cli/media-command/issues", "source": "https://github.com/wp-cli/media-command/tree/v2.0.12"}, "time": "2021-12-06T16:13:51+00:00"}, {"name": "wp-cli/mustangostang-spyc", "version": "0.6.3", "source": {"type": "git", "url": "https://github.com/wp-cli/spyc.git", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/spyc/zipball/6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "reference": "6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "4.3.*@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.5.x-dev"}}, "autoload": {"files": ["includes/functions.php"], "psr-4": {"Mustangostang\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "mustangostang", "email": "<EMAIL>"}], "description": "A simple YAML loader/dumper class for PHP (WP-CLI fork)", "homepage": "https://github.com/mustangostang/spyc/", "support": {"source": "https://github.com/wp-cli/spyc/tree/autoload"}, "time": "2017-04-25T11:26:20+00:00"}, {"name": "wp-cli/package-command", "version": "v2.2.2", "source": {"type": "git", "url": "https://github.com/wp-cli/package-command.git", "reference": "36afdee21d022e6270867aa0cbfef6f453041814"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/package-command/zipball/36afdee21d022e6270867aa0cbfef6f453041814", "reference": "36afdee21d022e6270867aa0cbfef6f453041814", "shasum": ""}, "require": {"composer/composer": "^1.10.23 || ^2.1.9", "ext-json": "*", "wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/scaffold-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["package", "package browse", "package install", "package list", "package update", "package uninstall"]}, "autoload": {"files": ["package-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Lists, installs, and removes WP-CLI packages.", "homepage": "https://github.com/wp-cli/package-command", "support": {"issues": "https://github.com/wp-cli/package-command/issues", "source": "https://github.com/wp-cli/package-command/tree/v2.2.2"}, "time": "2022-01-13T01:28:18+00:00"}, {"name": "wp-cli/php-cli-tools", "version": "v0.11.13", "source": {"type": "git", "url": "https://github.com/wp-cli/php-cli-tools.git", "reference": "a2866855ac1abc53005c102e901553ad5772dc04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/php-cli-tools/zipball/a2866855ac1abc53005c102e901553ad5772dc04", "reference": "a2866855ac1abc53005c102e901553ad5772dc04", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "type": "library", "autoload": {"files": ["lib/cli/cli.php"], "psr-0": {"cli": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Console utilities for PHP", "homepage": "http://github.com/wp-cli/php-cli-tools", "keywords": ["cli", "console"], "support": {"issues": "https://github.com/wp-cli/php-cli-tools/issues", "source": "https://github.com/wp-cli/php-cli-tools/tree/v0.11.13"}, "time": "2021-07-01T15:08:16+00:00"}, {"name": "wp-cli/rewrite-command", "version": "v2.0.10", "source": {"type": "git", "url": "https://github.com/wp-cli/rewrite-command.git", "reference": "562a0a5a0d51be000de87d7a8a870de13383ecd6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/rewrite-command/zipball/562a0a5a0d51be000de87d7a8a870de13383ecd6", "reference": "562a0a5a0d51be000de87d7a8a870de13383ecd6", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["rewrite", "rewrite flush", "rewrite list", "rewrite structure"]}, "autoload": {"files": ["rewrite-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Lists or flushes the site's rewrite rules, updates the permalink structure.", "homepage": "https://github.com/wp-cli/rewrite-command", "support": {"issues": "https://github.com/wp-cli/rewrite-command/issues", "source": "https://github.com/wp-cli/rewrite-command/tree/v2.0.10"}, "time": "2022-01-13T01:28:11+00:00"}, {"name": "wp-cli/role-command", "version": "v2.0.9", "source": {"type": "git", "url": "https://github.com/wp-cli/role-command.git", "reference": "9abd93952565935084160bc3be49dfb2483bb0b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/role-command/zipball/9abd93952565935084160bc3be49dfb2483bb0b6", "reference": "9abd93952565935084160bc3be49dfb2483bb0b6", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["role", "role create", "role delete", "role exists", "role list", "role reset", "cap", "cap add", "cap list", "cap remove"]}, "autoload": {"files": ["role-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Adds, removes, lists, and resets roles and capabilities.", "homepage": "https://github.com/wp-cli/role-command", "support": {"issues": "https://github.com/wp-cli/role-command/issues", "source": "https://github.com/wp-cli/role-command/tree/v2.0.9"}, "time": "2022-01-13T01:31:23+00:00"}, {"name": "wp-cli/scaffold-command", "version": "v2.0.16", "source": {"type": "git", "url": "https://github.com/wp-cli/scaffold-command.git", "reference": "6d92fb363b8ed7473af7f12cf342aaf9d2c96e81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/scaffold-command/zipball/6d92fb363b8ed7473af7f12cf342aaf9d2c96e81", "reference": "6d92fb363b8ed7473af7f12cf342aaf9d2c96e81", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["scaffold", "scaffold underscores", "scaffold block", "scaffold child-theme", "scaffold plugin", "scaffold plugin-tests", "scaffold post-type", "scaffold taxonomy", "scaffold theme-tests"]}, "autoload": {"files": ["scaffold-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Generates code for post types, taxonomies, blocks, plugins, child themes, etc.", "homepage": "https://github.com/wp-cli/scaffold-command", "support": {"issues": "https://github.com/wp-cli/scaffold-command/issues", "source": "https://github.com/wp-cli/scaffold-command/tree/v2.0.16"}, "time": "2022-01-25T06:32:00+00:00"}, {"name": "wp-cli/search-replace-command", "version": "v2.0.16", "source": {"type": "git", "url": "https://github.com/wp-cli/search-replace-command.git", "reference": "dbf21560fd91710b2900f5631448657d28f2b380"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/search-replace-command/zipball/dbf21560fd91710b2900f5631448657d28f2b380", "reference": "dbf21560fd91710b2900f5631448657d28f2b380", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.3 || ^2", "wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "bundled": true, "commands": ["search-replace"]}, "autoload": {"files": ["search-replace-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Searches/replaces strings in the database.", "homepage": "https://github.com/wp-cli/search-replace-command", "support": {"issues": "https://github.com/wp-cli/search-replace-command/issues", "source": "https://github.com/wp-cli/search-replace-command/tree/v2.0.16"}, "time": "2021-12-13T22:48:28+00:00"}, {"name": "wp-cli/server-command", "version": "v2.0.10", "source": {"type": "git", "url": "https://github.com/wp-cli/server-command.git", "reference": "50c81f45f1cf09bc0a52e3582b3e56d27ca3c33c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/server-command/zipball/50c81f45f1cf09bc0a52e3582b3e56d27ca3c33c", "reference": "50c81f45f1cf09bc0a52e3582b3e56d27ca3c33c", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["server"]}, "autoload": {"files": ["server-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Launches PHP's built-in web server for a specific WordPress installation.", "homepage": "https://github.com/wp-cli/server-command", "support": {"issues": "https://github.com/wp-cli/server-command/issues", "source": "https://github.com/wp-cli/server-command/tree/v2.0.10"}, "time": "2022-01-13T01:34:09+00:00"}, {"name": "wp-cli/shell-command", "version": "v2.0.11", "source": {"type": "git", "url": "https://github.com/wp-cli/shell-command.git", "reference": "28a7de3134c9f059900d8fa4aea1d7d618481454"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/shell-command/zipball/28a7de3134c9f059900d8fa4aea1d7d618481454", "reference": "28a7de3134c9f059900d8fa4aea1d7d618481454", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["shell"]}, "autoload": {"files": ["shell-command.php"], "psr-4": {"": "src/", "WP_CLI\\": "src/WP_CLI"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Opens an interactive PHP console for running and testing PHP code.", "homepage": "https://github.com/wp-cli/shell-command", "support": {"issues": "https://github.com/wp-cli/shell-command/issues", "source": "https://github.com/wp-cli/shell-command/tree/v2.0.11"}, "time": "2022-01-13T01:34:02+00:00"}, {"name": "wp-cli/super-admin-command", "version": "v2.0.10", "source": {"type": "git", "url": "https://github.com/wp-cli/super-admin-command.git", "reference": "e6707f3acfff089d19c5c55bba0fd66cd7d6c2fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/super-admin-command/zipball/e6707f3acfff089d19c5c55bba0fd66cd7d6c2fa", "reference": "e6707f3acfff089d19c5c55bba0fd66cd7d6c2fa", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/entity-command": "^1.3 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["super-admin", "super-admin add", "super-admin list", "super-admin remove"]}, "autoload": {"files": ["super-admin-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Lists, adds, or removes super admin users on a multisite installation.", "homepage": "https://github.com/wp-cli/super-admin-command", "support": {"issues": "https://github.com/wp-cli/super-admin-command/issues", "source": "https://github.com/wp-cli/super-admin-command/tree/v2.0.10"}, "time": "2022-01-13T01:40:54+00:00"}, {"name": "wp-cli/widget-command", "version": "v2.1.7", "source": {"type": "git", "url": "https://github.com/wp-cli/widget-command.git", "reference": "6aedab77f1cd2a0f511b62110c244c32b84dd429"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/widget-command/zipball/6aedab77f1cd2a0f511b62110c244c32b84dd429", "reference": "6aedab77f1cd2a0f511b62110c244c32b84dd429", "shasum": ""}, "require": {"wp-cli/wp-cli": "^2.5"}, "require-dev": {"wp-cli/extension-command": "^1.2 || ^2", "wp-cli/wp-cli-tests": "^3.1"}, "type": "wp-cli-package", "extra": {"branch-alias": {"dev-main": "2.x-dev"}, "bundled": true, "commands": ["widget", "widget add", "widget deactivate", "widget delete", "widget list", "widget move", "widget reset", "widget update", "sidebar", "sidebar list"]}, "autoload": {"files": ["widget-command.php"], "psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://runcommand.io"}], "description": "Adds, moves, and removes widgets; lists sidebars.", "homepage": "https://github.com/wp-cli/widget-command", "support": {"issues": "https://github.com/wp-cli/widget-command/issues", "source": "https://github.com/wp-cli/widget-command/tree/v2.1.7"}, "time": "2022-01-13T01:41:02+00:00"}, {"name": "wp-cli/wp-cli", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/wp-cli/wp-cli.git", "reference": "dee13c2baf6bf972484a63f8b8dab48f7220f095"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/wp-cli/zipball/dee13c2baf6bf972484a63f8b8dab48f7220f095", "reference": "dee13c2baf6bf972484a63f8b8dab48f7220f095", "shasum": ""}, "require": {"ext-curl": "*", "mustache/mustache": "^2.14.1", "php": "^5.6 || ^7.0 || ^8.0", "rmccue/requests": "^1.8", "symfony/finder": ">2.7", "wp-cli/mustangostang-spyc": "^0.6.3", "wp-cli/php-cli-tools": "~0.11.2"}, "require-dev": {"roave/security-advisories": "dev-latest", "wp-cli/db-command": "^1.3 || ^2", "wp-cli/entity-command": "^1.2 || ^2", "wp-cli/extension-command": "^1.1 || ^2", "wp-cli/package-command": "^1 || ^2", "wp-cli/wp-cli-tests": "^3.1.3"}, "suggest": {"ext-readline": "Include for a better --prompt implementation", "ext-zip": "Needed to support extraction of ZIP archives when doing downloads or updates"}, "bin": ["bin/wp", "bin/wp.bat"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6.x-dev"}}, "autoload": {"psr-0": {"WP_CLI\\": "php/"}, "classmap": ["php/class-wp-cli.php", "php/class-wp-cli-command.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WP-CLI framework", "homepage": "https://wp-cli.org", "keywords": ["cli", "wordpress"], "support": {"docs": "https://make.wordpress.org/cli/handbook/", "issues": "https://github.com/wp-cli/wp-cli/issues", "source": "https://github.com/wp-cli/wp-cli"}, "time": "2022-01-25T16:31:27+00:00"}, {"name": "wp-cli/wp-cli-bundle", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/wp-cli/wp-cli-bundle.git", "reference": "50c984247925e68e314611dd47ed00e5bc7b3a10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/wp-cli-bundle/zipball/50c984247925e68e314611dd47ed00e5bc7b3a10", "reference": "50c984247925e68e314611dd47ed00e5bc7b3a10", "shasum": ""}, "require": {"composer/composer": "^1.10.23 || ^2.2.3", "php": ">=5.6", "wp-cli/cache-command": "^2", "wp-cli/checksum-command": "^2.1", "wp-cli/config-command": "^2.1", "wp-cli/core-command": "^2.1", "wp-cli/cron-command": "^2", "wp-cli/db-command": "^2", "wp-cli/embed-command": "^2", "wp-cli/entity-command": "^2", "wp-cli/eval-command": "^2", "wp-cli/export-command": "^2", "wp-cli/extension-command": "^2.1", "wp-cli/i18n-command": "^2", "wp-cli/import-command": "^2", "wp-cli/language-command": "^2", "wp-cli/maintenance-mode-command": "^2", "wp-cli/media-command": "^2", "wp-cli/package-command": "^2.1", "wp-cli/rewrite-command": "^2", "wp-cli/role-command": "^2", "wp-cli/scaffold-command": "^2", "wp-cli/search-replace-command": "^2", "wp-cli/server-command": "^2", "wp-cli/shell-command": "^2", "wp-cli/super-admin-command": "^2", "wp-cli/widget-command": "^2", "wp-cli/wp-cli": "^2.6"}, "require-dev": {"roave/security-advisories": "dev-latest", "wp-cli/wp-cli-tests": "^3.0.7"}, "suggest": {"psy/psysh": "Enhanced `wp shell` functionality"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.6.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "WP-CLI bundle package with default commands.", "homepage": "https://wp-cli.org", "keywords": ["cli", "wordpress"], "support": {"docs": "https://make.wordpress.org/cli/handbook/", "issues": "https://github.com/wp-cli/wp-cli-bundle/issues", "source": "https://github.com/wp-cli/wp-cli-bundle"}, "time": "2022-01-26T00:03:43+00:00"}, {"name": "wp-cli/wp-config-transformer", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/wp-cli/wp-config-transformer.git", "reference": "2e90eefc6b8f5166f53aa5414fd8f1a572164ef1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-cli/wp-config-transformer/zipball/2e90eefc6b8f5166f53aa5414fd8f1a572164ef1", "reference": "2e90eefc6b8f5166f53aa5414fd8f1a572164ef1", "shasum": ""}, "require": {"php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"wp-cli/wp-cli-tests": "^3.1"}, "type": "library", "autoload": {"files": ["src/WPConfigTransformer.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Programmatically edit a wp-config.php file.", "homepage": "https://github.com/wp-cli/wp-config-transformer", "support": {"issues": "https://github.com/wp-cli/wp-config-transformer/issues", "source": "https://github.com/wp-cli/wp-config-transformer/tree/v1.3.0"}, "time": "2022-01-10T18:37:52+00:00"}, {"name": "wp-coding-standards/wpcs", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "7da1894633f168fe244afc6de00d141f27517b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/7da1894633f168fe244afc6de00d141f27517b62", "reference": "7da1894633f168fe244afc6de00d141f27517b62", "shasum": ""}, "require": {"php": ">=5.4", "squizlabs/php_codesniffer": "^3.3.1"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || ^0.6", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "wordpress"], "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}, "time": "2020-05-13T23:57:56+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "platform-overrides": {"php": "7.4"}, "plugin-api-version": "2.3.0"}