{"name": "saintdesign/headless-core", "description": "The core WordPress plugin for the Next.js WordPress Starter.", "homepage": "https://gitlab.com/SaintDesign1/headless-core", "type": "wordpress-plugin", "license": "GPL-2.0-or-later", "authors": [{"name": "SaintDesign", "email": "<EMAIL>"}], "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}, "platform": {"php": "7.4"}}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "phpcompatibility/phpcompatibility-wp": "^2.1", "wp-cli/wp-cli-bundle": "^2.6", "wp-coding-standards/wpcs": "^2.3"}, "scripts": {"format": "./vendor/bin/phpcbf --standard=.phpcs.xml --report=summary,source", "lint": "./vendor/bin/phpcs --standard=.phpcs.xml --report=summary,source"}}