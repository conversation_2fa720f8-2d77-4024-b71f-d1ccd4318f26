sudo: false
dist: trusty

language: php

notifications:
  email:
    on_success: never
    on_failure: change

branches:
  only:
    - master

cache:
  directories:
    - vendor
    - $HOME/.composer/cache

matrix:
  include:
    - php: 7.3
      env: WP_VERSION=latest
    - php: 7.2
      env: WP_VERSION=latest
    - php: 7.1
      env: WP_VERSION=latest
    - php: 7.0
      env: WP_VERSION=latest
    - php: 7.0
      env: WP_VERSION=trunk
  allow_failures:
    - env: WP_TRAVISCI=phpcs

install:
  - |
    cd $TRAVIS_BUILD_DIR
    curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar
    chmod +x wp-cli.phar
    sudo mv wp-cli.phar /usr/local/bin/wp

before_script:
  - export PATH="$HOME/.composer/vendor/bin:$PATH"
  - |
    if [[ ! -z "$WP_VERSION" ]]; then
       cp .env.dist .env
      composer install-wp-tests
      COMPOSER_MEMORY_LIMIT=-1 travis_retry composer install --prefer-source --no-interaction
    fi
  - |
    if [[ "$WP_TRAVISCI" == "phpcs" ]]; then
      COMPOSER_MEMORY_LIMIT=-1 travis_retry composer require \
        squizlabs/php_codesniffer \
        phpcompatibility/phpcompatibility-wp wp-coding-standards/wpcs \
        dealerdirect/phpcodesniffer-composer-installer
      COMPOSER_MEMORY_LIMIT=-1 travis_retry composer install --no-dev
    fi

script:
  - |
    if [[ ! -z "$WP_VERSION" ]]; then
      vendor/bin/codecept run wpunit
    fi
  - |
    if [[ "$WP_TRAVISCI" == "phpcs" ]]; then
      vendor/bin/phpcs \
        wp-graphql-acf.php \
        src/*.php --standard=WordPress
    fi
after_success:
  # Download and run Coveralls.io client
  - |
    if [ "$COVERAGE" == "1" ]; then
      wget https://github.com/php-coveralls/php-coveralls/releases/download/v2.1.0/php-coveralls.phar
      chmod +x php-coveralls.phar

      travis_retry php php-coveralls.phar -v
    fi
