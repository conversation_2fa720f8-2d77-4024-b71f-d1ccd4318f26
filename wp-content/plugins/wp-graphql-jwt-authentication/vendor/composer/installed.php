<?php return array(
    'root' => array(
        'pretty_version' => 'dev-develop',
        'version' => 'dev-develop',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '44f7e1d50852749f3af1b4ef2a06ef4702ae4994',
        'name' => 'wp-graphql/wp-graphql-jwt-authentication',
        'dev' => false,
    ),
    'versions' => array(
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.1.0',
            'version' => '6.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'reference' => 'fbb2967a3a68b07e37678c00c0cf51165051495f',
            'dev_requirement' => false,
        ),
        'wp-graphql/wp-graphql-jwt-authentication' => array(
            'pretty_version' => 'dev-develop',
            'version' => 'dev-develop',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '44f7e1d50852749f3af1b4ef2a06ef4702ae4994',
            'dev_requirement' => false,
        ),
    ),
);
