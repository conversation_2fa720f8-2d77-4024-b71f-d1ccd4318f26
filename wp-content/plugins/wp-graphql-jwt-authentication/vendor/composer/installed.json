{"packages": [{"name": "firebase/php-jwt", "version": "v6.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "fbb2967a3a68b07e37678c00c0cf51165051495f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/fbb2967a3a68b07e37678c00c0cf51165051495f", "reference": "fbb2967a3a68b07e37678c00c0cf51165051495f", "shasum": ""}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"phpunit/phpunit": "^7.5||9.5"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "time": "2022-03-23T18:26:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.1.0"}, "install-path": "../firebase/php-jwt"}], "dev": false, "dev-package-names": []}