{"name": "firebase/php-jwt", "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["php", "jwt"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "^7.1||^8.0"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "require-dev": {"phpunit/phpunit": "^7.5||9.5"}}