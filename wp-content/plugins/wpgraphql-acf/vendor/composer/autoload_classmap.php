<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Appsero\\Client' => $vendorDir . '/appsero/client/src/Client.php',
    'Appsero\\Insights' => $vendorDir . '/appsero/client/src/Insights.php',
    'Appsero\\License' => $vendorDir . '/appsero/client/src/License.php',
    'Appsero\\Updater' => $vendorDir . '/appsero/client/src/Updater.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'WPGraphQL\\Acf\\AcfGraphQLFieldResolver' => $baseDir . '/src/AcfGraphQLFieldResolver.php',
    'WPGraphQL\\Acf\\AcfGraphQLFieldType' => $baseDir . '/src/AcfGraphQLFieldType.php',
    'WPGraphQL\\Acf\\Admin\\OptionsPageRegistration' => $baseDir . '/src/Admin/OptionsPageRegistration.php',
    'WPGraphQL\\Acf\\Admin\\PostTypeRegistration' => $baseDir . '/src/Admin/PostTypeRegistration.php',
    'WPGraphQL\\Acf\\Admin\\Settings' => $baseDir . '/src/Admin/Settings.php',
    'WPGraphQL\\Acf\\Admin\\TaxonomyRegistration' => $baseDir . '/src/Admin/TaxonomyRegistration.php',
    'WPGraphQL\\Acf\\Data\\Loader\\AcfOptionsPageLoader' => $baseDir . '/src/Data/Loader/AcfOptionsPageLoader.php',
    'WPGraphQL\\Acf\\FieldConfig' => $baseDir . '/src/FieldConfig.php',
    'WPGraphQL\\Acf\\FieldTypeRegistry' => $baseDir . '/src/FieldTypeRegistry.php',
    'WPGraphQL\\Acf\\FieldType\\ButtonGroup' => $baseDir . '/src/FieldType/ButtonGroup.php',
    'WPGraphQL\\Acf\\FieldType\\Checkbox' => $baseDir . '/src/FieldType/Checkbox.php',
    'WPGraphQL\\Acf\\FieldType\\CloneField' => $baseDir . '/src/FieldType/CloneField.php',
    'WPGraphQL\\Acf\\FieldType\\ColorPicker' => $baseDir . '/src/FieldType/ColorPicker.php',
    'WPGraphQL\\Acf\\FieldType\\DatePicker' => $baseDir . '/src/FieldType/DatePicker.php',
    'WPGraphQL\\Acf\\FieldType\\DateTimePicker' => $baseDir . '/src/FieldType/DateTimePicker.php',
    'WPGraphQL\\Acf\\FieldType\\Email' => $baseDir . '/src/FieldType/Email.php',
    'WPGraphQL\\Acf\\FieldType\\File' => $baseDir . '/src/FieldType/File.php',
    'WPGraphQL\\Acf\\FieldType\\FlexibleContent' => $baseDir . '/src/FieldType/FlexibleContent.php',
    'WPGraphQL\\Acf\\FieldType\\Gallery' => $baseDir . '/src/FieldType/Gallery.php',
    'WPGraphQL\\Acf\\FieldType\\GoogleMap' => $baseDir . '/src/FieldType/GoogleMap.php',
    'WPGraphQL\\Acf\\FieldType\\Group' => $baseDir . '/src/FieldType/Group.php',
    'WPGraphQL\\Acf\\FieldType\\Image' => $baseDir . '/src/FieldType/Image.php',
    'WPGraphQL\\Acf\\FieldType\\Link' => $baseDir . '/src/FieldType/Link.php',
    'WPGraphQL\\Acf\\FieldType\\Number' => $baseDir . '/src/FieldType/Number.php',
    'WPGraphQL\\Acf\\FieldType\\Oembed' => $baseDir . '/src/FieldType/Oembed.php',
    'WPGraphQL\\Acf\\FieldType\\PageLink' => $baseDir . '/src/FieldType/PageLink.php',
    'WPGraphQL\\Acf\\FieldType\\Password' => $baseDir . '/src/FieldType/Password.php',
    'WPGraphQL\\Acf\\FieldType\\PostObject' => $baseDir . '/src/FieldType/PostObject.php',
    'WPGraphQL\\Acf\\FieldType\\Radio' => $baseDir . '/src/FieldType/Radio.php',
    'WPGraphQL\\Acf\\FieldType\\Range' => $baseDir . '/src/FieldType/Range.php',
    'WPGraphQL\\Acf\\FieldType\\Relationship' => $baseDir . '/src/FieldType/Relationship.php',
    'WPGraphQL\\Acf\\FieldType\\Repeater' => $baseDir . '/src/FieldType/Repeater.php',
    'WPGraphQL\\Acf\\FieldType\\Select' => $baseDir . '/src/FieldType/Select.php',
    'WPGraphQL\\Acf\\FieldType\\Taxonomy' => $baseDir . '/src/FieldType/Taxonomy.php',
    'WPGraphQL\\Acf\\FieldType\\Text' => $baseDir . '/src/FieldType/Text.php',
    'WPGraphQL\\Acf\\FieldType\\Textarea' => $baseDir . '/src/FieldType/Textarea.php',
    'WPGraphQL\\Acf\\FieldType\\TimePicker' => $baseDir . '/src/FieldType/TimePicker.php',
    'WPGraphQL\\Acf\\FieldType\\TrueFalse' => $baseDir . '/src/FieldType/TrueFalse.php',
    'WPGraphQL\\Acf\\FieldType\\Url' => $baseDir . '/src/FieldType/Url.php',
    'WPGraphQL\\Acf\\FieldType\\User' => $baseDir . '/src/FieldType/User.php',
    'WPGraphQL\\Acf\\FieldType\\Wysiwyg' => $baseDir . '/src/FieldType/Wysiwyg.php',
    'WPGraphQL\\Acf\\LocationRules\\LocationRules' => $baseDir . '/src/LocationRules/LocationRules.php',
    'WPGraphQL\\Acf\\Model\\AcfOptionsPage' => $baseDir . '/src/Model/AcfOptionsPage.php',
    'WPGraphQL\\Acf\\Registry' => $baseDir . '/src/Registry.php',
    'WPGraphQL\\Acf\\ThirdParty' => $baseDir . '/src/ThirdParty.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\AcfExtended' => $baseDir . '/src/ThirdParty/AcfExtended/AcfExtended.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeAdvancedLink' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeAdvancedLink.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeCodeEditor' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeCodeEditor.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeCountries' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeCountries.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeCurrencies' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeCurrencies.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeDateRangePicker' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeDateRangePicker.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeImageSelector' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeImageSelector.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeImageSizes' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeImageSizes.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeLanguages' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeLanguages.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeMenuLocations' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeMenuLocations.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeMenus' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeMenus.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfePhoneNumber' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfePhoneNumber.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfePostFormats' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfePostFormats.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeTaxonomies' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeTaxonomies.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeTaxonomyTerms' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeTaxonomyTerms.php',
    'WPGraphQL\\Acf\\ThirdParty\\AcfExtended\\FieldType\\AcfeUserRoles' => $baseDir . '/src/ThirdParty/AcfExtended/FieldType/AcfeUserRoles.php',
    'WPGraphQL\\Acf\\ThirdParty\\WPGraphQLContentBlocks\\WPGraphQLContentBlocks' => $baseDir . '/src/ThirdParty/WPGraphQLContentBlocks/WPGraphQLContentBlocks.php',
    'WPGraphQL\\Acf\\ThirdParty\\WPGraphQLSmartCache\\WPGraphQLSmartCache' => $baseDir . '/src/ThirdParty/WPGraphQLSmartCache/WPGraphQLSmartCache.php',
    'WPGraphQL\\Acf\\Utils' => $baseDir . '/src/Utils.php',
);
