(()=>{var e={4146:(e,t,n)=>{"use strict";var r=n(3404),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function c(e){return r.isMemo(e)?a:s[e.$$typeof]||o}s[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[r.Memo]=a;var l=Object.defineProperty,u=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!=typeof n){if(h){var o=p(n);o&&o!==h&&e(t,o,r)}var a=u(n);f&&(a=a.concat(f(n)));for(var s=c(t),m=c(n),g=0;g<a.length;++g){var v=a[g];if(!(i[v]||r&&r[v]||m&&m[v]||s&&s[v])){var b=d(n,v);try{l(t,v,b)}catch(e){}}}}return t}},3072:(e,t)=>{"use strict";var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,a=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,c=n?Symbol.for("react.provider"):60109,l=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,f=n?Symbol.for("react.concurrent_mode"):60111,d=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,b=n?Symbol.for("react.fundamental"):60117,y=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function x(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case f:case i:case s:case a:case p:return e;default:switch(e=e&&e.$$typeof){case l:case d:case g:case m:case c:return e;default:return t}}case o:return t}}}function S(e){return x(e)===f}t.AsyncMode=u,t.ConcurrentMode=f,t.ContextConsumer=l,t.ContextProvider=c,t.Element=r,t.ForwardRef=d,t.Fragment=i,t.Lazy=g,t.Memo=m,t.Portal=o,t.Profiler=s,t.StrictMode=a,t.Suspense=p,t.isAsyncMode=function(e){return S(e)||x(e)===u},t.isConcurrentMode=S,t.isContextConsumer=function(e){return x(e)===l},t.isContextProvider=function(e){return x(e)===c},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return x(e)===d},t.isFragment=function(e){return x(e)===i},t.isLazy=function(e){return x(e)===g},t.isMemo=function(e){return x(e)===m},t.isPortal=function(e){return x(e)===o},t.isProfiler=function(e){return x(e)===s},t.isStrictMode=function(e){return x(e)===a},t.isSuspense=function(e){return x(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===f||e===s||e===a||e===p||e===h||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===c||e.$$typeof===l||e.$$typeof===d||e.$$typeof===b||e.$$typeof===y||e.$$typeof===w||e.$$typeof===v)},t.typeOf=x},3404:(e,t,n)=>{"use strict";e.exports=n(3072)},2799:(e,t)=>{"use strict";var n,r=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case i:case s:case a:case d:case p:return e;default:switch(e=e&&e.$$typeof){case u:case l:case f:case m:case h:case c:return e;default:return t}}case o:return t}}}n=Symbol.for("react.module.reference"),t.ForwardRef=f,t.isFragment=function(e){return v(e)===i},t.isMemo=function(e){return v(e)===h},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===s||e===a||e===d||e===p||e===g||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===h||e.$$typeof===c||e.$$typeof===l||e.$$typeof===f||e.$$typeof===n||void 0!==e.getModuleId)},t.typeOf=v},4363:(e,t,n)=>{"use strict";e.exports=n(2799)},2833:e=>{e.exports=function(e,t,n,r){var o=n?n.call(r,e,t):void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var i=Object.keys(e),a=Object.keys(t);if(i.length!==a.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(t),c=0;c<i.length;c++){var l=i[c];if(!s(l))return!1;var u=e[l],f=t[l];if(!1===(o=n?n.call(r,u,f,l):void 0)||void 0===o&&u!==f)return!1}return!0}},6942:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=a(e,i(n)))}return e}function i(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=a(t,n));return t}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React;var t=n.n(e);const r=window.wp.element,{hooks:o}=wpGraphiQL,i=(0,r.createContext)(),a=()=>(0,r.useContext)(i),s=({children:t})=>{const[n,a]=(0,r.useState)((()=>{const e=window?.localStorage.getItem("graphiql:usePublicFetcher");return!(e&&"false"===e)})()),s=o.applyFilters("graphiql_auth_switch_context_default_value",{usePublicFetcher:n,setUsePublicFetcher:a,toggleUsePublicFetcher:()=>{const e=!n;window.localStorage.setItem("graphiql:usePublicFetcher",e.toString()),a(e)}});return(0,e.createElement)(i.Provider,{value:s},t)};var c=n(6942),l=n.n(c);function u(t){var n=t.children,r=t.prefixCls,o=t.id,i=t.overlayInnerStyle,a=t.className,s=t.style;return e.createElement("div",{className:l()("".concat(r,"-content"),a),style:s},e.createElement("div",{className:"".concat(r,"-inner"),id:o,role:"tooltip",style:i},"function"==typeof n?n():n))}function f(){return f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},f.apply(this,arguments)}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function p(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==d(t)?t:t+""}function h(e,t,n){return(t=p(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function v(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function y(e,t){if(e){if("string"==typeof e)return b(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?b(e,t):void 0}}function w(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return s}}(e,t)||y(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}const x=window.ReactDOM;var S=n.n(x);function k(){return!("undefined"==typeof window||!window.document||!window.document.createElement)}var C={},O=[];function E(e,t){}function $(e,t){}function A(e,t,n){t||C[n]||(e(!1,n),C[n]=!0)}function j(e,t){A(E,e,t)}j.preMessage=function(e){O.push(e)},j.resetWarned=function(){C={}},j.noteOnce=function(e,t){A($,e,t)};const M=j;var P=n(4363);function R(e,t){"function"==typeof e?e(t):"object"===d(e)&&e&&"current"in e&&(e.current=t)}function T(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.filter((function(e){return e}));return r.length<=1?r[0]:function(e){t.forEach((function(t){R(t,e)}))}}function z(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return o=n,"value"in(i=e.useRef({})).current&&!function(e,t){return e.length!==t.length||e.every((function(e,n){return e!==t[n]}))}(i.current.condition,o)||(i.current.value=T.apply(void 0,n),i.current.condition=o),i.current.value;var o,i}function _(e){var t,n,r=(0,P.isMemo)(e)?e.type.type:e.type;return!!("function"!=typeof r||null!==(t=r.prototype)&&void 0!==t&&t.render||r.$$typeof===P.ForwardRef)&&!!("function"!=typeof e||null!==(n=e.prototype)&&void 0!==n&&n.render||e.$$typeof===P.ForwardRef)}const N=e.createContext(null);function I(e){return function(e){if(Array.isArray(e))return b(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||y(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var L=k()?e.useLayoutEffect:e.useEffect,H=function(t,n){var r=e.useRef(!0);L((function(){return t(r.current)}),n),L((function(){return r.current=!1,function(){r.current=!0}}),[])},B=function(e,t){H((function(t){if(!t)return e()}),t)};const F=H;var D=[],W="data-rc-order",X="data-rc-priority",q="rc-util-key",V=new Map;function G(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).mark;return e?e.startsWith("data-")?e:"data-".concat(e):q}function Y(e){return e.attachTo?e.attachTo:document.querySelector("head")||document.body}function U(e){return Array.from((V.get(e)||e).children).filter((function(e){return"STYLE"===e.tagName}))}function Z(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!k())return null;var n=t.csp,r=t.prepend,o=t.priority,i=void 0===o?0:o,a=function(e){return"queue"===e?"prependQueue":e?"prepend":"append"}(r),s="prependQueue"===a,c=document.createElement("style");c.setAttribute(W,a),s&&i&&c.setAttribute(X,"".concat(i)),null!=n&&n.nonce&&(c.nonce=null==n?void 0:n.nonce),c.innerHTML=e;var l=Y(t),u=l.firstChild;if(r){if(s){var f=(t.styles||U(l)).filter((function(e){if(!["prepend","prependQueue"].includes(e.getAttribute(W)))return!1;var t=Number(e.getAttribute(X)||0);return i>=t}));if(f.length)return l.insertBefore(c,f[f.length-1].nextSibling),c}l.insertBefore(c,u)}else l.appendChild(c);return c}function K(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=Y(t);return(t.styles||U(n)).find((function(n){return n.getAttribute(G(t))===e}))}function Q(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=K(e,t);n&&Y(t).removeChild(n)}function J(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=Y(n),o=U(r),i=g(g({},n),{},{styles:o});!function(e,t){var n=V.get(e);if(!n||!function(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var n=t;n;){if(n===e)return!0;n=n.parentNode}return!1}(document,n)){var r=Z("",t),o=r.parentNode;V.set(e,o),e.removeChild(r)}}(r,i);var a,s,c,l=K(t,i);if(l)return null!==(a=i.csp)&&void 0!==a&&a.nonce&&l.nonce!==(null===(s=i.csp)||void 0===s?void 0:s.nonce)&&(l.nonce=null===(c=i.csp)||void 0===c?void 0:c.nonce),l.innerHTML!==e&&(l.innerHTML=e),l;var u=Z(e,i);return u.setAttribute(G(i),t),u}var ee="rc-util-locker-".concat(Date.now()),te=0;function ne(t){var n=!!t,r=w(e.useState((function(){return te+=1,"".concat(ee,"_").concat(te)})),1)[0];F((function(){if(n){var e=(o=document.body,"undefined"!=typeof document&&o&&o instanceof Element?function(e){var t="rc-scrollbar-measure-".concat(Math.random().toString(36).substring(7)),n=document.createElement("div");n.id=t;var r,o,i=n.style;if(i.position="absolute",i.left="0",i.top="0",i.width="100px",i.height="100px",i.overflow="scroll",e){var a=getComputedStyle(e);i.scrollbarColor=a.scrollbarColor,i.scrollbarWidth=a.scrollbarWidth;var s=getComputedStyle(e,"::-webkit-scrollbar"),c=parseInt(s.width,10),l=parseInt(s.height,10);try{var u=c?"width: ".concat(s.width,";"):"",f=l?"height: ".concat(s.height,";"):"";J("\n#".concat(t,"::-webkit-scrollbar {\n").concat(u,"\n").concat(f,"\n}"),t)}catch(e){console.error(e),r=c,o=l}}document.body.appendChild(n);var d=e&&r&&!isNaN(r)?r:n.offsetWidth-n.clientWidth,p=e&&o&&!isNaN(o)?o:n.offsetHeight-n.clientHeight;return document.body.removeChild(n),Q(t),{width:d,height:p}}(o):{width:0,height:0}).width,t=document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth;J("\nhtml body {\n  overflow-y: hidden;\n  ".concat(t?"width: calc(100% - ".concat(e,"px);"):"","\n}"),r)}else Q(r);var o;return function(){Q(r)}}),[n,r])}var re=!1,oe=function(e){return!1!==e&&(k()&&e?"string"==typeof e?document.querySelector(e):"function"==typeof e?e():e:null)},ie=e.forwardRef((function(t,n){var r=t.open,o=t.autoLock,i=t.getContainer,a=(t.debug,t.autoDestroy),s=void 0===a||a,c=t.children,l=w(e.useState(r),2),u=l[0],f=l[1],d=u||r;e.useEffect((function(){(s||r)&&f(r)}),[r,s]);var p=w(e.useState((function(){return oe(i)})),2),h=p[0],m=p[1];e.useEffect((function(){var e=oe(i);m(null!=e?e:null)}));var g=function(t,n){var r=w(e.useState((function(){return k()?document.createElement("div"):null})),1)[0],o=e.useRef(!1),i=e.useContext(N),a=w(e.useState(D),2),s=a[0],c=a[1],l=i||(o.current?void 0:function(e){c((function(t){return[e].concat(I(t))}))});function u(){r.parentElement||document.body.appendChild(r),o.current=!0}function f(){var e;null===(e=r.parentElement)||void 0===e||e.removeChild(r),o.current=!1}return F((function(){return t?i?i(u):u():f(),f}),[t]),F((function(){s.length&&(s.forEach((function(e){return e()})),c(D))}),[s]),[r,l]}(d&&!h),v=w(g,2),b=v[0],y=v[1],S=null!=h?h:b;ne(o&&r&&k()&&(S===b||S===document.body));var C=null;c&&_(c)&&n&&(C=c.ref);var O=z(C,n);if(!d||!k()||void 0===h)return null;var E=!1===S||re,$=c;return n&&($=e.cloneElement(c,{ref:O})),e.createElement(N.Provider,{value:y},E?$:(0,x.createPortal)($,S))}));const ae=ie;function se(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=[];return t().Children.forEach(e,(function(e){(null!=e||n.keepEmpty)&&(Array.isArray(e)?r=r.concat(se(e)):(0,P.isFragment)(e)&&e.props?r=r.concat(se(e.props.children,n)):r.push(e))})),r}function ce(e){return e instanceof HTMLElement||e instanceof SVGElement}function le(e){return ce(e)?e:e instanceof t().Component?S().findDOMNode(e):null}var ue=e.createContext(null),fe=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some((function(e,r){return e[0]===t&&(n=r,!0)})),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var o=r[n];e.call(t,o[1],o[0])}},t}()}(),de="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,pe=void 0!==n.g&&n.g.Math===Math?n.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),he="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(pe):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)},me=["top","right","bottom","left","width","height","size","weight"],ge="undefined"!=typeof MutationObserver,ve=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var n=!1,r=!1,o=0;function i(){n&&(n=!1,e()),r&&s()}function a(){he(i)}function s(){var e=Date.now();if(n){if(e-o<2)return;r=!0}else n=!0,r=!1,setTimeout(a,t);o=e}return s}(this.refresh.bind(this),20)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){de&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),ge?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){de&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;me.some((function(e){return!!~n.indexOf(e)}))&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),be=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var o=r[n];Object.defineProperty(e,o,{value:t[o],enumerable:!1,writable:!1,configurable:!0})}return e},ye=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||pe},we=Oe(0,0,0,0);function xe(e){return parseFloat(e)||0}function Se(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){return t+xe(e["border-"+n+"-width"])}),0)}var ke="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof ye(e).SVGGraphicsElement}:function(e){return e instanceof ye(e).SVGElement&&"function"==typeof e.getBBox};function Ce(e){return de?ke(e)?function(e){var t=e.getBBox();return Oe(0,0,t.width,t.height)}(e):function(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return we;var r=ye(e).getComputedStyle(e),o=function(e){for(var t={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var o=r[n],i=e["padding-"+o];t[o]=xe(i)}return t}(r),i=o.left+o.right,a=o.top+o.bottom,s=xe(r.width),c=xe(r.height);if("border-box"===r.boxSizing&&(Math.round(s+i)!==t&&(s-=Se(r,"left","right")+i),Math.round(c+a)!==n&&(c-=Se(r,"top","bottom")+a)),!function(e){return e===ye(e).document.documentElement}(e)){var l=Math.round(s+i)-t,u=Math.round(c+a)-n;1!==Math.abs(l)&&(s-=l),1!==Math.abs(u)&&(c-=u)}return Oe(o.left,o.top,s,c)}(e):we}function Oe(e,t,n,r){return{x:e,y:t,width:n,height:r}}var Ee=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Oe(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=Ce(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),$e=function(e,t){var n=function(e){var t=e.x,n=e.y,r=e.width,o=e.height,i="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,a=Object.create(i.prototype);return be(a,{x:t,y:n,width:r,height:o,top:n,right:t+r,bottom:o+n,left:t}),a}(t);be(this,{target:e,contentRect:n})},Ae=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new fe,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof ye(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new Ee(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof ye(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new $e(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),je="undefined"!=typeof WeakMap?new WeakMap:new fe,Me=function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=ve.getInstance(),r=new Ae(t,n,this);je.set(this,r)};["observe","unobserve","disconnect"].forEach((function(e){Me.prototype[e]=function(){var t;return(t=je.get(this))[e].apply(t,arguments)}}));const Pe=void 0!==pe.ResizeObserver?pe.ResizeObserver:Me;var Re=new Map,Te=new Pe((function(e){e.forEach((function(e){var t,n=e.target;null===(t=Re.get(n))||void 0===t||t.forEach((function(e){return e(n)}))}))}));function ze(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,p(r.key),r)}}function Ne(e,t,n){return t&&_e(e.prototype,t),n&&_e(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ie(e,t){return Ie=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ie(e,t)}function Le(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ie(e,t)}function He(e){return He=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},He(e)}function Be(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Be=function(){return!!e})()}function Fe(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function De(e,t){if(t&&("object"===d(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Fe(e)}function We(e){var t=Be();return function(){var n,r=He(e);if(t){var o=He(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return De(this,n)}}var Xe=function(e){Le(n,e);var t=We(n);function n(){return ze(this,n),t.apply(this,arguments)}return Ne(n,[{key:"render",value:function(){return this.props.children}}]),n}(e.Component);function qe(t,n){var r=t.children,o=t.disabled,i=e.useRef(null),a=e.useRef(null),s=e.useContext(ue),c="function"==typeof r,l=c?r(i):r,u=e.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),f=!c&&e.isValidElement(l)&&_(l),p=z(f?l.ref:null,i),h=function(){var e;return le(i.current)||(i.current&&"object"===d(i.current)?le(null===(e=i.current)||void 0===e?void 0:e.nativeElement):null)||le(a.current)};e.useImperativeHandle(n,(function(){return h()}));var m=e.useRef(t);m.current=t;var v=e.useCallback((function(e){var t=m.current,n=t.onResize,r=t.data,o=e.getBoundingClientRect(),i=o.width,a=o.height,c=e.offsetWidth,l=e.offsetHeight,f=Math.floor(i),d=Math.floor(a);if(u.current.width!==f||u.current.height!==d||u.current.offsetWidth!==c||u.current.offsetHeight!==l){var p={width:f,height:d,offsetWidth:c,offsetHeight:l};u.current=p;var h=c===Math.round(i)?i:c,v=l===Math.round(a)?a:l,b=g(g({},p),{},{offsetWidth:h,offsetHeight:v});null==s||s(b,e,r),n&&Promise.resolve().then((function(){n(b,e)}))}}),[]);return e.useEffect((function(){var e,t,n=h();return n&&!o&&(e=n,t=v,Re.has(e)||(Re.set(e,new Set),Te.observe(e)),Re.get(e).add(t)),function(){return function(e,t){Re.has(e)&&(Re.get(e).delete(t),Re.get(e).size||(Te.unobserve(e),Re.delete(e)))}(n,v)}}),[i.current,o]),e.createElement(Xe,{ref:a},f?e.cloneElement(l,{ref:p}):l)}const Ve=e.forwardRef(qe);function Ge(t,n){var r=t.children;return("function"==typeof r?[r]:se(r)).map((function(r,o){var i=(null==r?void 0:r.key)||"".concat("rc-observer-key","-").concat(o);return e.createElement(Ve,f({},t,{key:i,ref:0===o?n:void 0}),r)}))}var Ye=e.forwardRef(Ge);Ye.Collection=function(t){var n=t.children,r=t.onBatchResize,o=e.useRef(0),i=e.useRef([]),a=e.useContext(ue),s=e.useCallback((function(e,t,n){o.current+=1;var s=o.current;i.current.push({size:e,element:t,data:n}),Promise.resolve().then((function(){s===o.current&&(null==r||r(i.current),i.current=[])})),null==a||a(e,t,n)}),[r,a]);return e.createElement(ue.Provider,{value:s},n)};const Ue=Ye;function Ze(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}function Ke(e){return function(e){return Ze(e)instanceof ShadowRoot}(e)?Ze(e):null}function Qe(t){var n=e.useRef();n.current=t;var r=e.useCallback((function(){for(var e,t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];return null===(e=n.current)||void 0===e?void 0:e.call.apply(e,[n].concat(r))}),[]);return r}var Je=0,et=g({},e).useId;const tt=et?function(e){var t=et();return e||t}:function(t){var n=w(e.useState("ssr-id"),2),r=n[0],o=n[1];return e.useEffect((function(){var e=Je;Je+=1,o("rc_unique_".concat(e))}),[]),t||r};var nt=e.createContext({}),rt=function(e){Le(n,e);var t=We(n);function n(){return ze(this,n),t.apply(this,arguments)}return Ne(n,[{key:"render",value:function(){return this.props.children}}]),n}(e.Component);const ot=rt;function it(t){var n=e.useRef(!1),r=w(e.useState(t),2),o=r[0],i=r[1];return e.useEffect((function(){return n.current=!1,function(){n.current=!0}}),[]),[o,function(e,t){t&&n.current||i(e)}]}var at="none",st="appear",ct="enter",lt="leave",ut="none",ft="prepare",dt="start",pt="active",ht="end",mt="prepared";function gt(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit".concat(e)]="webkit".concat(t),n["Moz".concat(e)]="moz".concat(t),n["ms".concat(e)]="MS".concat(t),n["O".concat(e)]="o".concat(t.toLowerCase()),n}var vt,bt,yt,wt=(vt=k(),bt="undefined"!=typeof window?window:{},yt={animationend:gt("Animation","AnimationEnd"),transitionend:gt("Transition","TransitionEnd")},vt&&("AnimationEvent"in bt||delete yt.animationend.animation,"TransitionEvent"in bt||delete yt.transitionend.transition),yt),xt={};if(k()){var St=document.createElement("div");xt=St.style}var kt={};function Ct(e){if(kt[e])return kt[e];var t=wt[e];if(t)for(var n=Object.keys(t),r=n.length,o=0;o<r;o+=1){var i=n[o];if(Object.prototype.hasOwnProperty.call(t,i)&&i in xt)return kt[e]=t[i],kt[e]}return""}var Ot=Ct("animationend"),Et=Ct("transitionend"),$t=!(!Ot||!Et),At=Ot||"animationend",jt=Et||"transitionend";function Mt(e,t){return e?"object"===d(e)?e[t.replace(/-\w/g,(function(e){return e[1].toUpperCase()}))]:"".concat(e,"-").concat(t):null}const Pt=k()?e.useLayoutEffect:e.useEffect;var Rt=function(e){return+setTimeout(e,16)},Tt=function(e){return clearTimeout(e)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(Rt=function(e){return window.requestAnimationFrame(e)},Tt=function(e){return window.cancelAnimationFrame(e)});var zt=0,_t=new Map;function Nt(e){_t.delete(e)}var It=function(e){var t=zt+=1;return function n(r){if(0===r)Nt(t),e();else{var o=Rt((function(){n(r-1)}));_t.set(t,o)}}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:1),t};It.cancel=function(e){var t=_t.get(e);return Nt(e),Tt(t)};const Lt=It;var Ht=[ft,dt,pt,ht],Bt=[ft,mt],Ft=!1;function Dt(e){return e===pt||e===ht}const Wt=function(t){var n=t;"object"===d(t)&&(n=t.transitionSupport);var r=e.forwardRef((function(t,r){var o=t.visible,i=void 0===o||o,a=t.removeOnLeave,s=void 0===a||a,c=t.forceRender,u=t.children,f=t.motionName,d=t.leavedClassName,p=t.eventProps,m=function(e,t){return!(!e.motionName||!n||!1===t)}(t,e.useContext(nt).motion),v=(0,e.useRef)(),b=(0,e.useRef)(),y=function(t,n,r,o){var i=o.motionEnter,a=void 0===i||i,s=o.motionAppear,c=void 0===s||s,l=o.motionLeave,u=void 0===l||l,f=o.motionDeadline,d=o.motionLeaveImmediately,p=o.onAppearPrepare,m=o.onEnterPrepare,v=o.onLeavePrepare,b=o.onAppearStart,y=o.onEnterStart,x=o.onLeaveStart,S=o.onAppearActive,k=o.onEnterActive,C=o.onLeaveActive,O=o.onAppearEnd,E=o.onEnterEnd,$=o.onLeaveEnd,A=o.onVisibleChanged,j=w(it(),2),M=j[0],P=j[1],R=w(it(at),2),T=R[0],z=R[1],_=w(it(null),2),N=_[0],I=_[1],L=(0,e.useRef)(!1),H=(0,e.useRef)(null);function B(){return r()}var F=(0,e.useRef)(!1);function D(){z(at,!0),I(null,!0)}function W(e){var t=B();if(!e||e.deadline||e.target===t){var n,r=F.current;T===st&&r?n=null==O?void 0:O(t,e):T===ct&&r?n=null==E?void 0:E(t,e):T===lt&&r&&(n=null==$?void 0:$(t,e)),T!==at&&r&&!1!==n&&D()}}var X=w(function(t){var n=(0,e.useRef)(),r=(0,e.useRef)(t);r.current=t;var o=e.useCallback((function(e){r.current(e)}),[]);function i(e){e&&(e.removeEventListener(jt,o),e.removeEventListener(At,o))}return e.useEffect((function(){return function(){i(n.current)}}),[]),[function(e){n.current&&n.current!==e&&i(n.current),e&&e!==n.current&&(e.addEventListener(jt,o),e.addEventListener(At,o),n.current=e)},i]}(W),1)[0],q=function(e){var t,n,r;switch(e){case st:return h(t={},ft,p),h(t,dt,b),h(t,pt,S),t;case ct:return h(n={},ft,m),h(n,dt,y),h(n,pt,k),n;case lt:return h(r={},ft,v),h(r,dt,x),h(r,pt,C),r;default:return{}}},V=e.useMemo((function(){return q(T)}),[T]),G=w(function(t,n,r){var o=w(it(ut),2),i=o[0],a=o[1],s=function(){var t=e.useRef(null);function n(){Lt.cancel(t.current)}return e.useEffect((function(){return function(){n()}}),[]),[function e(r){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;n();var i=Lt((function(){o<=1?r({isCanceled:function(){return i!==t.current}}):e(r,o-1)}));t.current=i},n]}(),c=w(s,2),l=c[0],u=c[1],f=n?Bt:Ht;return Pt((function(){if(i!==ut&&i!==ht){var e=f.indexOf(i),t=f[e+1],n=r(i);n===Ft?a(t,!0):t&&l((function(e){function r(){e.isCanceled()||a(t,!0)}!0===n?r():Promise.resolve(n).then(r)}))}}),[t,i]),e.useEffect((function(){return function(){u()}}),[]),[function(){a(ft,!0)},i]}(T,!t,(function(e){if(e===ft){var t=V[ft];return t?t(B()):Ft}var n;return U in V&&I((null===(n=V[U])||void 0===n?void 0:n.call(V,B(),null))||null),U===pt&&(X(B()),f>0&&(clearTimeout(H.current),H.current=setTimeout((function(){W({deadline:!0})}),f))),U===mt&&D(),true})),2),Y=G[0],U=G[1],Z=Dt(U);F.current=Z,Pt((function(){P(n);var e,r=L.current;L.current=!0,!r&&n&&c&&(e=st),r&&n&&a&&(e=ct),(r&&!n&&u||!r&&d&&!n&&u)&&(e=lt);var o=q(e);e&&(t||o[ft])?(z(e),Y()):z(at)}),[n]),(0,e.useEffect)((function(){(T===st&&!c||T===ct&&!a||T===lt&&!u)&&z(at)}),[c,a,u]),(0,e.useEffect)((function(){return function(){L.current=!1,clearTimeout(H.current)}}),[]);var K=e.useRef(!1);(0,e.useEffect)((function(){M&&(K.current=!0),void 0!==M&&T===at&&((K.current||M)&&(null==A||A(M)),K.current=!0)}),[M,T]);var Q=N;return V[ft]&&U===dt&&(Q=g({transition:"none"},Q)),[T,U,Q,null!=M?M:n]}(m,i,(function(){try{return v.current instanceof HTMLElement?v.current:le(b.current)}catch(e){return null}}),t),x=w(y,4),S=x[0],k=x[1],C=x[2],O=x[3],E=e.useRef(O);O&&(E.current=!0);var $,A=e.useCallback((function(e){v.current=e,R(r,e)}),[r]),j=g(g({},p),{},{visible:i});if(u)if(S===at)$=O?u(g({},j),A):!s&&E.current&&d?u(g(g({},j),{},{className:d}),A):c||!s&&!d?u(g(g({},j),{},{style:{display:"none"}}),A):null;else{var M,P;k===ft?P="prepare":Dt(k)?P="active":k===dt&&(P="start");var T=Mt(f,"".concat(S,"-").concat(P));$=u(g(g({},j),{},{className:l()(Mt(f,S),(M={},h(M,T,T&&P),h(M,f,"string"==typeof f),M)),style:C}),A)}else $=null;return e.isValidElement($)&&_($)&&($.ref||($=e.cloneElement($,{ref:A}))),e.createElement(ot,{ref:b},$)}));return r.displayName="CSSMotion",r}($t);var Xt="add",qt="keep",Vt="remove",Gt="removed";function Yt(e){var t;return g(g({},t=e&&"object"===d(e)&&"key"in e?e:{key:e}),{},{key:String(t.key)})}function Ut(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).map(Yt)}var Zt=["component","children","onVisibleChanged","onAllRemoved"],Kt=["status"],Qt=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearPrepare","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];!function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Wt,r=function(t){Le(o,t);var r=We(o);function o(){var e;ze(this,o);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return h(Fe(e=r.call.apply(r,[this].concat(n))),"state",{keyEntities:[]}),h(Fe(e),"removeKey",(function(t){var n=e.state.keyEntities.map((function(e){return e.key!==t?e:g(g({},e),{},{status:Gt})}));return e.setState({keyEntities:n}),n.filter((function(e){return e.status!==Gt})).length})),e}return Ne(o,[{key:"render",value:function(){var t=this,r=this.state.keyEntities,o=this.props,i=o.component,a=o.children,s=o.onVisibleChanged,c=o.onAllRemoved,l=v(o,Zt),u=i||e.Fragment,d={};return Qt.forEach((function(e){d[e]=l[e],delete l[e]})),delete l.keys,e.createElement(u,l,r.map((function(r,o){var i=r.status,l=v(r,Kt),u=i===Xt||i===qt;return e.createElement(n,f({},d,{key:l.key,visible:u,eventProps:l,onVisibleChanged:function(e){null==s||s(e,{key:l.key}),e||0===t.removeKey(l.key)&&c&&c()}}),(function(e,t){return a(g(g({},e),{},{index:o}),t)}))})))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.keys,r=t.keyEntities,o=Ut(n),i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],r=0,o=t.length,i=Ut(e),a=Ut(t);i.forEach((function(e){for(var t=!1,i=r;i<o;i+=1){var s=a[i];if(s.key===e.key){r<i&&(n=n.concat(a.slice(r,i).map((function(e){return g(g({},e),{},{status:Xt})}))),r=i),n.push(g(g({},s),{},{status:qt})),r+=1,t=!0;break}}t||n.push(g(g({},e),{},{status:Vt}))})),r<o&&(n=n.concat(a.slice(r).map((function(e){return g(g({},e),{},{status:Xt})}))));var s={};return n.forEach((function(e){var t=e.key;s[t]=(s[t]||0)+1})),Object.keys(s).filter((function(e){return s[e]>1})).forEach((function(e){(n=n.filter((function(t){var n=t.key,r=t.status;return n!==e||r!==Vt}))).forEach((function(t){t.key===e&&(t.status=qt)}))})),n}(r,o);return{keyEntities:i.filter((function(e){var t=r.find((function(t){var n=t.key;return e.key===n}));return!t||t.status!==Gt||e.status!==Vt}))}}}]),o}(e.Component);h(r,"defaultProps",{component:"div"})}($t);const Jt=Wt;function en(t){var n=t.prefixCls,r=t.align,o=t.arrow,i=t.arrowPos,a=o||{},s=a.className,c=a.content,u=i.x,f=void 0===u?0:u,d=i.y,p=void 0===d?0:d,h=e.useRef();if(!r||!r.points)return null;var m={position:"absolute"};if(!1!==r.autoArrow){var g=r.points[0],v=r.points[1],b=g[0],y=g[1],w=v[0],x=v[1];b!==w&&["t","b"].includes(b)?"t"===b?m.top=0:m.bottom=0:m.top=p,y!==x&&["l","r"].includes(y)?"l"===y?m.left=0:m.right=0:m.left=f}return e.createElement("div",{ref:h,className:l()("".concat(n,"-arrow"),s),style:m},c)}function tn(t){var n=t.prefixCls,r=t.open,o=t.zIndex,i=t.mask,a=t.motion;return i?e.createElement(Jt,f({},a,{motionAppear:!0,visible:r,removeOnLeave:!0}),(function(t){var r=t.className;return e.createElement("div",{style:{zIndex:o},className:l()("".concat(n,"-mask"),r)})})):null}var nn=e.memo((function(e){return e.children}),(function(e,t){return t.cache}));const rn=nn;var on=e.forwardRef((function(t,n){var r=t.popup,o=t.className,i=t.prefixCls,a=t.style,s=t.target,c=t.onVisibleChanged,u=t.open,d=t.keepDom,p=t.fresh,h=t.onClick,m=t.mask,v=t.arrow,b=t.arrowPos,y=t.align,x=t.motion,S=t.maskMotion,k=t.forceRender,C=t.getPopupContainer,O=t.autoDestroy,E=t.portal,$=t.zIndex,A=t.onMouseEnter,j=t.onMouseLeave,M=t.onPointerEnter,P=t.ready,R=t.offsetX,z=t.offsetY,_=t.offsetR,N=t.offsetB,I=t.onAlign,L=t.onPrepare,H=t.stretch,B=t.targetWidth,D=t.targetHeight,W="function"==typeof r?r():r,X=u||d,q=(null==C?void 0:C.length)>0,V=w(e.useState(!C||!q),2),G=V[0],Y=V[1];if(F((function(){!G&&q&&s&&Y(!0)}),[G,q,s]),!G)return null;var U="auto",Z={left:"-1000vw",top:"-1000vh",right:U,bottom:U};if(P||!u){var K,Q=y.points,J=y.dynamicInset||(null===(K=y._experimental)||void 0===K?void 0:K.dynamicInset),ee=J&&"r"===Q[0][1],te=J&&"b"===Q[0][0];ee?(Z.right=_,Z.left=U):(Z.left=R,Z.right=U),te?(Z.bottom=N,Z.top=U):(Z.top=z,Z.bottom=U)}var ne={};return H&&(H.includes("height")&&D?ne.height=D:H.includes("minHeight")&&D&&(ne.minHeight=D),H.includes("width")&&B?ne.width=B:H.includes("minWidth")&&B&&(ne.minWidth=B)),u||(ne.pointerEvents="none"),e.createElement(E,{open:k||X,getContainer:C&&function(){return C(s)},autoDestroy:O},e.createElement(tn,{prefixCls:i,open:u,zIndex:$,mask:m,motion:S}),e.createElement(Ue,{onResize:I,disabled:!u},(function(t){return e.createElement(Jt,f({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:k,leavedClassName:"".concat(i,"-hidden")},x,{onAppearPrepare:L,onEnterPrepare:L,visible:u,onVisibleChanged:function(e){var t;null==x||null===(t=x.onVisibleChanged)||void 0===t||t.call(x,e),c(e)}}),(function(r,s){var c=r.className,f=r.style,d=l()(i,c,o);return e.createElement("div",{ref:T(t,n,s),className:d,style:g(g(g(g({"--arrow-x":"".concat(b.x||0,"px"),"--arrow-y":"".concat(b.y||0,"px")},Z),ne),f),{},{boxSizing:"border-box",zIndex:$},a),onMouseEnter:A,onMouseLeave:j,onPointerEnter:M,onClick:h},v&&e.createElement(en,{prefixCls:i,arrow:v,arrowPos:b,align:y}),e.createElement(rn,{cache:!u&&!p},W))}))})))}));const an=on;var sn=e.forwardRef((function(t,n){var r=t.children,o=t.getTriggerDOMNode,i=_(r),a=e.useCallback((function(e){R(n,o?o(e):e)}),[o]),s=z(a,r.ref);return i?e.cloneElement(r,{ref:s}):r}));const cn=sn,ln=e.createContext(null);function un(e){return e?Array.isArray(e)?e:[e]:[]}function fn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return(arguments.length>2?arguments[2]:void 0)?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}function dn(e,t,n,r){return t||(n?{motionName:"".concat(e,"-").concat(n)}:r?{motionName:r}:null)}function pn(e){return e.ownerDocument.defaultView}function hn(e){for(var t=[],n=null==e?void 0:e.parentElement,r=["hidden","scroll","clip","auto"];n;){var o=pn(n).getComputedStyle(n);[o.overflowX,o.overflowY,o.overflow].some((function(e){return r.includes(e)}))&&t.push(n),n=n.parentElement}return t}function mn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return Number.isNaN(e)?t:e}function gn(e){return mn(parseFloat(e),0)}function vn(e,t){var n=g({},e);return(t||[]).forEach((function(e){if(!(e instanceof HTMLBodyElement||e instanceof HTMLHtmlElement)){var t=pn(e).getComputedStyle(e),r=t.overflow,o=t.overflowClipMargin,i=t.borderTopWidth,a=t.borderBottomWidth,s=t.borderLeftWidth,c=t.borderRightWidth,l=e.getBoundingClientRect(),u=e.offsetHeight,f=e.clientHeight,d=e.offsetWidth,p=e.clientWidth,h=gn(i),m=gn(a),g=gn(s),v=gn(c),b=mn(Math.round(l.width/d*1e3)/1e3),y=mn(Math.round(l.height/u*1e3)/1e3),w=(d-p-g-v)*b,x=(u-f-h-m)*y,S=h*y,k=m*y,C=g*b,O=v*b,E=0,$=0;if("clip"===r){var A=gn(o);E=A*b,$=A*y}var j=l.x+C-E,M=l.y+S-$,P=j+l.width+2*E-C-O-w,R=M+l.height+2*$-S-k-x;n.left=Math.max(n.left,j),n.top=Math.max(n.top,M),n.right=Math.min(n.right,P),n.bottom=Math.min(n.bottom,R)}})),n}function bn(e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),n=t.match(/^(.*)\%$/);return n?e*(parseFloat(n[1])/100):parseFloat(t)}function yn(e,t){var n=w(t||[],2),r=n[0],o=n[1];return[bn(e.width,r),bn(e.height,o)]}function wn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return[e[0],e[1]]}function xn(e,t){var n,r=t[0],o=t[1];return n="t"===r?e.y:"b"===r?e.y+e.height:e.y+e.height/2,{x:"l"===o?e.x:"r"===o?e.x+e.width:e.x+e.width/2,y:n}}function Sn(e,t){var n={t:"b",b:"t",l:"r",r:"l"};return e.map((function(e,r){return r===t?n[e]||"c":e})).join("")}var kn=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"];const Cn=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ae,n=e.forwardRef((function(n,r){var o=n.prefixCls,i=void 0===o?"rc-trigger-popup":o,a=n.children,s=n.action,c=void 0===s?"hover":s,u=n.showAction,f=n.hideAction,d=n.popupVisible,p=n.defaultPopupVisible,h=n.onPopupVisibleChange,m=n.afterPopupVisibleChange,b=n.mouseEnterDelay,y=n.mouseLeaveDelay,x=void 0===y?.1:y,S=n.focusDelay,k=n.blurDelay,C=n.mask,O=n.maskClosable,E=void 0===O||O,$=n.getPopupContainer,A=n.forceRender,j=n.autoDestroy,M=n.destroyPopupOnHide,P=n.popup,R=n.popupClassName,T=n.popupStyle,z=n.popupPlacement,_=n.builtinPlacements,N=void 0===_?{}:_,L=n.popupAlign,H=n.zIndex,B=n.stretch,D=n.getPopupClassNameFromAlign,W=n.fresh,X=n.alignPoint,q=n.onPopupClick,V=n.onPopupAlign,G=n.arrow,Y=n.popupMotion,U=n.maskMotion,Z=n.popupTransitionName,K=n.popupAnimation,Q=n.maskTransitionName,J=n.maskAnimation,ee=n.className,te=n.getTriggerDOMNode,ne=v(n,kn),re=j||M||!1,oe=w(e.useState(!1),2),ie=oe[0],ae=oe[1];F((function(){ae(function(){if("undefined"==typeof navigator||"undefined"==typeof window)return!1;var e=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(null==e?void 0:e.substr(0,4))}())}),[]);var se=e.useRef({}),le=e.useContext(ln),ue=e.useMemo((function(){return{registerSubPopup:function(e,t){se.current[e]=t,null==le||le.registerSubPopup(e,t)}}}),[le]),fe=tt(),de=w(e.useState(null),2),pe=de[0],he=de[1],me=Qe((function(e){ce(e)&&pe!==e&&he(e),null==le||le.registerSubPopup(fe,e)})),ge=w(e.useState(null),2),ve=ge[0],be=ge[1],ye=e.useRef(null),we=Qe((function(e){ce(e)&&ve!==e&&(be(e),ye.current=e)})),xe=e.Children.only(a),Se=(null==xe?void 0:xe.props)||{},ke={},Ce=Qe((function(e){var t,n,r=ve;return(null==r?void 0:r.contains(e))||(null===(t=Ke(r))||void 0===t?void 0:t.host)===e||e===r||(null==pe?void 0:pe.contains(e))||(null===(n=Ke(pe))||void 0===n?void 0:n.host)===e||e===pe||Object.values(se.current).some((function(t){return(null==t?void 0:t.contains(e))||e===t}))})),Oe=dn(i,Y,K,Z),Ee=dn(i,U,J,Q),$e=w(e.useState(p||!1),2),Ae=$e[0],je=$e[1],Me=null!=d?d:Ae,Pe=Qe((function(e){void 0===d&&je(e)}));F((function(){je(d||!1)}),[d]);var Re=e.useRef(Me);Re.current=Me;var Te=e.useRef([]);Te.current=[];var ze=Qe((function(e){var t;Pe(e),(null!==(t=Te.current[Te.current.length-1])&&void 0!==t?t:Me)!==e&&(Te.current.push(e),null==h||h(e))})),_e=e.useRef(),Ne=function(){clearTimeout(_e.current)},Ie=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;Ne(),0===t?ze(e):_e.current=setTimeout((function(){ze(e)}),1e3*t)};e.useEffect((function(){return Ne}),[]);var Le=w(e.useState(!1),2),He=Le[0],Be=Le[1];F((function(e){e&&!Me||Be(!0)}),[Me]);var Fe=w(e.useState(null),2),De=Fe[0],We=Fe[1],Xe=w(e.useState([0,0]),2),qe=Xe[0],Ve=Xe[1],Ge=function(e){Ve([e.clientX,e.clientY])},Ye=function(t,n,r,o,i,a,s){var c=w(e.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:i[o]||{}}),2),l=c[0],u=c[1],f=e.useRef(0),d=e.useMemo((function(){return n?hn(n):[]}),[n]),p=e.useRef({});t||(p.current={});var h=Qe((function(){if(n&&r&&t){var e,c,l,f=n,h=f.ownerDocument,m=pn(f).getComputedStyle(f),v=m.width,b=m.height,y=m.position,x=f.style.left,S=f.style.top,k=f.style.right,C=f.style.bottom,O=f.style.overflow,E=g(g({},i[o]),a),$=h.createElement("div");if(null===(e=f.parentElement)||void 0===e||e.appendChild($),$.style.left="".concat(f.offsetLeft,"px"),$.style.top="".concat(f.offsetTop,"px"),$.style.position=y,$.style.height="".concat(f.offsetHeight,"px"),$.style.width="".concat(f.offsetWidth,"px"),f.style.left="0",f.style.top="0",f.style.right="auto",f.style.bottom="auto",f.style.overflow="hidden",Array.isArray(r))l={x:r[0],y:r[1],width:0,height:0};else{var A=r.getBoundingClientRect();l={x:A.x,y:A.y,width:A.width,height:A.height}}var j=f.getBoundingClientRect(),M=h.documentElement,P=M.clientWidth,R=M.clientHeight,T=M.scrollWidth,z=M.scrollHeight,_=M.scrollTop,N=M.scrollLeft,I=j.height,L=j.width,H=l.height,B=l.width,F={left:0,top:0,right:P,bottom:R},D={left:-N,top:-_,right:T-N,bottom:z-_},W=E.htmlRegion,X="visible",q="visibleFirst";"scroll"!==W&&W!==q&&(W=X);var V=W===q,G=vn(D,d),Y=vn(F,d),U=W===X?Y:G,Z=V?Y:U;f.style.left="auto",f.style.top="auto",f.style.right="0",f.style.bottom="0";var K=f.getBoundingClientRect();f.style.left=x,f.style.top=S,f.style.right=k,f.style.bottom=C,f.style.overflow=O,null===(c=f.parentElement)||void 0===c||c.removeChild($);var Q=mn(Math.round(L/parseFloat(v)*1e3)/1e3),J=mn(Math.round(I/parseFloat(b)*1e3)/1e3);if(0===Q||0===J||ce(r)&&!function(e){if(!e)return!1;if(e instanceof Element){if(e.offsetParent)return!0;if(e.getBBox){var t=e.getBBox(),n=t.width,r=t.height;if(n||r)return!0}if(e.getBoundingClientRect){var o=e.getBoundingClientRect(),i=o.width,a=o.height;if(i||a)return!0}}return!1}(r))return;var ee=E.offset,te=E.targetOffset,ne=w(yn(j,ee),2),re=ne[0],oe=ne[1],ie=w(yn(l,te),2),ae=ie[0],se=ie[1];l.x-=ae,l.y-=se;var le=w(E.points||[],2),ue=le[0],fe=wn(le[1]),de=wn(ue),pe=xn(l,fe),he=xn(j,de),me=g({},E),ge=pe.x-he.x+re,ve=pe.y-he.y+oe;function ut(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:U,r=j.x+e,o=j.y+t,i=r+L,a=o+I,s=Math.max(r,n.left),c=Math.max(o,n.top),l=Math.min(i,n.right),u=Math.min(a,n.bottom);return Math.max(0,(l-s)*(u-c))}var be,ye,we,xe,Se=ut(ge,ve),ke=ut(ge,ve,Y),Ce=xn(l,["t","l"]),Oe=xn(j,["t","l"]),Ee=xn(l,["b","r"]),$e=xn(j,["b","r"]),Ae=E.overflow||{},je=Ae.adjustX,Me=Ae.adjustY,Pe=Ae.shiftX,Re=Ae.shiftY,Te=function(e){return"boolean"==typeof e?e:e>=0};function ft(){be=j.y+ve,ye=be+I,we=j.x+ge,xe=we+L}ft();var ze=Te(Me),_e=de[0]===fe[0];if(ze&&"t"===de[0]&&(ye>Z.bottom||p.current.bt)){var Ne=ve;_e?Ne-=I-H:Ne=Ce.y-$e.y-oe;var Ie=ut(ge,Ne),Le=ut(ge,Ne,Y);Ie>Se||Ie===Se&&(!V||Le>=ke)?(p.current.bt=!0,ve=Ne,oe=-oe,me.points=[Sn(de,0),Sn(fe,0)]):p.current.bt=!1}if(ze&&"b"===de[0]&&(be<Z.top||p.current.tb)){var He=ve;_e?He+=I-H:He=Ee.y-Oe.y-oe;var Be=ut(ge,He),Fe=ut(ge,He,Y);Be>Se||Be===Se&&(!V||Fe>=ke)?(p.current.tb=!0,ve=He,oe=-oe,me.points=[Sn(de,0),Sn(fe,0)]):p.current.tb=!1}var De=Te(je),We=de[1]===fe[1];if(De&&"l"===de[1]&&(xe>Z.right||p.current.rl)){var Xe=ge;We?Xe-=L-B:Xe=Ce.x-$e.x-re;var qe=ut(Xe,ve),Ve=ut(Xe,ve,Y);qe>Se||qe===Se&&(!V||Ve>=ke)?(p.current.rl=!0,ge=Xe,re=-re,me.points=[Sn(de,1),Sn(fe,1)]):p.current.rl=!1}if(De&&"r"===de[1]&&(we<Z.left||p.current.lr)){var Ge=ge;We?Ge+=L-B:Ge=Ee.x-Oe.x-re;var Ye=ut(Ge,ve),Ue=ut(Ge,ve,Y);Ye>Se||Ye===Se&&(!V||Ue>=ke)?(p.current.lr=!0,ge=Ge,re=-re,me.points=[Sn(de,1),Sn(fe,1)]):p.current.lr=!1}ft();var Ze=!0===Pe?0:Pe;"number"==typeof Ze&&(we<Y.left&&(ge-=we-Y.left-re,l.x+B<Y.left+Ze&&(ge+=l.x-Y.left+B-Ze)),xe>Y.right&&(ge-=xe-Y.right-re,l.x>Y.right-Ze&&(ge+=l.x-Y.right+Ze)));var Ke=!0===Re?0:Re;"number"==typeof Ke&&(be<Y.top&&(ve-=be-Y.top-oe,l.y+H<Y.top+Ke&&(ve+=l.y-Y.top+H-Ke)),ye>Y.bottom&&(ve-=ye-Y.bottom-oe,l.y>Y.bottom-Ke&&(ve+=l.y-Y.bottom+Ke)));var Qe=j.x+ge,Je=Qe+L,et=j.y+ve,tt=et+I,nt=l.x,rt=nt+B,ot=l.y,it=ot+H,at=(Math.max(Qe,nt)+Math.min(Je,rt))/2-Qe,st=(Math.max(et,ot)+Math.min(tt,it))/2-et;null==s||s(n,me);var ct=K.right-j.x-(ge+j.width),lt=K.bottom-j.y-(ve+j.height);u({ready:!0,offsetX:ge/Q,offsetY:ve/J,offsetR:ct/Q,offsetB:lt/J,arrowX:at/Q,arrowY:st/J,scaleX:Q,scaleY:J,align:me})}})),m=function(){u((function(e){return g(g({},e),{},{ready:!1})}))};return F(m,[o]),F((function(){t||m()}),[t]),[l.ready,l.offsetX,l.offsetY,l.offsetR,l.offsetB,l.arrowX,l.arrowY,l.scaleX,l.scaleY,l.align,function(){f.current+=1;var e=f.current;Promise.resolve().then((function(){f.current===e&&h()}))}]}(Me,pe,X?qe:ve,z,N,L,V),Ze=w(Ye,11),Je=Ze[0],et=Ze[1],nt=Ze[2],rt=Ze[3],ot=Ze[4],it=Ze[5],at=Ze[6],st=Ze[7],ct=Ze[8],lt=Ze[9],ut=Ze[10],ft=function(t,n,r,o){return e.useMemo((function(){var e=un(null!=r?r:n),i=un(null!=o?o:n),a=new Set(e),s=new Set(i);return t&&(a.has("hover")&&(a.delete("hover"),a.add("click")),s.has("hover")&&(s.delete("hover"),s.add("click"))),[a,s]}),[t,n,r,o])}(ie,c,u,f),dt=w(ft,2),pt=dt[0],ht=dt[1],mt=pt.has("click"),gt=ht.has("click")||ht.has("contextMenu"),vt=Qe((function(){He||ut()}));!function(e,t,n,r,o){F((function(){if(e&&t&&n){var o=n,i=hn(t),a=hn(o),s=pn(o),c=new Set([s].concat(I(i),I(a)));function l(){r(),Re.current&&X&&gt&&Ie(!1)}return c.forEach((function(e){e.addEventListener("scroll",l,{passive:!0})})),s.addEventListener("resize",l,{passive:!0}),r(),function(){c.forEach((function(e){e.removeEventListener("scroll",l),s.removeEventListener("resize",l)}))}}}),[e,t,n])}(Me,ve,pe,vt),F((function(){vt()}),[qe,z]),F((function(){!Me||null!=N&&N[z]||vt()}),[JSON.stringify(L)]);var bt=e.useMemo((function(){var e=function(e,t,n,r){for(var o=n.points,i=Object.keys(e),a=0;a<i.length;a+=1){var s,c=i[a];if(fn(null===(s=e[c])||void 0===s?void 0:s.points,o,r))return"".concat(t,"-placement-").concat(c)}return""}(N,i,lt,X);return l()(e,null==D?void 0:D(lt))}),[lt,D,N,i,X]);e.useImperativeHandle(r,(function(){return{nativeElement:ye.current,forceAlign:vt}}));var yt=w(e.useState(0),2),wt=yt[0],xt=yt[1],St=w(e.useState(0),2),kt=St[0],Ct=St[1],Ot=function(){if(B&&ve){var e=ve.getBoundingClientRect();xt(e.width),Ct(e.height)}};function Et(e,t,n,r){ke[e]=function(o){var i;null==r||r(o),Ie(t,n);for(var a=arguments.length,s=new Array(a>1?a-1:0),c=1;c<a;c++)s[c-1]=arguments[c];null===(i=Se[e])||void 0===i||i.call.apply(i,[Se,o].concat(s))}}F((function(){De&&(ut(),De(),We(null))}),[De]),(mt||gt)&&(ke.onClick=function(e){var t;Re.current&&gt?Ie(!1):!Re.current&&mt&&(Ge(e),Ie(!0));for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];null===(t=Se.onClick)||void 0===t||t.call.apply(t,[Se,e].concat(r))}),function(t,n,r,o,i,a,s,c){var l=e.useRef(t);l.current=t,e.useEffect((function(){if(n&&o&&(!i||a)){var e=function(e){var t=e.target;l.current&&!s(t)&&c(!1)},t=pn(o);t.addEventListener("mousedown",e,!0),t.addEventListener("contextmenu",e,!0);var u=Ke(r);return u&&(u.addEventListener("mousedown",e,!0),u.addEventListener("contextmenu",e,!0)),function(){t.removeEventListener("mousedown",e,!0),t.removeEventListener("contextmenu",e,!0),u&&(u.removeEventListener("mousedown",e,!0),u.removeEventListener("contextmenu",e,!0))}}}),[n,r,o,i,a])}(Me,gt,ve,pe,C,E,Ce,Ie);var $t,At,jt=pt.has("hover"),Mt=ht.has("hover");jt&&(Et("onMouseEnter",!0,b,(function(e){Ge(e)})),Et("onPointerEnter",!0,b,(function(e){Ge(e)})),$t=function(e){(Me||He)&&null!=pe&&pe.contains(e.target)&&Ie(!0,b)},X&&(ke.onMouseMove=function(e){var t;null===(t=Se.onMouseMove)||void 0===t||t.call(Se,e)})),Mt&&(Et("onMouseLeave",!1,x),Et("onPointerLeave",!1,x),At=function(){Ie(!1,x)}),pt.has("focus")&&Et("onFocus",!0,S),ht.has("focus")&&Et("onBlur",!1,k),pt.has("contextMenu")&&(ke.onContextMenu=function(e){var t;Re.current&&ht.has("contextMenu")?Ie(!1):(Ge(e),Ie(!0)),e.preventDefault();for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];null===(t=Se.onContextMenu)||void 0===t||t.call.apply(t,[Se,e].concat(r))}),ee&&(ke.className=l()(Se.className,ee));var Pt=g(g({},Se),ke),Rt={};["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"].forEach((function(e){ne[e]&&(Rt[e]=function(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];null===(t=Pt[e])||void 0===t||t.call.apply(t,[Pt].concat(r)),ne[e].apply(ne,r)})}));var Tt=e.cloneElement(xe,g(g({},Pt),Rt)),zt={x:it,y:at},_t=G?g({},!0!==G?G:{}):null;return e.createElement(e.Fragment,null,e.createElement(Ue,{disabled:!Me,ref:we,onResize:function(){Ot(),vt()}},e.createElement(cn,{getTriggerDOMNode:te},Tt)),e.createElement(ln.Provider,{value:ue},e.createElement(an,{portal:t,ref:me,prefixCls:i,popup:P,className:l()(R,bt),style:T,target:ve,onMouseEnter:$t,onMouseLeave:At,onPointerEnter:$t,zIndex:H,open:Me,keepDom:He,fresh:W,onClick:q,mask:C,motion:Oe,maskMotion:Ee,onVisibleChanged:function(e){Be(!1),ut(),null==m||m(e)},onPrepare:function(){return new Promise((function(e){Ot(),We((function(){return e}))}))},forceRender:A,autoDestroy:re,getPopupContainer:$,align:lt,arrow:_t,arrowPos:zt,ready:Je,offsetX:et,offsetY:nt,offsetR:rt,offsetB:ot,onAlign:vt,stretch:B,targetWidth:wt/st,targetHeight:kt/ct})))}));return n}(ae);var On={shiftX:64,adjustY:1},En={adjustX:1,shiftY:!0},$n=[0,0],An={left:{points:["cr","cl"],overflow:En,offset:[-4,0],targetOffset:$n},right:{points:["cl","cr"],overflow:En,offset:[4,0],targetOffset:$n},top:{points:["bc","tc"],overflow:On,offset:[0,-4],targetOffset:$n},bottom:{points:["tc","bc"],overflow:On,offset:[0,4],targetOffset:$n},topLeft:{points:["bl","tl"],overflow:On,offset:[0,-4],targetOffset:$n},leftTop:{points:["tr","tl"],overflow:En,offset:[-4,0],targetOffset:$n},topRight:{points:["br","tr"],overflow:On,offset:[0,-4],targetOffset:$n},rightTop:{points:["tl","tr"],overflow:En,offset:[4,0],targetOffset:$n},bottomRight:{points:["tr","br"],overflow:On,offset:[0,4],targetOffset:$n},rightBottom:{points:["bl","br"],overflow:En,offset:[4,0],targetOffset:$n},bottomLeft:{points:["tl","bl"],overflow:On,offset:[0,4],targetOffset:$n},leftBottom:{points:["br","bl"],overflow:En,offset:[-4,0],targetOffset:$n}},jn=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow"],Mn=function(t,n){var r=t.overlayClassName,o=t.trigger,i=void 0===o?["hover"]:o,a=t.mouseEnterDelay,s=void 0===a?0:a,c=t.mouseLeaveDelay,l=void 0===c?.1:c,d=t.overlayStyle,p=t.prefixCls,h=void 0===p?"rc-tooltip":p,m=t.children,b=t.onVisibleChange,y=t.afterVisibleChange,w=t.transitionName,x=t.animation,S=t.motion,k=t.placement,C=void 0===k?"right":k,O=t.align,E=void 0===O?{}:O,$=t.destroyTooltipOnHide,A=void 0!==$&&$,j=t.defaultVisible,M=t.getTooltipContainer,P=t.overlayInnerStyle,R=(t.arrowContent,t.overlay),T=t.id,z=t.showArrow,_=void 0===z||z,N=v(t,jn),I=(0,e.useRef)(null);(0,e.useImperativeHandle)(n,(function(){return I.current}));var L=g({},N);return"visible"in t&&(L.popupVisible=t.visible),e.createElement(Cn,f({popupClassName:r,prefixCls:h,popup:function(){return e.createElement(u,{key:"content",prefixCls:h,id:T,overlayInnerStyle:P},R)},action:i,builtinPlacements:An,popupPlacement:C,ref:I,popupAlign:E,getPopupContainer:M,onPopupVisibleChange:b,afterPopupVisibleChange:y,popupTransitionName:w,popupAnimation:x,popupMotion:S,defaultPopupVisible:j,autoDestroy:A,mouseLeaveDelay:l,popupStyle:d,mouseEnterDelay:s,arrow:_},L),m)};const Pn=(0,e.forwardRef)(Mn);function Rn(e){return void 0!==e}const Tn=function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)};var zn="%";function Nn(e){return e.join(zn)}const In=function(){function e(t){ze(this,e),h(this,"instanceId",void 0),h(this,"cache",new Map),this.instanceId=t}return Ne(e,[{key:"get",value:function(e){return this.opGet(Nn(e))}},{key:"opGet",value:function(e){return this.cache.get(e)||null}},{key:"update",value:function(e,t){return this.opUpdate(Nn(e),t)}},{key:"opUpdate",value:function(e,t){var n=t(this.cache.get(e));null===n?this.cache.delete(e):this.cache.set(e,n)}}]),e}();var Ln="data-token-hash",Hn="data-css-hash",Bn="__cssinjs_instance__";var Fn=e.createContext({hashPriority:"low",cache:function(){var e=Math.random().toString(12).slice(2);if("undefined"!=typeof document&&document.head&&document.body){var t=document.body.querySelectorAll("style[".concat(Hn,"]"))||[],n=document.head.firstChild;Array.from(t).forEach((function(t){t[Bn]=t[Bn]||e,t[Bn]===e&&document.head.insertBefore(t,n)}));var r={};Array.from(document.querySelectorAll("style[".concat(Hn,"]"))).forEach((function(t){var n,o=t.getAttribute(Hn);r[o]?t[Bn]===e&&(null===(n=t.parentNode)||void 0===n||n.removeChild(t)):r[o]=!0}))}return new In(e)}(),defaultCache:!0});const Dn=Fn;var Wn=function(){function e(){ze(this,e),h(this,"cache",void 0),h(this,"keys",void 0),h(this,"cacheCallTimes",void 0),this.cache=new Map,this.keys=[],this.cacheCallTimes=0}return Ne(e,[{key:"size",value:function(){return this.keys.length}},{key:"internalGet",value:function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o={map:this.cache};return e.forEach((function(e){var t;o=o?null===(t=o)||void 0===t||null===(t=t.map)||void 0===t?void 0:t.get(e):void 0})),null!==(t=o)&&void 0!==t&&t.value&&r&&(o.value[1]=this.cacheCallTimes++),null===(n=o)||void 0===n?void 0:n.value}},{key:"get",value:function(e){var t;return null===(t=this.internalGet(e,!0))||void 0===t?void 0:t[0]}},{key:"has",value:function(e){return!!this.internalGet(e)}},{key:"set",value:function(t,n){var r=this;if(!this.has(t)){if(this.size()+1>e.MAX_CACHE_SIZE+e.MAX_CACHE_OFFSET){var o=this.keys.reduce((function(e,t){var n=w(e,2)[1];return r.internalGet(t)[1]<n?[t,r.internalGet(t)[1]]:e}),[this.keys[0],this.cacheCallTimes]),i=w(o,1)[0];this.delete(i)}this.keys.push(t)}var a=this.cache;t.forEach((function(e,o){if(o===t.length-1)a.set(e,{value:[n,r.cacheCallTimes++]});else{var i=a.get(e);i?i.map||(i.map=new Map):a.set(e,{map:new Map}),a=a.get(e).map}}))}},{key:"deleteByPath",value:function(e,t){var n,r=e.get(t[0]);if(1===t.length)return r.map?e.set(t[0],{map:r.map}):e.delete(t[0]),null===(n=r.value)||void 0===n?void 0:n[0];var o=this.deleteByPath(r.map,t.slice(1));return r.map&&0!==r.map.size||r.value||e.delete(t[0]),o}},{key:"delete",value:function(e){if(this.has(e))return this.keys=this.keys.filter((function(t){return!function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,e)})),this.deleteByPath(this.cache,e)}}]),e}();h(Wn,"MAX_CACHE_SIZE",20),h(Wn,"MAX_CACHE_OFFSET",5);var Xn=0,qn=function(){function e(t){ze(this,e),h(this,"derivatives",void 0),h(this,"id",void 0),this.derivatives=Array.isArray(t)?t:[t],this.id=Xn,0===t.length&&t.length,Xn+=1}return Ne(e,[{key:"getDerivativeToken",value:function(e){return this.derivatives.reduce((function(t,n){return n(e,t)}),void 0)}}]),e}(),Vn=new Wn,Gn=new WeakMap,Yn={},Un=new WeakMap;function Zn(e){var t=Un.get(e)||"";return t||(Object.keys(e).forEach((function(n){var r=e[n];t+=n,r instanceof qn?t+=r.id:r&&"object"===d(r)?t+=Zn(r):t+=r})),Un.set(e,t)),t}function Kn(e,t){return Tn("".concat(t,"_").concat(Zn(e)))}var Qn="random-".concat(Date.now(),"-").concat(Math.random()).replace(/\./g,""),Jn="_bAmBoO_";var er=void 0,tr=k();function nr(e){return"number"==typeof e?"".concat(e,"px"):e}function rr(e,t,n){if(arguments.length>4&&void 0!==arguments[4]&&arguments[4])return e;var r=g(g({},arguments.length>3&&void 0!==arguments[3]?arguments[3]:{}),{},h(h({},Ln,t),Hn,n)),o=Object.keys(r).map((function(e){var t=r[e];return t?"".concat(e,'="').concat(t,'"'):null})).filter((function(e){return e})).join(" ");return"<style ".concat(o,">").concat(e,"</style>")}var or=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"--".concat(t?"".concat(t,"-"):"").concat(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").replace(/([A-Z]+)([A-Z][a-z0-9]+)/g,"$1-$2").replace(/([a-z])([A-Z0-9])/g,"$1-$2").toLowerCase()},ir=function(e,t,n){return Object.keys(e).length?".".concat(t).concat(null!=n&&n.scope?".".concat(n.scope):"","{").concat(Object.entries(e).map((function(e){var t=w(e,2),n=t[0],r=t[1];return"".concat(n,":").concat(r,";")})).join(""),"}"):""},ar=function(e,t,n){var r={},o={};return Object.entries(e).forEach((function(e){var t,i,a=w(e,2),s=a[0],c=a[1];if(null!=n&&null!==(t=n.preserve)&&void 0!==t&&t[s])o[s]=c;else if(!("string"!=typeof c&&"number"!=typeof c||null!=n&&null!==(i=n.ignore)&&void 0!==i&&i[s])){var l,u=or(s,null==n?void 0:n.prefix);r[u]="number"!=typeof c||null!=n&&null!==(l=n.unitless)&&void 0!==l&&l[s]?String(c):"".concat(c,"px"),o[s]="var(".concat(u,")")}})),[o,ir(r,t,{scope:null==n?void 0:n.scope})]},sr=g({},e).useInsertionEffect;const cr=sr?function(e,t,n){return sr((function(){return e(),t()}),n)}:function(t,n,r){e.useMemo(t,r),F((function(){return n(!0)}),r)},lr=void 0!==g({},e).useInsertionEffect?function(t){var n=[],r=!1;return e.useEffect((function(){return r=!1,function(){r=!0,n.length&&n.forEach((function(e){return e()}))}}),t),function(e){r||n.push(e)}}:function(){return function(e){e()}},ur=function(){return!1};function fr(t,n,r,o,i){var a=e.useContext(Dn).cache,s=Nn([t].concat(I(n))),c=lr([s]),l=(ur(),function(e){a.opUpdate(s,(function(t){var n=w(t||[void 0,void 0],2),o=n[0],i=[void 0===o?0:o,n[1]||r()];return e?e(i):i}))});e.useMemo((function(){l()}),[s]);var u=a.opGet(s)[1];return cr((function(){null==i||i(u)}),(function(e){return l((function(t){var n=w(t,2),r=n[0],o=n[1];return e&&0===r&&(null==i||i(u)),[r+1,o]})),function(){a.opUpdate(s,(function(t){var n=w(t||[],2),r=n[0],i=void 0===r?0:r,l=n[1];return 0==i-1?(c((function(){!e&&a.opGet(s)||null==o||o(l,!1)})),null):[i-1,l]}))}}),[s]),u}var dr={},pr="css",hr=new Map,mr=0;var gr=function(e,t,n,r){var o=g(g({},n.getDerivativeToken(e)),t);return r&&(o=r(o)),o},vr="token";function br(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=(0,e.useContext)(Dn),i=o.cache.instanceId,a=o.container,s=r.salt,c=void 0===s?"":s,l=r.override,u=void 0===l?dr:l,f=r.formatToken,d=r.getComputedToken,p=r.cssVar,h=function(e,t){for(var r=Gn,o=0;o<t.length;o+=1){var i=t[o];r.has(i)||r.set(i,new WeakMap),r=r.get(i)}return r.has(Yn)||r.set(Yn,Object.assign.apply(Object,[{}].concat(I(n)))),r.get(Yn)}(0,n),m=Zn(h),v=Zn(u),b=p?Zn(p):"",y=fr(vr,[c,t.id,m,v,b],(function(){var e,n=d?d(h,u,t):gr(h,u,t,f),r=g({},n),o="";if(p){var i=w(ar(n,p.key,{prefix:p.prefix,ignore:p.ignore,unitless:p.unitless,preserve:p.preserve}),2);n=i[0],o=i[1]}var a=Kn(n,c);n._tokenKey=a,r._tokenKey=Kn(r,c);var s=null!==(e=null==p?void 0:p.key)&&void 0!==e?e:a;n._themeKey=s,function(e){hr.set(e,(hr.get(e)||0)+1)}(s);var l="".concat(pr,"-").concat(Tn(a));return n._hashId=l,[n,l,r,o,(null==p?void 0:p.key)||""]}),(function(e){!function(e,t){hr.set(e,(hr.get(e)||0)-1);var n=Array.from(hr.keys()),r=n.filter((function(e){return(hr.get(e)||0)<=0}));n.length-r.length>mr&&r.forEach((function(e){!function(e,t){"undefined"!=typeof document&&document.querySelectorAll("style[".concat(Ln,'="').concat(e,'"]')).forEach((function(e){var n;e[Bn]===t&&(null===(n=e.parentNode)||void 0===n||n.removeChild(e))}))}(e,t),hr.delete(e)}))}(e[0]._themeKey,i)}),(function(e){var t=w(e,4),n=t[0],r=t[3];if(p&&r){var o=J(r,Tn("css-variables-".concat(n._themeKey)),{mark:Hn,prepend:"queue",attachTo:a,priority:-999});o[Bn]=i,o.setAttribute(Ln,n._themeKey)}}));return y}const yr={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};var wr="comm",xr="rule",Sr="decl",kr="@import",Cr="@keyframes",Or="@layer",Er=Math.abs,$r=String.fromCharCode;function Ar(e){return e.trim()}function jr(e,t,n){return e.replace(t,n)}function Mr(e,t,n){return e.indexOf(t,n)}function Pr(e,t){return 0|e.charCodeAt(t)}function Rr(e,t,n){return e.slice(t,n)}function Tr(e){return e.length}function zr(e,t){return t.push(e),e}function _r(e,t){for(var n="",r=0;r<e.length;r++)n+=t(e[r],r,e,t)||"";return n}function Nr(e,t,n,r){switch(e.type){case Or:if(e.children.length)break;case kr:case Sr:return e.return=e.return||e.value;case wr:return"";case Cr:return e.return=e.value+"{"+_r(e.children,r)+"}";case xr:if(!Tr(e.value=e.props.join(",")))return""}return Tr(n=_r(e.children,r))?e.return=e.value+"{"+n+"}":""}Object.assign;var Ir=1,Lr=1,Hr=0,Br=0,Fr=0,Dr="";function Wr(e,t,n,r,o,i,a,s){return{value:e,root:t,parent:n,type:r,props:o,children:i,line:Ir,column:Lr,length:a,return:"",siblings:s}}function Xr(){return Fr=Br>0?Pr(Dr,--Br):0,Lr--,10===Fr&&(Lr=1,Ir--),Fr}function qr(){return Fr=Br<Hr?Pr(Dr,Br++):0,Lr++,10===Fr&&(Lr=1,Ir++),Fr}function Vr(){return Pr(Dr,Br)}function Gr(){return Br}function Yr(e,t){return Rr(Dr,e,t)}function Ur(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Zr(e){return Ar(Yr(Br-1,Jr(91===e?e+2:40===e?e+1:e)))}function Kr(e){for(;(Fr=Vr())&&Fr<33;)qr();return Ur(e)>2||Ur(Fr)>3?"":" "}function Qr(e,t){for(;--t&&qr()&&!(Fr<48||Fr>102||Fr>57&&Fr<65||Fr>70&&Fr<97););return Yr(e,Gr()+(t<6&&32==Vr()&&32==qr()))}function Jr(e){for(;qr();)switch(Fr){case e:return Br;case 34:case 39:34!==e&&39!==e&&Jr(Fr);break;case 40:41===e&&Jr(e);break;case 92:qr()}return Br}function eo(e,t){for(;qr()&&e+Fr!==57&&(e+Fr!==84||47!==Vr()););return"/*"+Yr(t,Br-1)+"*"+$r(47===e?e:qr())}function to(e){for(;!Ur(Vr());)qr();return Yr(e,Br)}function no(e){return function(e){return Dr="",e}(ro("",null,null,null,[""],e=function(e){return Ir=Lr=1,Hr=Tr(Dr=e),Br=0,[]}(e),0,[0],e))}function ro(e,t,n,r,o,i,a,s,c){for(var l=0,u=0,f=a,d=0,p=0,h=0,m=1,g=1,v=1,b=0,y="",w=o,x=i,S=r,k=y;g;)switch(h=b,b=qr()){case 40:if(108!=h&&58==Pr(k,f-1)){-1!=Mr(k+=jr(Zr(b),"&","&\f"),"&\f",Er(l?s[l-1]:0))&&(v=-1);break}case 34:case 39:case 91:k+=Zr(b);break;case 9:case 10:case 13:case 32:k+=Kr(h);break;case 92:k+=Qr(Gr()-1,7);continue;case 47:switch(Vr()){case 42:case 47:zr(io(eo(qr(),Gr()),t,n,c),c);break;default:k+="/"}break;case 123*m:s[l++]=Tr(k)*v;case 125*m:case 59:case 0:switch(b){case 0:case 125:g=0;case 59+u:-1==v&&(k=jr(k,/\f/g,"")),p>0&&Tr(k)-f&&zr(p>32?ao(k+";",r,n,f-1,c):ao(jr(k," ","")+";",r,n,f-2,c),c);break;case 59:k+=";";default:if(zr(S=oo(k,t,n,l,u,o,s,y,w=[],x=[],f,i),i),123===b)if(0===u)ro(k,t,S,S,w,i,f,s,x);else switch(99===d&&110===Pr(k,3)?100:d){case 100:case 108:case 109:case 115:ro(e,S,S,r&&zr(oo(e,S,S,0,0,o,s,y,o,w=[],f,x),x),o,x,f,s,r?w:x);break;default:ro(k,S,S,S,[""],x,0,s,x)}}l=u=p=0,m=v=1,y=k="",f=a;break;case 58:f=1+Tr(k),p=h;default:if(m<1)if(123==b)--m;else if(125==b&&0==m++&&125==Xr())continue;switch(k+=$r(b),b*m){case 38:v=u>0?1:(k+="\f",-1);break;case 44:s[l++]=(Tr(k)-1)*v,v=1;break;case 64:45===Vr()&&(k+=Zr(qr())),d=Vr(),u=f=Tr(y=k+=to(Gr())),b++;break;case 45:45===h&&2==Tr(k)&&(m=0)}}return i}function oo(e,t,n,r,o,i,a,s,c,l,u,f){for(var d=o-1,p=0===o?i:[""],h=function(e){return e.length}(p),m=0,g=0,v=0;m<r;++m)for(var b=0,y=Rr(e,d+1,d=Er(g=a[m])),w=e;b<h;++b)(w=Ar(g>0?p[b]+" "+y:jr(y,/&\f/g,p[b])))&&(c[v++]=w);return Wr(e,t,n,0===o?xr:s,c,l,u,f)}function io(e,t,n,r){return Wr(e,t,n,wr,$r(Fr),Rr(e,2,-2),0,r)}function ao(e,t,n,r,o){return Wr(e,t,n,Sr,Rr(e,0,r),Rr(e,r+1,-1),r,o)}var so,co="data-ant-cssinjs-cache-path",lo="_FILE_STYLE__",uo=!0;var fo="_multi_value_";function po(e){return _r(no(e),Nr).replace(/\{%%%\:[^;];}/g,";")}var ho=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{root:!0,parentSelectors:[]},o=r.root,i=r.injectHash,a=r.parentSelectors,s=n.hashId,c=n.layer,l=(n.path,n.hashPriority),u=n.transformers,f=void 0===u?[]:u,p=(n.linters,""),h={};function m(t){var r=t.getName(s);if(!h[r]){var o=w(e(t.style,n,{root:!1,parentSelectors:a}),1)[0];h[r]="@keyframes ".concat(t.getName(s)).concat(o)}}var v=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return t.forEach((function(t){Array.isArray(t)?e(t,n):t&&n.push(t)})),n}(Array.isArray(t)?t:[t]);if(v.forEach((function(t){var r="string"!=typeof t||o?t:{};if("string"==typeof r)p+="".concat(r,"\n");else if(r._keyframe)m(r);else{var c=f.reduce((function(e,t){var n;return(null==t||null===(n=t.visit)||void 0===n?void 0:n.call(t,e))||e}),r);Object.keys(c).forEach((function(t){var r=c[t];if("object"!==d(r)||!r||"animationName"===t&&r._keyframe||function(e){return"object"===d(e)&&e&&("_skip_check_"in e||fo in e)}(r)){var u;function C(e,t){var n=e.replace(/[A-Z]/g,(function(e){return"-".concat(e.toLowerCase())})),r=t;yr[e]||"number"!=typeof r||0===r||(r="".concat(r,"px")),"animationName"===e&&null!=t&&t._keyframe&&(m(t),r=t.getName(s)),p+="".concat(n,":").concat(r,";")}var f=null!==(u=null==r?void 0:r.value)&&void 0!==u?u:r;"object"===d(r)&&null!=r&&r[fo]&&Array.isArray(f)?f.forEach((function(e){C(t,e)})):C(t,f)}else{var v=!1,b=t.trim(),y=!1;(o||i)&&s?b.startsWith("@")?v=!0:b=function(e,t,n){if(!t)return e;var r=".".concat(t),o="low"===n?":where(".concat(r,")"):r,i=e.split(",").map((function(e){var t,n=e.trim().split(/\s+/),r=n[0]||"",i=(null===(t=r.match(/^\w+/))||void 0===t?void 0:t[0])||"";return[r="".concat(i).concat(o).concat(r.slice(i.length))].concat(I(n.slice(1))).join(" ")}));return i.join(",")}(t,s,l):!o||s||"&"!==b&&""!==b||(b="",y=!0);var x=w(e(r,n,{root:y,injectHash:v,parentSelectors:[].concat(I(a),[b])}),2),S=x[0],k=x[1];h=g(g({},h),k),p+="".concat(b).concat(S)}}))}})),o){if(c&&(void 0===er&&(er=function(e,t,n){if(k()){var r,o;J(e,Qn);var i=document.createElement("div");i.style.position="fixed",i.style.left="0",i.style.top="0",null==t||t(i),document.body.appendChild(i);var a=n?n(i):null===(r=getComputedStyle(i).content)||void 0===r?void 0:r.includes(Jn);return null===(o=i.parentNode)||void 0===o||o.removeChild(i),Q(Qn),a}return!1}("@layer ".concat(Qn," { .").concat(Qn,' { content: "').concat(Jn,'"!important; } }'),(function(e){e.className=Qn}))),er)){var b=c.split(","),y=b[b.length-1].trim();p="@layer ".concat(y," {").concat(p,"}"),b.length>1&&(p="@layer ".concat(c,"{%%%:%}").concat(p))}}else p="{".concat(p,"}");return[p,h]};function mo(e,t){return Tn("".concat(e.join("%")).concat(t))}function go(){return null}var vo="style";function bo(t,n){var r=t.token,o=t.path,i=t.hashId,a=t.layer,s=t.nonce,c=t.clientOnly,l=t.order,u=void 0===l?0:l,d=e.useContext(Dn),p=d.autoClear,m=(d.mock,d.defaultCache),g=d.hashPriority,v=d.container,b=d.ssrInline,y=d.transformers,x=d.linters,S=d.cache,C=r._tokenKey,O=[C].concat(I(o)),E=tr,$=fr(vo,O,(function(){var e=O.join("|");if(function(e){return function(){if(!so&&(so={},k())){var e=document.createElement("div");e.className=co,e.style.position="fixed",e.style.visibility="hidden",e.style.top="-9999px",document.body.appendChild(e);var t=getComputedStyle(e).content||"";(t=t.replace(/^"/,"").replace(/"$/,"")).split(";").forEach((function(e){var t=w(e.split(":"),2),n=t[0],r=t[1];so[n]=r}));var n,r=document.querySelector("style[".concat(co,"]"));r&&(uo=!1,null===(n=r.parentNode)||void 0===n||n.removeChild(r)),document.body.removeChild(e)}}(),!!so[e]}(e)){var t=function(e){var t=so[e],n=null;if(t&&k())if(uo)n=lo;else{var r=document.querySelector("style[".concat(Hn,'="').concat(so[e],'"]'));r?n=r.innerHTML:delete so[e]}return[n,t]}(e),r=w(t,2),s=r[0],l=r[1];if(s)return[s,C,l,{},c,u]}var f=n(),d=w(ho(f,{hashId:i,hashPriority:g,layer:a,path:o.join("-"),transformers:y,linters:x}),2),p=d[0],h=d[1],m=po(p),v=mo(O,m);return[m,C,v,h,c,u]}),(function(e,t){var n=w(e,3)[2];(t||p)&&tr&&Q(n,{mark:Hn})}),(function(e){var t=w(e,4),n=t[0],r=(t[1],t[2]),o=t[3];if(E&&n!==lo){var i={mark:Hn,prepend:"queue",attachTo:v,priority:u},a="function"==typeof s?s():s;a&&(i.csp={nonce:a});var c=J(n,r,i);c[Bn]=S.instanceId,c.setAttribute(Ln,C),Object.keys(o).forEach((function(e){J(po(o[e]),"_effect-".concat(e),i)}))}})),A=w($,3),j=A[0],M=A[1],P=A[2];return function(t){var n;return n=b&&!E&&m?e.createElement("style",f({},h(h({},Ln,M),Hn,P),{dangerouslySetInnerHTML:{__html:j}})):e.createElement(go,null),e.createElement(e.Fragment,null,n,t)}}var yo="cssVar";h(h(h({},vo,(function(e,t,n){var r=w(e,6),o=r[0],i=r[1],a=r[2],s=r[3],c=r[4],l=r[5],u=(n||{}).plain;if(c)return null;var f=o,d={"data-rc-order":"prependQueue","data-rc-priority":"".concat(l)};return f=rr(o,i,a,d,u),s&&Object.keys(s).forEach((function(e){if(!t[e]){t[e]=!0;var n=po(s[e]);f+=rr(n,i,"_effect-".concat(e),d,u)}})),[l,a,f]})),vr,(function(e,t,n){var r=w(e,5),o=r[2],i=r[3],a=r[4],s=(n||{}).plain;if(!i)return null;var c=o._tokenKey;return[-999,c,rr(i,a,c,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s)]})),yo,(function(e,t,n){var r=w(e,4),o=r[1],i=r[2],a=r[3],s=(n||{}).plain;return o?[-999,i,rr(o,a,i,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s)]:null}));var wo=function(){function e(t,n){ze(this,e),h(this,"name",void 0),h(this,"style",void 0),h(this,"_keyframe",!0),this.name=t,this.style=n}return Ne(e,[{key:"getName",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e?"".concat(e,"-").concat(this.name):this.name}}]),e}();const xo=wo;function So(e){return e.notSplit=!0,e}So(["borderTop","borderBottom"]),So(["borderTop"]),So(["borderBottom"]),So(["borderLeft","borderRight"]),So(["borderLeft"]),So(["borderRight"]);const ko="5.15.4";function Co(e,t){(function(e){return"string"==typeof e&&-1!==e.indexOf(".")&&1===parseFloat(e)})(e)&&(e="100%");var n=function(e){return"string"==typeof e&&-1!==e.indexOf("%")}(e);return e=360===t?e:Math.min(t,Math.max(0,parseFloat(e))),n&&(e=parseInt(String(e*t),10)/100),Math.abs(e-t)<1e-6?1:e=360===t?(e<0?e%t+t:e%t)/parseFloat(String(t)):e%t/parseFloat(String(t))}function Oo(e){return Math.min(1,Math.max(0,e))}function Eo(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function $o(e){return e<=1?"".concat(100*Number(e),"%"):e}function Ao(e){return 1===e.length?"0"+e:String(e)}function jo(e,t,n){e=Co(e,255),t=Co(t,255),n=Co(n,255);var r=Math.max(e,t,n),o=Math.min(e,t,n),i=0,a=0,s=(r+o)/2;if(r===o)a=0,i=0;else{var c=r-o;switch(a=s>.5?c/(2-r-o):c/(r+o),r){case e:i=(t-n)/c+(t<n?6:0);break;case t:i=(n-e)/c+2;break;case n:i=(e-t)/c+4}i/=6}return{h:i,s:a,l:s}}function Mo(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*n*(t-e):n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Po(e,t,n){e=Co(e,255),t=Co(t,255),n=Co(n,255);var r=Math.max(e,t,n),o=Math.min(e,t,n),i=0,a=r,s=r-o,c=0===r?0:s/r;if(r===o)i=0;else{switch(r){case e:i=(t-n)/s+(t<n?6:0);break;case t:i=(n-e)/s+2;break;case n:i=(e-t)/s+4}i/=6}return{h:i,s:c,v:a}}function Ro(e,t,n,r){var o=[Ao(Math.round(e).toString(16)),Ao(Math.round(t).toString(16)),Ao(Math.round(n).toString(16))];return r&&o[0].startsWith(o[0].charAt(1))&&o[1].startsWith(o[1].charAt(1))&&o[2].startsWith(o[2].charAt(1))?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0):o.join("")}function To(e){return zo(e)/255}function zo(e){return parseInt(e,16)}var _o={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function No(e){var t={r:0,g:0,b:0},n=1,r=null,o=null,i=null,a=!1,s=!1;return"string"==typeof e&&(e=function(e){if(0===(e=e.trim().toLowerCase()).length)return!1;var t=!1;if(_o[e])e=_o[e],t=!0;else if("transparent"===e)return{r:0,g:0,b:0,a:0,format:"name"};var n=Bo.rgb.exec(e);return n?{r:n[1],g:n[2],b:n[3]}:(n=Bo.rgba.exec(e))?{r:n[1],g:n[2],b:n[3],a:n[4]}:(n=Bo.hsl.exec(e))?{h:n[1],s:n[2],l:n[3]}:(n=Bo.hsla.exec(e))?{h:n[1],s:n[2],l:n[3],a:n[4]}:(n=Bo.hsv.exec(e))?{h:n[1],s:n[2],v:n[3]}:(n=Bo.hsva.exec(e))?{h:n[1],s:n[2],v:n[3],a:n[4]}:(n=Bo.hex8.exec(e))?{r:zo(n[1]),g:zo(n[2]),b:zo(n[3]),a:To(n[4]),format:t?"name":"hex8"}:(n=Bo.hex6.exec(e))?{r:zo(n[1]),g:zo(n[2]),b:zo(n[3]),format:t?"name":"hex"}:(n=Bo.hex4.exec(e))?{r:zo(n[1]+n[1]),g:zo(n[2]+n[2]),b:zo(n[3]+n[3]),a:To(n[4]+n[4]),format:t?"name":"hex8"}:!!(n=Bo.hex3.exec(e))&&{r:zo(n[1]+n[1]),g:zo(n[2]+n[2]),b:zo(n[3]+n[3]),format:t?"name":"hex"}}(e)),"object"==typeof e&&(Fo(e.r)&&Fo(e.g)&&Fo(e.b)?(t=function(e,t,n){return{r:255*Co(e,255),g:255*Co(t,255),b:255*Co(n,255)}}(e.r,e.g,e.b),a=!0,s="%"===String(e.r).substr(-1)?"prgb":"rgb"):Fo(e.h)&&Fo(e.s)&&Fo(e.v)?(r=$o(e.s),o=$o(e.v),t=function(e,t,n){e=6*Co(e,360),t=Co(t,100),n=Co(n,100);var r=Math.floor(e),o=e-r,i=n*(1-t),a=n*(1-o*t),s=n*(1-(1-o)*t),c=r%6;return{r:255*[n,a,i,i,s,n][c],g:255*[s,n,n,a,i,i][c],b:255*[i,i,s,n,n,a][c]}}(e.h,r,o),a=!0,s="hsv"):Fo(e.h)&&Fo(e.s)&&Fo(e.l)&&(r=$o(e.s),i=$o(e.l),t=function(e,t,n){var r,o,i;if(e=Co(e,360),t=Co(t,100),n=Co(n,100),0===t)o=n,i=n,r=n;else{var a=n<.5?n*(1+t):n+t-n*t,s=2*n-a;r=Mo(s,a,e+1/3),o=Mo(s,a,e),i=Mo(s,a,e-1/3)}return{r:255*r,g:255*o,b:255*i}}(e.h,r,i),a=!0,s="hsl"),Object.prototype.hasOwnProperty.call(e,"a")&&(n=e.a)),n=Eo(n),{ok:a,format:e.format||s,r:Math.min(255,Math.max(t.r,0)),g:Math.min(255,Math.max(t.g,0)),b:Math.min(255,Math.max(t.b,0)),a:n}}var Io="(?:".concat("[-\\+]?\\d*\\.\\d+%?",")|(?:").concat("[-\\+]?\\d+%?",")"),Lo="[\\s|\\(]+(".concat(Io,")[,|\\s]+(").concat(Io,")[,|\\s]+(").concat(Io,")\\s*\\)?"),Ho="[\\s|\\(]+(".concat(Io,")[,|\\s]+(").concat(Io,")[,|\\s]+(").concat(Io,")[,|\\s]+(").concat(Io,")\\s*\\)?"),Bo={CSS_UNIT:new RegExp(Io),rgb:new RegExp("rgb"+Lo),rgba:new RegExp("rgba"+Ho),hsl:new RegExp("hsl"+Lo),hsla:new RegExp("hsla"+Ho),hsv:new RegExp("hsv"+Lo),hsva:new RegExp("hsva"+Ho),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function Fo(e){return Boolean(Bo.CSS_UNIT.exec(String(e)))}var Do=2,Wo=.16,Xo=.05,qo=.05,Vo=.15,Go=5,Yo=4,Uo=[{index:7,opacity:.15},{index:6,opacity:.25},{index:5,opacity:.3},{index:5,opacity:.45},{index:5,opacity:.65},{index:5,opacity:.85},{index:4,opacity:.9},{index:3,opacity:.95},{index:2,opacity:.97},{index:1,opacity:.98}];function Zo(e){var t=Po(e.r,e.g,e.b);return{h:360*t.h,s:t.s,v:t.v}}function Ko(e){var t=e.r,n=e.g,r=e.b;return"#".concat(Ro(t,n,r,!1))}function Qo(e,t,n){var r;return(r=Math.round(e.h)>=60&&Math.round(e.h)<=240?n?Math.round(e.h)-Do*t:Math.round(e.h)+Do*t:n?Math.round(e.h)+Do*t:Math.round(e.h)-Do*t)<0?r+=360:r>=360&&(r-=360),r}function Jo(e,t,n){return 0===e.h&&0===e.s?e.s:((r=n?e.s-Wo*t:t===Yo?e.s+Wo:e.s+Xo*t)>1&&(r=1),n&&t===Go&&r>.1&&(r=.1),r<.06&&(r=.06),Number(r.toFixed(2)));var r}function ei(e,t,n){var r;return(r=n?e.v+qo*t:e.v-Vo*t)>1&&(r=1),Number(r.toFixed(2))}function ti(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],r=No(e),o=Go;o>0;o-=1){var i=Zo(r),a=Ko(No({h:Qo(i,o,!0),s:Jo(i,o,!0),v:ei(i,o,!0)}));n.push(a)}n.push(Ko(r));for(var s=1;s<=Yo;s+=1){var c=Zo(r),l=Ko(No({h:Qo(c,s),s:Jo(c,s),v:ei(c,s)}));n.push(l)}return"dark"===t.theme?Uo.map((function(e){var r,o,i,a=e.index,s=e.opacity;return Ko((r=No(t.backgroundColor||"#141414"),i=100*s/100,{r:((o=No(n[a])).r-r.r)*i+r.r,g:(o.g-r.g)*i+r.g,b:(o.b-r.b)*i+r.b}))})):n}var ni={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},ri={},oi={};Object.keys(ni).forEach((function(e){ri[e]=ti(ni[e]),ri[e].primary=ri[e][5],oi[e]=ti(ni[e],{theme:"dark",backgroundColor:"#141414"}),oi[e].primary=oi[e][5]})),ri.red,ri.volcano,ri.gold,ri.orange,ri.yellow,ri.lime,ri.green,ri.cyan;var ii=ri.blue;ri.geekblue,ri.purple,ri.magenta,ri.grey,ri.grey;const ai={blue:"#1677ff",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#eb2f96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911"},si=Object.assign(Object.assign({},ai),{colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677ff",colorLink:"",colorTextBase:"",colorBgBase:"",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,\n'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\n'Noto Color Emoji'",fontFamilyCode:"'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInBack:"cubic-bezier(0.71, -0.46, 0.88, 0.6)",motionEaseInQuint:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:6,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,motion:!0});var ci=function(){function e(t,n){var r;if(void 0===t&&(t=""),void 0===n&&(n={}),t instanceof e)return t;"number"==typeof t&&(t=function(e){return{r:e>>16,g:(65280&e)>>8,b:255&e}}(t)),this.originalInput=t;var o=No(t);this.originalInput=t,this.r=o.r,this.g=o.g,this.b=o.b,this.a=o.a,this.roundA=Math.round(100*this.a)/100,this.format=null!==(r=n.format)&&void 0!==r?r:o.format,this.gradientType=n.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=o.ok}return e.prototype.isDark=function(){return this.getBrightness()<128},e.prototype.isLight=function(){return!this.isDark()},e.prototype.getBrightness=function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},e.prototype.getLuminance=function(){var e=this.toRgb(),t=e.r/255,n=e.g/255,r=e.b/255;return.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))+.0722*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))},e.prototype.getAlpha=function(){return this.a},e.prototype.setAlpha=function(e){return this.a=Eo(e),this.roundA=Math.round(100*this.a)/100,this},e.prototype.isMonochrome=function(){return 0===this.toHsl().s},e.prototype.toHsv=function(){var e=Po(this.r,this.g,this.b);return{h:360*e.h,s:e.s,v:e.v,a:this.a}},e.prototype.toHsvString=function(){var e=Po(this.r,this.g,this.b),t=Math.round(360*e.h),n=Math.round(100*e.s),r=Math.round(100*e.v);return 1===this.a?"hsv(".concat(t,", ").concat(n,"%, ").concat(r,"%)"):"hsva(".concat(t,", ").concat(n,"%, ").concat(r,"%, ").concat(this.roundA,")")},e.prototype.toHsl=function(){var e=jo(this.r,this.g,this.b);return{h:360*e.h,s:e.s,l:e.l,a:this.a}},e.prototype.toHslString=function(){var e=jo(this.r,this.g,this.b),t=Math.round(360*e.h),n=Math.round(100*e.s),r=Math.round(100*e.l);return 1===this.a?"hsl(".concat(t,", ").concat(n,"%, ").concat(r,"%)"):"hsla(".concat(t,", ").concat(n,"%, ").concat(r,"%, ").concat(this.roundA,")")},e.prototype.toHex=function(e){return void 0===e&&(e=!1),Ro(this.r,this.g,this.b,e)},e.prototype.toHexString=function(e){return void 0===e&&(e=!1),"#"+this.toHex(e)},e.prototype.toHex8=function(e){return void 0===e&&(e=!1),function(e,t,n,r,o){var i,a=[Ao(Math.round(e).toString(16)),Ao(Math.round(t).toString(16)),Ao(Math.round(n).toString(16)),Ao((i=r,Math.round(255*parseFloat(i)).toString(16)))];return o&&a[0].startsWith(a[0].charAt(1))&&a[1].startsWith(a[1].charAt(1))&&a[2].startsWith(a[2].charAt(1))&&a[3].startsWith(a[3].charAt(1))?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0):a.join("")}(this.r,this.g,this.b,this.a,e)},e.prototype.toHex8String=function(e){return void 0===e&&(e=!1),"#"+this.toHex8(e)},e.prototype.toHexShortString=function(e){return void 0===e&&(e=!1),1===this.a?this.toHexString(e):this.toHex8String(e)},e.prototype.toRgb=function(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}},e.prototype.toRgbString=function(){var e=Math.round(this.r),t=Math.round(this.g),n=Math.round(this.b);return 1===this.a?"rgb(".concat(e,", ").concat(t,", ").concat(n,")"):"rgba(".concat(e,", ").concat(t,", ").concat(n,", ").concat(this.roundA,")")},e.prototype.toPercentageRgb=function(){var e=function(e){return"".concat(Math.round(100*Co(e,255)),"%")};return{r:e(this.r),g:e(this.g),b:e(this.b),a:this.a}},e.prototype.toPercentageRgbString=function(){var e=function(e){return Math.round(100*Co(e,255))};return 1===this.a?"rgb(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%)"):"rgba(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%, ").concat(this.roundA,")")},e.prototype.toName=function(){if(0===this.a)return"transparent";if(this.a<1)return!1;for(var e="#"+Ro(this.r,this.g,this.b,!1),t=0,n=Object.entries(_o);t<n.length;t++){var r=n[t],o=r[0];if(e===r[1])return o}return!1},e.prototype.toString=function(e){var t=Boolean(e);e=null!=e?e:this.format;var n=!1,r=this.a<1&&this.a>=0;return t||!r||!e.startsWith("hex")&&"name"!==e?("rgb"===e&&(n=this.toRgbString()),"prgb"===e&&(n=this.toPercentageRgbString()),"hex"!==e&&"hex6"!==e||(n=this.toHexString()),"hex3"===e&&(n=this.toHexString(!0)),"hex4"===e&&(n=this.toHex8String(!0)),"hex8"===e&&(n=this.toHex8String()),"name"===e&&(n=this.toName()),"hsl"===e&&(n=this.toHslString()),"hsv"===e&&(n=this.toHsvString()),n||this.toHexString()):"name"===e&&0===this.a?this.toName():this.toRgbString()},e.prototype.toNumber=function(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)},e.prototype.clone=function(){return new e(this.toString())},e.prototype.lighten=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.l+=t/100,n.l=Oo(n.l),new e(n)},e.prototype.brighten=function(t){void 0===t&&(t=10);var n=this.toRgb();return n.r=Math.max(0,Math.min(255,n.r-Math.round(-t/100*255))),n.g=Math.max(0,Math.min(255,n.g-Math.round(-t/100*255))),n.b=Math.max(0,Math.min(255,n.b-Math.round(-t/100*255))),new e(n)},e.prototype.darken=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.l-=t/100,n.l=Oo(n.l),new e(n)},e.prototype.tint=function(e){return void 0===e&&(e=10),this.mix("white",e)},e.prototype.shade=function(e){return void 0===e&&(e=10),this.mix("black",e)},e.prototype.desaturate=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.s-=t/100,n.s=Oo(n.s),new e(n)},e.prototype.saturate=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.s+=t/100,n.s=Oo(n.s),new e(n)},e.prototype.greyscale=function(){return this.desaturate(100)},e.prototype.spin=function(t){var n=this.toHsl(),r=(n.h+t)%360;return n.h=r<0?360+r:r,new e(n)},e.prototype.mix=function(t,n){void 0===n&&(n=50);var r=this.toRgb(),o=new e(t).toRgb(),i=n/100;return new e({r:(o.r-r.r)*i+r.r,g:(o.g-r.g)*i+r.g,b:(o.b-r.b)*i+r.b,a:(o.a-r.a)*i+r.a})},e.prototype.analogous=function(t,n){void 0===t&&(t=6),void 0===n&&(n=30);var r=this.toHsl(),o=360/n,i=[this];for(r.h=(r.h-(o*t>>1)+720)%360;--t;)r.h=(r.h+o)%360,i.push(new e(r));return i},e.prototype.complement=function(){var t=this.toHsl();return t.h=(t.h+180)%360,new e(t)},e.prototype.monochromatic=function(t){void 0===t&&(t=6);for(var n=this.toHsv(),r=n.h,o=n.s,i=n.v,a=[],s=1/t;t--;)a.push(new e({h:r,s:o,v:i})),i=(i+s)%1;return a},e.prototype.splitcomplement=function(){var t=this.toHsl(),n=t.h;return[this,new e({h:(n+72)%360,s:t.s,l:t.l}),new e({h:(n+216)%360,s:t.s,l:t.l})]},e.prototype.onBackground=function(t){var n=this.toRgb(),r=new e(t).toRgb(),o=n.a+r.a*(1-n.a);return new e({r:(n.r*n.a+r.r*r.a*(1-n.a))/o,g:(n.g*n.a+r.g*r.a*(1-n.a))/o,b:(n.b*n.a+r.b*r.a*(1-n.a))/o,a:o})},e.prototype.triad=function(){return this.polyad(3)},e.prototype.tetrad=function(){return this.polyad(4)},e.prototype.polyad=function(t){for(var n=this.toHsl(),r=n.h,o=[this],i=360/t,a=1;a<t;a++)o.push(new e({h:(r+a*i)%360,s:n.s,l:n.l}));return o},e.prototype.equals=function(t){return this.toRgbString()===new e(t).toRgbString()},e}();const li=(e,t)=>new ci(e).setAlpha(t).toRgbString(),ui=(e,t)=>new ci(e).darken(t).toHexString(),fi=e=>{const t=ti(e);return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[4],6:t[5],7:t[6],8:t[4],9:t[5],10:t[6]}},di=(e,t)=>{const n=e||"#fff",r=t||"#000";return{colorBgBase:n,colorTextBase:r,colorText:li(r,.88),colorTextSecondary:li(r,.65),colorTextTertiary:li(r,.45),colorTextQuaternary:li(r,.25),colorFill:li(r,.15),colorFillSecondary:li(r,.06),colorFillTertiary:li(r,.04),colorFillQuaternary:li(r,.02),colorBgLayout:ui(n,4),colorBgContainer:ui(n,0),colorBgElevated:ui(n,0),colorBgSpotlight:li(r,.85),colorBgBlur:"transparent",colorBorder:ui(n,15),colorBorderSecondary:ui(n,6)}},pi=(hi=function(e){const t=Object.keys(ai).map((t=>{const n=ti(e[t]);return new Array(10).fill(1).reduce(((e,r,o)=>(e[`${t}-${o+1}`]=n[o],e[`${t}${o+1}`]=n[o],e)),{})})).reduce(((e,t)=>Object.assign(Object.assign({},e),t)),{});return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e),t),function(e,t){let{generateColorPalettes:n,generateNeutralColorPalettes:r}=t;const{colorSuccess:o,colorWarning:i,colorError:a,colorInfo:s,colorPrimary:c,colorBgBase:l,colorTextBase:u}=e,f=n(c),d=n(o),p=n(i),h=n(a),m=n(s),g=r(l,u),v=n(e.colorLink||e.colorInfo);return Object.assign(Object.assign({},g),{colorPrimaryBg:f[1],colorPrimaryBgHover:f[2],colorPrimaryBorder:f[3],colorPrimaryBorderHover:f[4],colorPrimaryHover:f[5],colorPrimary:f[6],colorPrimaryActive:f[7],colorPrimaryTextHover:f[8],colorPrimaryText:f[9],colorPrimaryTextActive:f[10],colorSuccessBg:d[1],colorSuccessBgHover:d[2],colorSuccessBorder:d[3],colorSuccessBorderHover:d[4],colorSuccessHover:d[4],colorSuccess:d[6],colorSuccessActive:d[7],colorSuccessTextHover:d[8],colorSuccessText:d[9],colorSuccessTextActive:d[10],colorErrorBg:h[1],colorErrorBgHover:h[2],colorErrorBorder:h[3],colorErrorBorderHover:h[4],colorErrorHover:h[5],colorError:h[6],colorErrorActive:h[7],colorErrorTextHover:h[8],colorErrorText:h[9],colorErrorTextActive:h[10],colorWarningBg:p[1],colorWarningBgHover:p[2],colorWarningBorder:p[3],colorWarningBorderHover:p[4],colorWarningHover:p[4],colorWarning:p[6],colorWarningActive:p[7],colorWarningTextHover:p[8],colorWarningText:p[9],colorWarningTextActive:p[10],colorInfoBg:m[1],colorInfoBgHover:m[2],colorInfoBorder:m[3],colorInfoBorderHover:m[4],colorInfoHover:m[4],colorInfo:m[6],colorInfoActive:m[7],colorInfoTextHover:m[8],colorInfoText:m[9],colorInfoTextActive:m[10],colorLinkHover:v[4],colorLink:v[6],colorLinkActive:v[7],colorBgMask:new ci("#000").setAlpha(.45).toRgbString(),colorWhite:"#fff"})}(e,{generateColorPalettes:fi,generateNeutralColorPalettes:di})),(e=>{const t=function(e){const t=new Array(10).fill(null).map(((t,n)=>{const r=n-1,o=e*Math.pow(2.71828,r/5),i=n>1?Math.floor(o):Math.ceil(o);return 2*Math.floor(i/2)}));return t[1]=e,t.map((e=>{return{size:e,lineHeight:(t=e,(t+8)/t)};var t}))}(e),n=t.map((e=>e.size)),r=t.map((e=>e.lineHeight)),o=n[1],i=n[0],a=n[2],s=r[1],c=r[0],l=r[2];return{fontSizeSM:i,fontSize:o,fontSizeLG:a,fontSizeXL:n[3],fontSizeHeading1:n[6],fontSizeHeading2:n[5],fontSizeHeading3:n[4],fontSizeHeading4:n[3],fontSizeHeading5:n[2],lineHeight:s,lineHeightLG:l,lineHeightSM:c,fontHeight:Math.round(s*o),fontHeightLG:Math.round(l*a),fontHeightSM:Math.round(c*i),lineHeightHeading1:r[6],lineHeightHeading2:r[5],lineHeightHeading3:r[4],lineHeightHeading4:r[3],lineHeightHeading5:r[2]}})(e.fontSize)),function(e){const{sizeUnit:t,sizeStep:n}=e;return{sizeXXL:t*(n+8),sizeXL:t*(n+4),sizeLG:t*(n+2),sizeMD:t*(n+1),sizeMS:t*n,size:t*n,sizeSM:t*(n-1),sizeXS:t*(n-2),sizeXXS:t*(n-3)}}(e)),(e=>{const{controlHeight:t}=e;return{controlHeightSM:.75*t,controlHeightXS:.5*t,controlHeightLG:1.25*t}})(e)),function(e){const{motionUnit:t,motionBase:n,borderRadius:r,lineWidth:o}=e;return Object.assign({motionDurationFast:`${(n+t).toFixed(1)}s`,motionDurationMid:`${(n+2*t).toFixed(1)}s`,motionDurationSlow:`${(n+3*t).toFixed(1)}s`,lineWidthBold:o+1},(e=>{let t=e,n=e,r=e,o=e;return e<6&&e>=5?t=e+1:e<16&&e>=6?t=e+2:e>=16&&(t=16),e<7&&e>=5?n=4:e<8&&e>=7?n=5:e<14&&e>=8?n=6:e<16&&e>=14?n=7:e>=16&&(n=8),e<6&&e>=2?r=1:e>=6&&(r=2),e>4&&e<8?o=4:e>=8&&(o=6),{borderRadius:e,borderRadiusXS:r,borderRadiusSM:n,borderRadiusLG:t,borderRadiusOuter:o}})(r))}(e))},mi=Array.isArray(hi)?hi:[hi],Vn.has(mi)||Vn.set(mi,new qn(mi)),Vn.get(mi));var hi,mi;const gi={token:si,override:{override:si},hashed:!0},vi=t().createContext(gi);function bi(e){return e>=0&&e<=255}const yi=function(e,t){const{r:n,g:r,b:o,a:i}=new ci(e).toRgb();if(i<1)return e;const{r:a,g:s,b:c}=new ci(t).toRgb();for(let e=.01;e<=1;e+=.01){const t=Math.round((n-a*(1-e))/e),i=Math.round((r-s*(1-e))/e),l=Math.round((o-c*(1-e))/e);if(bi(t)&&bi(i)&&bi(l))return new ci({r:t,g:i,b:l,a:Math.round(100*e)/100}).toRgbString()}return new ci({r:n,g:r,b:o,a:1}).toRgbString()};var wi=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function xi(e){const{override:t}=e,n=wi(e,["override"]),r=Object.assign({},t);Object.keys(si).forEach((e=>{delete r[e]}));const o=Object.assign(Object.assign({},n),r);if(!1===o.motion){const e="0s";o.motionDurationFast=e,o.motionDurationMid=e,o.motionDurationSlow=e}return Object.assign(Object.assign(Object.assign({},o),{colorFillContent:o.colorFillSecondary,colorFillContentHover:o.colorFill,colorFillAlter:o.colorFillQuaternary,colorBgContainerDisabled:o.colorFillTertiary,colorBorderBg:o.colorBgContainer,colorSplit:yi(o.colorBorderSecondary,o.colorBgContainer),colorTextPlaceholder:o.colorTextQuaternary,colorTextDisabled:o.colorTextQuaternary,colorTextHeading:o.colorText,colorTextLabel:o.colorTextSecondary,colorTextDescription:o.colorTextTertiary,colorTextLightSolid:o.colorWhite,colorHighlight:o.colorError,colorBgTextHover:o.colorFillSecondary,colorBgTextActive:o.colorFill,colorIcon:o.colorTextTertiary,colorIconHover:o.colorText,colorErrorOutline:yi(o.colorErrorBg,o.colorBgContainer),colorWarningOutline:yi(o.colorWarningBg,o.colorBgContainer),fontSizeIcon:o.fontSizeSM,lineWidthFocus:4*o.lineWidth,lineWidth:o.lineWidth,controlOutlineWidth:2*o.lineWidth,controlInteractiveSize:o.controlHeight/2,controlItemBgHover:o.colorFillTertiary,controlItemBgActive:o.colorPrimaryBg,controlItemBgActiveHover:o.colorPrimaryBgHover,controlItemBgActiveDisabled:o.colorFill,controlTmpOutline:o.colorFillQuaternary,controlOutline:yi(o.colorPrimaryBg,o.colorBgContainer),lineType:o.lineType,borderRadius:o.borderRadius,borderRadiusXS:o.borderRadiusXS,borderRadiusSM:o.borderRadiusSM,borderRadiusLG:o.borderRadiusLG,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:o.sizeXXS,paddingXS:o.sizeXS,paddingSM:o.sizeSM,padding:o.size,paddingMD:o.sizeMD,paddingLG:o.sizeLG,paddingXL:o.sizeXL,paddingContentHorizontalLG:o.sizeLG,paddingContentVerticalLG:o.sizeMS,paddingContentHorizontal:o.sizeMS,paddingContentVertical:o.sizeSM,paddingContentHorizontalSM:o.size,paddingContentVerticalSM:o.sizeXS,marginXXS:o.sizeXXS,marginXS:o.sizeXS,marginSM:o.sizeSM,margin:o.size,marginMD:o.sizeMD,marginLG:o.sizeLG,marginXL:o.sizeXL,marginXXL:o.sizeXXL,boxShadow:"\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowSecondary:"\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowTertiary:"\n      0 1px 2px 0 rgba(0, 0, 0, 0.03),\n      0 1px 6px -1px rgba(0, 0, 0, 0.02),\n      0 2px 4px 0 rgba(0, 0, 0, 0.02)\n    ",screenXS:480,screenXSMin:480,screenXSMax:575,screenSM:576,screenSMMin:576,screenSMMax:767,screenMD:768,screenMDMin:768,screenMDMax:991,screenLG:992,screenLGMin:992,screenLGMax:1199,screenXL:1200,screenXLMin:1200,screenXLMax:1599,screenXXL:1600,screenXXLMin:1600,boxShadowPopoverArrow:"2px 2px 5px rgba(0, 0, 0, 0.05)",boxShadowCard:`\n      0 1px 2px -2px ${new ci("rgba(0, 0, 0, 0.16)").toRgbString()},\n      0 3px 6px 0 ${new ci("rgba(0, 0, 0, 0.12)").toRgbString()},\n      0 5px 12px 4px ${new ci("rgba(0, 0, 0, 0.09)").toRgbString()}\n    `,boxShadowDrawerRight:"\n      -6px 0 16px 0 rgba(0, 0, 0, 0.08),\n      -3px 0 6px -4px rgba(0, 0, 0, 0.12),\n      -9px 0 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowDrawerLeft:"\n      6px 0 16px 0 rgba(0, 0, 0, 0.08),\n      3px 0 6px -4px rgba(0, 0, 0, 0.12),\n      9px 0 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowDrawerUp:"\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowDrawerDown:"\n      0 -6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 -3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 -9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)"}),r)}var Si=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const ki={lineHeight:!0,lineHeightSM:!0,lineHeightLG:!0,lineHeightHeading1:!0,lineHeightHeading2:!0,lineHeightHeading3:!0,lineHeightHeading4:!0,lineHeightHeading5:!0,opacityLoading:!0,fontWeightStrong:!0,zIndexPopupBase:!0,zIndexBase:!0},Ci={size:!0,sizeSM:!0,sizeLG:!0,sizeMD:!0,sizeXS:!0,sizeXXS:!0,sizeMS:!0,sizeXL:!0,sizeXXL:!0,sizeUnit:!0,sizeStep:!0,motionBase:!0,motionUnit:!0},Oi={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},Ei=(e,t,n)=>{const r=n.getDerivativeToken(e),{override:o}=t,i=Si(t,["override"]);let a=Object.assign(Object.assign({},r),{override:o});return a=xi(a),i&&Object.entries(i).forEach((e=>{let[t,n]=e;const{theme:r}=n,o=Si(n,["theme"]);let i=o;r&&(i=Ei(Object.assign(Object.assign({},a),o),{override:o},r)),a[t]=i})),a};function $i(){const{token:e,hashed:n,theme:r,override:o,cssVar:i}=t().useContext(vi),a=`${ko}-${n||""}`,s=r||pi,[c,l,u]=br(s,[si,e],{salt:a,override:o,getComputedToken:Ei,formatToken:xi,cssVar:i&&{prefix:i.prefix,key:i.key,unitless:ki,ignore:Ci,preserve:Oi}});return[s,u,n?l:"",c,i]}const Ai=t().createContext(void 0),ji=100,Mi={Modal:ji,Drawer:ji,Popover:ji,Popconfirm:ji,Tooltip:ji,Tour:ji},Pi={SelectLike:50,Dropdown:50,DatePicker:50,Menu:50,ImagePreview:1};const Ri=(e,t,n)=>void 0!==n?n:`${e}-${t}`;function Ti(e){const{sizePopupArrow:t,borderRadiusXS:n,borderRadiusOuter:r}=e,o=t/2,i=o,a=1*r/Math.sqrt(2),s=o-r*(1-1/Math.sqrt(2)),c=o-n*(1/Math.sqrt(2)),l=r*(Math.sqrt(2)-1)+n*(1/Math.sqrt(2)),u=2*o-c,f=l,d=2*o-a,p=s,h=2*o-0,m=i,g=o*Math.sqrt(2)+r*(Math.sqrt(2)-2),v=r*(Math.sqrt(2)-1);return{arrowShadowWidth:g,arrowPath:`path('M 0 ${i} A ${r} ${r} 0 0 0 ${a} ${s} L ${c} ${l} A ${n} ${n} 0 0 1 ${u} ${f} L ${d} ${p} A ${r} ${r} 0 0 0 ${h} ${m} Z')`,arrowPolygon:`polygon(${v}px 100%, 50% ${v}px, ${2*o-v}px 100%, ${v}px 100%)`}}const zi=(e,t,n)=>{const{sizePopupArrow:r,arrowPolygon:o,arrowPath:i,arrowShadowWidth:a,borderRadiusXS:s,calc:c}=e;return{pointerEvents:"none",width:r,height:r,overflow:"hidden","&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:r,height:c(r).div(2).equal(),background:t,clipPath:{_multi_value_:!0,value:[o,i]},content:'""'},"&::after":{content:'""',position:"absolute",width:a,height:a,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${nr(s)} 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:n,zIndex:0,background:"transparent"}}},_i=8;function Ni(e){const{contentRadius:t,limitVerticalRadius:n}=e,r=t>12?t+2:12;return{arrowOffsetHorizontal:r,arrowOffsetVertical:n?_i:r}}function Ii(e,t){return e?t:{}}function Li(e,t,n){const{componentCls:r,boxShadowPopoverArrow:o,arrowOffsetVertical:i,arrowOffsetHorizontal:a}=e,{arrowDistance:s=0,arrowPlacement:c={left:!0,right:!0,top:!0,bottom:!0}}=n||{};return{[r]:Object.assign(Object.assign(Object.assign(Object.assign({[`${r}-arrow`]:[Object.assign(Object.assign({position:"absolute",zIndex:1,display:"block"},zi(e,t,o)),{"&:before":{background:t}})]},Ii(!!c.top,{[[`&-placement-top > ${r}-arrow`,`&-placement-topLeft > ${r}-arrow`,`&-placement-topRight > ${r}-arrow`].join(",")]:{bottom:s,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${r}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},[`&-placement-topLeft > ${r}-arrow`]:{left:{_skip_check_:!0,value:a}},[`&-placement-topRight > ${r}-arrow`]:{right:{_skip_check_:!0,value:a}}})),Ii(!!c.bottom,{[[`&-placement-bottom > ${r}-arrow`,`&-placement-bottomLeft > ${r}-arrow`,`&-placement-bottomRight > ${r}-arrow`].join(",")]:{top:s,transform:"translateY(-100%)"},[`&-placement-bottom > ${r}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},[`&-placement-bottomLeft > ${r}-arrow`]:{left:{_skip_check_:!0,value:a}},[`&-placement-bottomRight > ${r}-arrow`]:{right:{_skip_check_:!0,value:a}}})),Ii(!!c.left,{[[`&-placement-left > ${r}-arrow`,`&-placement-leftTop > ${r}-arrow`,`&-placement-leftBottom > ${r}-arrow`].join(",")]:{right:{_skip_check_:!0,value:s},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left > ${r}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop > ${r}-arrow`]:{top:i},[`&-placement-leftBottom > ${r}-arrow`]:{bottom:i}})),Ii(!!c.right,{[[`&-placement-right > ${r}-arrow`,`&-placement-rightTop > ${r}-arrow`,`&-placement-rightBottom > ${r}-arrow`].join(",")]:{left:{_skip_check_:!0,value:s},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right > ${r}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop > ${r}-arrow`]:{top:i},[`&-placement-rightBottom > ${r}-arrow`]:{bottom:i}}))}}const Hi={left:{points:["cr","cl"]},right:{points:["cl","cr"]},top:{points:["bc","tc"]},bottom:{points:["tc","bc"]},topLeft:{points:["bl","tl"]},leftTop:{points:["tr","tl"]},topRight:{points:["br","tr"]},rightTop:{points:["tl","tr"]},bottomRight:{points:["tr","br"]},rightBottom:{points:["bl","br"]},bottomLeft:{points:["tl","bl"]},leftBottom:{points:["br","bl"]}},Bi={topLeft:{points:["bl","tc"]},leftTop:{points:["tr","cl"]},topRight:{points:["br","tc"]},rightTop:{points:["tl","cr"]},bottomRight:{points:["tr","bc"]},rightBottom:{points:["bl","cr"]},bottomLeft:{points:["tl","bc"]},leftBottom:{points:["br","cl"]}},Fi=new Set(["topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"]);function Di(e,n){return((e,n,r)=>t().isValidElement(e)?t().cloneElement(e,"function"==typeof r?r(e.props||{}):r):n)(e,e,n)}function Wi(){}const Xi=e.createContext({getPrefixCls:(e,t)=>t||(e?`ant-${e}`:"ant"),iconPrefixCls:"anticon"}),{Consumer:qi}=Xi,Vi=e.createContext(null),Gi=t=>{let{children:n}=t;return e.createElement(Vi.Provider,{value:null},n)},Yi=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return{boxSizing:"border-box",margin:0,padding:0,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,listStyle:"none",fontFamily:t?"inherit":e.fontFamily}},Ui=e=>({a:{color:e.colorLink,textDecoration:e.linkDecoration,backgroundColor:"transparent",outline:"none",cursor:"pointer",transition:`color ${e.motionDurationSlow}`,"-webkit-text-decoration-skip":"objects","&:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive},"&:active,\n  &:hover":{textDecoration:e.linkHoverDecoration,outline:0},"&:focus":{textDecoration:e.linkFocusDecoration,outline:0},"&[disabled]":{color:e.colorTextDisabled,cursor:"not-allowed"}}}),Zi=(e,t,n)=>{const{fontFamily:r,fontSize:o}=e,i=`[class^="${t}"], [class*=" ${t}"]`;return{[n?`.${n}`:i]:{fontFamily:r,fontSize:o,boxSizing:"border-box","&::before, &::after":{boxSizing:"border-box"},[i]:{boxSizing:"border-box","&::before, &::after":{boxSizing:"border-box"}}}}},Ki=e=>({animationDuration:e,animationFillMode:"both"}),Qi=e=>({animationDuration:e,animationFillMode:"both"}),Ji=function(e,t,n,r){const o=arguments.length>4&&void 0!==arguments[4]&&arguments[4]?"&":"";return{[`\n      ${o}${e}-enter,\n      ${o}${e}-appear\n    `]:Object.assign(Object.assign({},Ki(r)),{animationPlayState:"paused"}),[`${o}${e}-leave`]:Object.assign(Object.assign({},Qi(r)),{animationPlayState:"paused"}),[`\n      ${o}${e}-enter${e}-enter-active,\n      ${o}${e}-appear${e}-appear-active\n    `]:{animationName:t,animationPlayState:"running"},[`${o}${e}-leave${e}-leave-active`]:{animationName:n,animationPlayState:"running",pointerEvents:"none"}}},ea=new xo("antZoomIn",{"0%":{transform:"scale(0.2)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),ta=new xo("antZoomOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.2)",opacity:0}}),na=new xo("antZoomBigIn",{"0%":{transform:"scale(0.8)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),ra=new xo("antZoomBigOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.8)",opacity:0}}),oa=new xo("antZoomUpIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 0%"}}),ia=new xo("antZoomUpOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 0%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0}}),aa={zoom:{inKeyframes:ea,outKeyframes:ta},"zoom-big":{inKeyframes:na,outKeyframes:ra},"zoom-big-fast":{inKeyframes:na,outKeyframes:ra},"zoom-left":{inKeyframes:new xo("antZoomLeftIn",{"0%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"0% 50%"}}),outKeyframes:new xo("antZoomLeftOut",{"0%":{transform:"scale(1)",transformOrigin:"0% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0}})},"zoom-right":{inKeyframes:new xo("antZoomRightIn",{"0%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"100% 50%"}}),outKeyframes:new xo("antZoomRightOut",{"0%":{transform:"scale(1)",transformOrigin:"100% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0}})},"zoom-up":{inKeyframes:oa,outKeyframes:ia},"zoom-down":{inKeyframes:new xo("antZoomDownIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 100%"}}),outKeyframes:new xo("antZoomDownOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 100%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0}})}},sa=(e,t)=>{const{antCls:n}=e,r=`${n}-${t}`,{inKeyframes:o,outKeyframes:i}=aa[t];return[Ji(r,o,i,"zoom-big-fast"===t?e.motionDurationFast:e.motionDurationMid),{[`\n        ${r}-enter,\n        ${r}-appear\n      `]:{transform:"scale(0)",opacity:0,animationTimingFunction:e.motionEaseOutCirc,"&-prepare":{transform:"none"}},[`${r}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]},ca=["blue","purple","cyan","green","magenta","pink","red","orange","yellow","volcano","geekblue","lime","gold"];function la(e,t){return ca.reduce(((n,r)=>{const o=e[`${r}1`],i=e[`${r}3`],a=e[`${r}6`],s=e[`${r}7`];return Object.assign(Object.assign({},n),t(r,{lightColor:o,lightBorderColor:i,darkColor:a,textColor:s}))}),{})}const ua="undefined"!=typeof CSSINJS_STATISTIC;let fa=!0;function da(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!ua)return Object.assign.apply(Object,[{}].concat(t));fa=!1;const r={};return t.forEach((e=>{Object.keys(e).forEach((t=>{Object.defineProperty(r,t,{configurable:!0,enumerable:!0,get:()=>e[t]})}))})),fa=!0,r}const pa={};function ha(){}function ma(e,t,n){return t=He(t),De(e,Be()?Reflect.construct(t,n||[],He(e).constructor):t.apply(e,n))}"undefined"==typeof Reflect?Object.keys:Reflect.ownKeys;const ga=Ne((function e(){ze(this,e)}));let va=function(e){function t(e){var n;return ze(this,t),(n=ma(this,t)).result=0,e instanceof t?n.result=e.result:"number"==typeof e&&(n.result=e),n}return Le(t,e),Ne(t,[{key:"add",value:function(e){return e instanceof t?this.result+=e.result:"number"==typeof e&&(this.result+=e),this}},{key:"sub",value:function(e){return e instanceof t?this.result-=e.result:"number"==typeof e&&(this.result-=e),this}},{key:"mul",value:function(e){return e instanceof t?this.result*=e.result:"number"==typeof e&&(this.result*=e),this}},{key:"div",value:function(e){return e instanceof t?this.result/=e.result:"number"==typeof e&&(this.result/=e),this}},{key:"equal",value:function(){return this.result}}])}(ga);const ba="CALC_UNIT";function ya(e){return"number"==typeof e?`${e}${ba}`:e}let wa=function(e){function t(e){var n;return ze(this,t),(n=ma(this,t)).result="",e instanceof t?n.result=`(${e.result})`:"number"==typeof e?n.result=ya(e):"string"==typeof e&&(n.result=e),n}return Le(t,e),Ne(t,[{key:"add",value:function(e){return e instanceof t?this.result=`${this.result} + ${e.getResult()}`:"number"!=typeof e&&"string"!=typeof e||(this.result=`${this.result} + ${ya(e)}`),this.lowPriority=!0,this}},{key:"sub",value:function(e){return e instanceof t?this.result=`${this.result} - ${e.getResult()}`:"number"!=typeof e&&"string"!=typeof e||(this.result=`${this.result} - ${ya(e)}`),this.lowPriority=!0,this}},{key:"mul",value:function(e){return this.lowPriority&&(this.result=`(${this.result})`),e instanceof t?this.result=`${this.result} * ${e.getResult(!0)}`:"number"!=typeof e&&"string"!=typeof e||(this.result=`${this.result} * ${e}`),this.lowPriority=!1,this}},{key:"div",value:function(e){return this.lowPriority&&(this.result=`(${this.result})`),e instanceof t?this.result=`${this.result} / ${e.getResult(!0)}`:"number"!=typeof e&&"string"!=typeof e||(this.result=`${this.result} / ${e}`),this.lowPriority=!1,this}},{key:"getResult",value:function(e){return this.lowPriority||e?`(${this.result})`:this.result}},{key:"equal",value:function(e){const{unit:t=!0}=e||{},n=new RegExp(`${ba}`,"g");return this.result=this.result.replace(n,t?"px":""),void 0!==this.lowPriority?`calc(${this.result})`:this.result}}])}(ga);const xa=(e,t,n)=>{var r;return"function"==typeof n?n(da(t,null!==(r=t[e])&&void 0!==r?r:{})):null!=n?n:{}},Sa=(e,t,n,r)=>{const o=Object.assign({},t[e]);if(null==r?void 0:r.deprecatedTokens){const{deprecatedTokens:e}=r;e.forEach((e=>{let[t,n]=e;var r;((null==o?void 0:o[t])||(null==o?void 0:o[n]))&&(null!==(r=o[n])&&void 0!==r||(o[n]=null==o?void 0:o[t]))}))}const i=Object.assign(Object.assign({},n),o);return Object.keys(i).forEach((e=>{i[e]===t[e]&&delete i[e]})),i};function ka(t,n,r){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const i=Array.isArray(t)?t:[t,t],[a]=i,s=i.join("-");return function(t){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t;const[c,l,u,f,d]=$i(),{getPrefixCls:p,iconPrefixCls:h,csp:m}=(0,e.useContext)(Xi),g=p(),v=d?"css":"js",b=(e=>{const t="css"===e?wa:va;return e=>new t(e)})(v),{max:y,min:w}=function(e){return"js"===e?{max:Math.max,min:Math.min}:{max:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return`max(${t.map((e=>nr(e))).join(",")})`},min:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return`min(${t.map((e=>nr(e))).join(",")})`}}}(v),x={theme:c,token:f,hashId:u,nonce:()=>null==m?void 0:m.nonce,clientOnly:o.clientOnly,order:o.order||-999};bo(Object.assign(Object.assign({},x),{clientOnly:!1,path:["Shared",g]}),(()=>[{"&":Ui(f)}])),((e,t)=>{const[n,r]=$i();bo({theme:n,token:r,hashId:"",path:["ant-design-icons",e],nonce:()=>null==t?void 0:t.nonce},(()=>[{[`.${e}`]:Object.assign(Object.assign({},{display:"inline-flex",alignItems:"center",color:"inherit",fontStyle:"normal",lineHeight:0,textAlign:"center",textTransform:"none",verticalAlign:"-0.125em",textRendering:"optimizeLegibility","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale","> *":{lineHeight:1},svg:{display:"inline-block"}}),{[`.${e} .${e}-icon`]:{display:"block"}})}]))})(h,m);const S=bo(Object.assign(Object.assign({},x),{path:[s,t,h]}),(()=>{if(!1===o.injectStyle)return[];const{token:e,flush:s}=(e=>{let t,n=e,r=ha;return ua&&"undefined"!=typeof Proxy&&(t=new Set,n=new Proxy(e,{get:(e,n)=>(fa&&t.add(n),e[n])}),r=(e,n)=>{var r;pa[e]={global:Array.from(t),component:Object.assign(Object.assign({},null===(r=pa[e])||void 0===r?void 0:r.component),n)}}),{token:n,keys:t,flush:r}})(f),c=xa(a,l,r),p=`.${t}`,m=Sa(a,l,c,{deprecatedTokens:o.deprecatedTokens});d&&Object.keys(c).forEach((e=>{c[e]=`var(${or(e,((e,t)=>`${[t,e.replace(/([A-Z]+)([A-Z][a-z]+)/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2")].filter(Boolean).join("-")}`)(a,d.prefix))})`}));const v=da(e,{componentCls:p,prefixCls:t,iconCls:`.${h}`,antCls:`.${g}`,calc:b,max:y,min:w},d?c:m),x=n(v,{hashId:u,prefixCls:t,rootPrefixCls:g,iconPrefixCls:h});return s(a,m),[!1===o.resetStyle?null:Zi(v,t,i),x]}));return[S,u]}}const Ca=(n,r,o,i)=>{const a=ka(n,r,o,i),s=((n,r,o)=>{function i(e){return`${n}${e.slice(0,1).toUpperCase()}${e.slice(1)}`}const{unitless:a={},injectStyle:s=!0}=null!=o?o:{},c={[i("zIndexPopup")]:!0};Object.keys(a).forEach((e=>{c[i(e)]=a[e]}));const l=t=>{let{rootCls:a,cssVar:s}=t;const[,l]=$i();return function(t,n){var r=t.key,o=t.prefix,i=t.unitless,a=t.ignore,s=t.token,c=t.scope,l=void 0===c?"":c,u=(0,e.useContext)(Dn),f=u.cache.instanceId,d=u.container,p=s._tokenKey,h=[].concat(I(t.path),[r,l,p]),m=fr(yo,h,(function(){var e=n(),t=w(ar(e,r,{prefix:o,unitless:i,ignore:a,scope:l}),2),s=t[0],c=t[1];return[s,c,mo(h,c),r]}),(function(e){var t=w(e,3)[2];tr&&Q(t,{mark:Hn})}),(function(e){var t=w(e,3),n=t[1],o=t[2];if(n){var i=J(n,o,{mark:Hn,prepend:"queue",attachTo:d,priority:-999});i[Bn]=f,i.setAttribute(Ln,r)}}))}({path:[n],prefix:s.prefix,key:null==s?void 0:s.key,unitless:Object.assign(Object.assign({},ki),c),ignore:Ci,token:l,scope:a},(()=>{const e=xa(n,l,r),t=Sa(n,l,e,{deprecatedTokens:null==o?void 0:o.deprecatedTokens});return Object.keys(e).forEach((e=>{t[i(e)]=t[e],delete t[e]})),t})),null};return e=>{const[,,,,r]=$i();return[o=>s&&r?t().createElement(t().Fragment,null,t().createElement(l,{rootCls:e,cssVar:r,component:n}),o):o,null==r?void 0:r.key]}})(Array.isArray(n)?n[0]:n,o,i);return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e;const[,n]=a(e,t),[r,o]=s(t);return[r,n,o]}},Oa=e=>{const{componentCls:t,tooltipMaxWidth:n,tooltipColor:r,tooltipBg:o,tooltipBorderRadius:i,zIndexPopup:a,controlHeight:s,boxShadowSecondary:c,paddingSM:l,paddingXS:u}=e;return[{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},Yi(e)),{position:"absolute",zIndex:a,display:"block",width:"max-content",maxWidth:n,visibility:"visible",transformOrigin:"var(--arrow-x, 50%) var(--arrow-y, 50%)","&-hidden":{display:"none"},"--antd-arrow-background-color":o,[`${t}-inner`]:{minWidth:s,minHeight:s,padding:`${nr(e.calc(l).div(2).equal())} ${nr(u)}`,color:r,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:o,borderRadius:i,boxShadow:c,boxSizing:"border-box"},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${t}-inner`]:{borderRadius:e.min(i,_i)}},[`${t}-content`]:{position:"relative"}}),la(e,((e,n)=>{let{darkColor:r}=n;return{[`&${t}-${e}`]:{[`${t}-inner`]:{backgroundColor:r},[`${t}-arrow`]:{"--antd-arrow-background-color":r}}}}))),{"&-rtl":{direction:"rtl"}})},Li(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow}}]},Ea=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+70},Ni({contentRadius:e.borderRadius,limitVerticalRadius:!0})),Ti(da(e,{borderRadiusOuter:Math.min(e.borderRadiusOuter,4)}))),$a=function(e){const t=Ca("Tooltip",(e=>{const{borderRadius:t,colorTextLightSolid:n,colorBgSpotlight:r}=e,o=da(e,{tooltipMaxWidth:250,tooltipColor:n,tooltipBorderRadius:t,tooltipBg:r});return[Oa(o),sa(e,"zoom-big-fast")]}),Ea,{resetStyle:!1,injectStyle:!(arguments.length>1&&void 0!==arguments[1])||arguments[1]});return t(e)},Aa=ca.map((e=>`${e}-inverse`));function ja(e){return arguments.length>1&&void 0!==arguments[1]&&!arguments[1]?ca.includes(e):[].concat(I(Aa),I(ca)).includes(e)}function Ma(e,t){const n=ja(t),r=l()({[`${e}-${t}`]:t&&n}),o={},i={};return t&&!n&&(o.background=t,i["--antd-arrow-background-color"]=t),{className:r,overlayStyle:o,arrowStyle:i}}const Pa=e.forwardRef(((n,r)=>{var o,i;const{prefixCls:a,openClassName:s,getTooltipContainer:c,overlayClassName:u,color:f,overlayInnerStyle:d,children:p,afterOpenChange:h,afterVisibleChange:m,destroyTooltipOnHide:g,arrow:v=!0,title:b,overlay:y,builtinPlacements:x,arrowPointAtCenter:S=!1,autoAdjustOverflow:k=!0}=n,C=!!v,[,O]=$i(),{getPopupContainer:E,getPrefixCls:$,direction:A}=e.useContext(Xi),j=(()=>{const e=()=>{};return e.deprecated=Wi,e})(),M=e.useRef(null),P=()=>{var e;null===(e=M.current)||void 0===e||e.forceAlign()};e.useImperativeHandle(r,(()=>({forceAlign:P,forcePopupAlign:()=>{j.deprecated(!1,"forcePopupAlign","forceAlign"),P()}})));const[R,T]=(z=!1,_={value:null!==(o=n.open)&&void 0!==o?o:n.visible,defaultValue:null!==(i=n.defaultOpen)&&void 0!==i?i:n.defaultVisible},I=(N=_||{}).defaultValue,L=N.value,H=N.onChange,F=N.postState,D=w(it((function(){return Rn(L)?L:Rn(I)?"function"==typeof I?I():I:z})),2),W=D[0],X=D[1],q=void 0!==L?L:W,V=F?F(q):q,G=Qe(H),Y=w(it([q]),2),U=Y[0],Z=Y[1],B((function(){var e=U[0];W!==e&&G(W,e)}),[U]),B((function(){Rn(L)||X(L)}),[L]),[V,Qe((function(e,t){X(e,t),Z([q],t)}))]);var z,_,N,I,L,H,F,D,W,X,q,V,G,Y,U,Z;const K=!b&&!y&&0!==b,Q=e.useMemo((()=>{var e,t;let n=S;return"object"==typeof v&&(n=null!==(t=null!==(e=v.pointAtCenter)&&void 0!==e?e:v.arrowPointAtCenter)&&void 0!==t?t:S),x||function(e){const{arrowWidth:t,autoAdjustOverflow:n,arrowPointAtCenter:r,offset:o,borderRadius:i,visibleFirst:a}=e,s=t/2,c={};return Object.keys(Hi).forEach((e=>{const l=r&&Bi[e]||Hi[e],u=Object.assign(Object.assign({},l),{offset:[0,0],dynamicInset:!0});switch(c[e]=u,Fi.has(e)&&(u.autoArrow=!1),e){case"top":case"topLeft":case"topRight":u.offset[1]=-s-o;break;case"bottom":case"bottomLeft":case"bottomRight":u.offset[1]=s+o;break;case"left":case"leftTop":case"leftBottom":u.offset[0]=-s-o;break;case"right":case"rightTop":case"rightBottom":u.offset[0]=s+o}const f=Ni({contentRadius:i,limitVerticalRadius:!0});if(r)switch(e){case"topLeft":case"bottomLeft":u.offset[0]=-f.arrowOffsetHorizontal-s;break;case"topRight":case"bottomRight":u.offset[0]=f.arrowOffsetHorizontal+s;break;case"leftTop":case"rightTop":u.offset[1]=-f.arrowOffsetHorizontal-s;break;case"leftBottom":case"rightBottom":u.offset[1]=f.arrowOffsetHorizontal+s}u.overflow=function(e,t,n,r){if(!1===r)return{adjustX:!1,adjustY:!1};const o=r&&"object"==typeof r?r:{},i={};switch(e){case"top":case"bottom":i.shiftX=2*t.arrowOffsetHorizontal+n,i.shiftY=!0,i.adjustY=!0;break;case"left":case"right":i.shiftY=2*t.arrowOffsetVertical+n,i.shiftX=!0,i.adjustX=!0}const a=Object.assign(Object.assign({},i),o);return a.shiftX||(a.adjustX=!0),a.shiftY||(a.adjustY=!0),a}(e,f,t,n),a&&(u.htmlRegion="visibleFirst")})),c}({arrowPointAtCenter:n,autoAdjustOverflow:k,arrowWidth:C?O.sizePopupArrow:0,borderRadius:O.borderRadius,offset:O.marginXXS,visibleFirst:!0})}),[S,v,x,O]),J=e.useMemo((()=>0===b?b:y||b||""),[y,b]),ee=e.createElement(Gi,null,"function"==typeof J?J():J),{getPopupContainer:te,placement:ne="top",mouseEnterDelay:re=.1,mouseLeaveDelay:oe=.1,overlayStyle:ie,rootClassName:ae}=n,se=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(n,["getPopupContainer","placement","mouseEnterDelay","mouseLeaveDelay","overlayStyle","rootClassName"]),ce=$("tooltip",a),le=$(),ue=n["data-popover-inject"];let fe=R;"open"in n||"visible"in n||!K||(fe=!1);const de=e.isValidElement(p)&&!function(e){return e&&t().isValidElement(e)&&e.type===t().Fragment}(p)?p:e.createElement("span",null,p),pe=de.props,he=pe.className&&"string"!=typeof pe.className?pe.className:l()(pe.className,s||`${ce}-open`),[me,ge,ve]=$a(ce,!ue),be=Ma(ce,f),ye=be.arrowStyle,we=Object.assign(Object.assign({},d),be.overlayStyle),xe=l()(u,{[`${ce}-rtl`]:"rtl"===A},be.className,ae,ge,ve),[Se,ke]=function(e,n){const[,r]=$i(),o=t().useContext(Ai);if(void 0!==n)return[n,n];let i=null!=o?o:0;return e in Mi?(i+=(o?0:r.zIndexPopupBase)+Mi[e],i=Math.min(i,r.zIndexPopupBase+1e3)):i+=Pi[e],[void 0===o?n:i,i]}("Tooltip",se.zIndex),Ce=e.createElement(Pn,Object.assign({},se,{zIndex:Se,showArrow:C,placement:ne,mouseEnterDelay:re,mouseLeaveDelay:oe,prefixCls:ce,overlayClassName:xe,overlayStyle:Object.assign(Object.assign({},ye),ie),getTooltipContainer:te||c||E,ref:M,builtinPlacements:Q,overlay:ee,visible:fe,onVisibleChange:e=>{var t,r;T(!K&&e),K||(null===(t=n.onOpenChange)||void 0===t||t.call(n,e),null===(r=n.onVisibleChange)||void 0===r||r.call(n,e))},afterVisibleChange:null!=h?h:m,overlayInnerStyle:we,arrowContent:e.createElement("span",{className:`${ce}-arrow-content`}),motion:{motionName:Ri(le,"zoom-big-fast",n.transitionName),motionDeadline:1e3},destroyTooltipOnHide:!!g}),fe?Di(de,{className:he}):de);return me(e.createElement(Ai.Provider,{value:ke},Ce))}));Pa._InternalPanelDoNotUseOrYouWillBeFired=t=>{const{prefixCls:n,className:r,placement:o="top",title:i,color:a,overlayInnerStyle:s}=t,{getPrefixCls:c}=e.useContext(Xi),f=c("tooltip",n),[d,p,h]=$a(f),m=Ma(f,a),g=m.arrowStyle,v=Object.assign(Object.assign({},s),m.overlayStyle),b=l()(p,h,f,`${f}-pure`,`${f}-placement-${o}`,r,m.className);return d(e.createElement("div",{className:b,style:g},e.createElement("div",{className:`${f}-arrow`}),e.createElement(u,Object.assign({},t,{className:p,prefixCls:f,overlayInnerStyle:v}),i)))};const Ra=Pa,Ta=new xo("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),za=new xo("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),_a=new xo("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),Na=new xo("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),Ia=new xo("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),La=new xo("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),Ha=e=>{const{fontHeight:t,lineWidth:n,marginXS:r,colorBorderBg:o}=e;return da(e,{badgeFontHeight:t,badgeShadowSize:n,badgeTextColor:e.colorBgContainer,badgeColor:e.colorError,badgeColorHover:e.colorErrorHover,badgeShadowColor:o,badgeProcessingDuration:"1.2s",badgeRibbonOffset:r,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},Ba=e=>{const{fontSize:t,lineHeight:n,fontSizeSM:r,lineWidth:o}=e;return{indicatorZIndex:"auto",indicatorHeight:Math.round(t*n)-2*o,indicatorHeightSM:t,dotSize:r/2,textFontSize:r,textFontSizeSM:r,textFontWeight:"normal",statusSize:r/2}},Fa=Ca("Badge",(e=>(e=>{const{componentCls:t,iconCls:n,antCls:r,badgeShadowSize:o,motionDurationSlow:i,textFontSize:a,textFontSizeSM:s,statusSize:c,dotSize:l,textFontWeight:u,indicatorHeight:f,indicatorHeightSM:d,marginXS:p,calc:h}=e,m=`${r}-scroll-number`,g=la(e,((e,n)=>{let{darkColor:r}=n;return{[`&${t} ${t}-color-${e}`]:{background:r,[`&:not(${t}-count)`]:{color:r}}}}));return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},Yi(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${t}-count`]:{zIndex:e.indicatorZIndex,minWidth:f,height:f,color:e.badgeTextColor,fontWeight:u,fontSize:a,lineHeight:nr(f),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:h(f).div(2).equal(),boxShadow:`0 0 0 ${nr(o)} ${e.badgeShadowColor}`,transition:`background ${e.motionDurationMid}`,a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},[`${t}-count-sm`]:{minWidth:d,height:d,fontSize:s,lineHeight:nr(d),borderRadius:h(d).div(2).equal()},[`${t}-multiple-words`]:{padding:`0 ${nr(e.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${t}-dot`]:{zIndex:e.indicatorZIndex,width:l,minWidth:l,height:l,background:e.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${nr(o)} ${e.badgeShadowColor}`},[`${t}-dot${m}`]:{transition:`background ${i}`},[`${t}-count, ${t}-dot, ${m}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${n}-spin`]:{animationName:La,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${t}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${t}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:c,height:c,verticalAlign:"middle",borderRadius:"50%"},[`${t}-status-success`]:{backgroundColor:e.colorSuccess},[`${t}-status-processing`]:{overflow:"visible",color:e.colorPrimary,backgroundColor:e.colorPrimary,"&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:o,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:Ta,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${t}-status-default`]:{backgroundColor:e.colorTextPlaceholder},[`${t}-status-error`]:{backgroundColor:e.colorError},[`${t}-status-warning`]:{backgroundColor:e.colorWarning},[`${t}-status-text`]:{marginInlineStart:p,color:e.colorText,fontSize:e.fontSize}}}),g),{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:za,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`${t}-zoom-leave`]:{animationName:_a,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`&${t}-not-a-wrapper`]:{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:Na,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`${t}-zoom-leave`]:{animationName:Ia,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`&:not(${t}-status)`]:{verticalAlign:"middle"},[`${m}-custom-component, ${t}-count`]:{transform:"none"},[`${m}-custom-component, ${m}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[`${m}`]:{overflow:"hidden",[`${m}-only`]:{position:"relative",display:"inline-block",height:f,transition:`all ${e.motionDurationSlow} ${e.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${m}-only-unit`]:{height:f,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${m}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${t}-count, ${t}-dot, ${m}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}})(Ha(e))),Ba),Da=Ca(["Badge","Ribbon"],(e=>(e=>{const{antCls:t,badgeFontHeight:n,marginXS:r,badgeRibbonOffset:o,calc:i}=e,a=`${t}-ribbon`,s=`${t}-ribbon-wrapper`,c=la(e,((e,t)=>{let{darkColor:n}=t;return{[`&${a}-color-${e}`]:{background:n,color:n}}}));return{[`${s}`]:{position:"relative"},[`${a}`]:Object.assign(Object.assign(Object.assign(Object.assign({},Yi(e)),{position:"absolute",top:r,padding:`0 ${nr(e.paddingXS)}`,color:e.colorPrimary,lineHeight:nr(n),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,[`${a}-text`]:{color:e.colorTextLightSolid},[`${a}-corner`]:{position:"absolute",top:"100%",width:o,height:o,color:"currentcolor",border:`${nr(i(o).div(2).equal())} solid`,transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),c),{[`&${a}-placement-end`]:{insetInlineEnd:i(o).mul(-1).equal(),borderEndEndRadius:0,[`${a}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${a}-placement-start`]:{insetInlineStart:i(o).mul(-1).equal(),borderEndStartRadius:0,[`${a}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}})(Ha(e))),Ba);function Wa(t){let n,{prefixCls:r,value:o,current:i,offset:a=0}=t;return a&&(n={position:"absolute",top:`${a}00%`,left:0}),e.createElement("span",{style:n,className:l()(`${r}-only-unit`,{current:i})},o)}function Xa(e,t,n){let r=e,o=0;for(;(r+10)%10!==t;)r+=n,o+=n;return o}function qa(t){const{prefixCls:n,count:r,value:o}=t,i=Number(o),a=Math.abs(r),[s,c]=e.useState(i),[l,u]=e.useState(a),f=()=>{c(i),u(a)};let d,p;if(e.useEffect((()=>{const e=setTimeout((()=>{f()}),1e3);return()=>{clearTimeout(e)}}),[i]),s===i||Number.isNaN(i)||Number.isNaN(s))d=[e.createElement(Wa,Object.assign({},t,{key:i,current:!0}))],p={transition:"none"};else{d=[];const n=i+10,r=[];for(let e=i;e<=n;e+=1)r.push(e);const o=r.findIndex((e=>e%10===s));d=r.map(((n,r)=>{const i=n%10;return e.createElement(Wa,Object.assign({},t,{key:n,value:i,offset:r-o,current:r===o}))})),p={transform:`translateY(${-Xa(s,i,l<a?1:-1)}00%)`}}return e.createElement("span",{className:`${n}-only`,style:p,onTransitionEnd:f},d)}const Va=e.forwardRef(((t,n)=>{const{prefixCls:r,count:o,className:i,motionClassName:a,style:s,title:c,show:u,component:f="sup",children:d}=t,p=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(t,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:h}=e.useContext(Xi),m=h("scroll-number",r),g=Object.assign(Object.assign({},p),{"data-show":u,style:s,className:l()(m,i,a),title:c});let v=o;if(o&&Number(o)%1==0){const t=String(o).split("");v=e.createElement("bdi",null,t.map(((n,r)=>e.createElement(qa,{prefixCls:m,count:Number(o),value:n,key:t.length-r}))))}return s&&s.borderColor&&(g.style=Object.assign(Object.assign({},s),{boxShadow:`0 0 0 1px ${s.borderColor} inset`})),d?Di(d,(e=>({className:l()(`${m}-custom-component`,null==e?void 0:e.className,a)}))):e.createElement(f,Object.assign({},g,{ref:n}),v)})),Ga=Va;const Ya=(t,n)=>{var r,o,i,a,s;const{prefixCls:c,scrollNumberPrefixCls:u,children:f,status:d,text:p,color:h,count:m=null,overflowCount:g=99,dot:v=!1,size:b="default",title:y,offset:w,style:x,className:S,rootClassName:k,classNames:C,styles:O,showZero:E=!1}=t,$=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(t,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:A,direction:j,badge:M}=e.useContext(Xi),P=A("badge",c),[R,T,z]=Fa(P),_=m>g?`${g}+`:m,N="0"===_||0===_,I=(null!=d||null!=h)&&(null===m||N&&!E),L=v&&!N,H=L?"":_,B=(0,e.useMemo)((()=>(null==H||""===H||N&&!E)&&!L),[H,N,E,L]),F=(0,e.useRef)(m);B||(F.current=m);const D=F.current,W=(0,e.useRef)(H);B||(W.current=H);const X=W.current,q=(0,e.useRef)(L);B||(q.current=L);const V=(0,e.useMemo)((()=>{if(!w)return Object.assign(Object.assign({},null==M?void 0:M.style),x);const e={marginTop:w[1]};return"rtl"===j?e.left=parseInt(w[0],10):e.right=-parseInt(w[0],10),Object.assign(Object.assign(Object.assign({},e),null==M?void 0:M.style),x)}),[j,w,x,null==M?void 0:M.style]),G=null!=y?y:"string"==typeof D||"number"==typeof D?D:void 0,Y=B||!p?null:e.createElement("span",{className:`${P}-status-text`},p),U=D&&"object"==typeof D?Di(D,(e=>({style:Object.assign(Object.assign({},V),e.style)}))):void 0,Z=ja(h,!1),K=l()(null==C?void 0:C.indicator,null===(r=null==M?void 0:M.classNames)||void 0===r?void 0:r.indicator,{[`${P}-status-dot`]:I,[`${P}-status-${d}`]:!!d,[`${P}-color-${h}`]:Z}),Q={};h&&!Z&&(Q.color=h,Q.background=h);const J=l()(P,{[`${P}-status`]:I,[`${P}-not-a-wrapper`]:!f,[`${P}-rtl`]:"rtl"===j},S,k,null==M?void 0:M.className,null===(o=null==M?void 0:M.classNames)||void 0===o?void 0:o.root,null==C?void 0:C.root,T,z);if(!f&&I){const t=V.color;return R(e.createElement("span",Object.assign({},$,{className:J,style:Object.assign(Object.assign(Object.assign({},null==O?void 0:O.root),null===(i=null==M?void 0:M.styles)||void 0===i?void 0:i.root),V)}),e.createElement("span",{className:K,style:Object.assign(Object.assign(Object.assign({},null==O?void 0:O.indicator),null===(a=null==M?void 0:M.styles)||void 0===a?void 0:a.indicator),Q)}),p&&e.createElement("span",{style:{color:t},className:`${P}-status-text`},p)))}return R(e.createElement("span",Object.assign({ref:n},$,{className:J,style:Object.assign(Object.assign({},null===(s=null==M?void 0:M.styles)||void 0===s?void 0:s.root),null==O?void 0:O.root)}),f,e.createElement(Jt,{visible:!B,motionName:`${P}-zoom`,motionAppear:!1,motionDeadline:1e3},(t=>{let{className:n,ref:r}=t;var o,i;const a=A("scroll-number",u),s=q.current,c=l()(null==C?void 0:C.indicator,null===(o=null==M?void 0:M.classNames)||void 0===o?void 0:o.indicator,{[`${P}-dot`]:s,[`${P}-count`]:!s,[`${P}-count-sm`]:"small"===b,[`${P}-multiple-words`]:!s&&X&&X.toString().length>1,[`${P}-status-${d}`]:!!d,[`${P}-color-${h}`]:Z});let f=Object.assign(Object.assign(Object.assign({},null==O?void 0:O.indicator),null===(i=null==M?void 0:M.styles)||void 0===i?void 0:i.indicator),V);return h&&!Z&&(f=f||{},f.background=h),e.createElement(Ga,{prefixCls:a,show:!B,motionClassName:n,className:c,count:X,title:G,style:f,key:"scrollNumber",ref:r},U)})),Y))},Ua=e.forwardRef(Ya);Ua.Ribbon=t=>{const{className:n,prefixCls:r,style:o,color:i,children:a,text:s,placement:c="end",rootClassName:u}=t,{getPrefixCls:f,direction:d}=e.useContext(Xi),p=f("ribbon",r),h=`${p}-wrapper`,[m,g,v]=Da(p,h),b=ja(i,!1),y=l()(p,`${p}-placement-${c}`,{[`${p}-rtl`]:"rtl"===d,[`${p}-color-${i}`]:b},n),w={},x={};return i&&!b&&(w.background=i,x.color=i),m(e.createElement("div",{className:l()(h,u,g,v)},a,e.createElement("div",{className:l()(y,g),style:Object.assign(Object.assign({},w),o)},e.createElement("span",{className:`${p}-text`},s),e.createElement("div",{className:`${p}-corner`,style:x}))))};const Za=Ua,Ka=["xxl","xl","lg","md","sm","xs"],Qa=e=>{const[,,,,t]=$i();return t?`${e}-css-var`:""},Ja=e.createContext(void 0),es=function(){let n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const r=(0,e.useRef)({}),o=function(){const[,t]=e.useReducer((e=>e+1),0);return t}(),i=function(){const[,e]=$i(),n=(e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`}))((e=>{const t=e,n=[].concat(Ka).reverse();return n.forEach(((e,r)=>{const o=e.toUpperCase(),i=`screen${o}Min`,a=`screen${o}`;if(!(t[i]<=t[a]))throw new Error(`${i}<=${a} fails : !(${t[i]}<=${t[a]})`);if(r<n.length-1){const e=`screen${o}Max`;if(!(t[a]<=t[e]))throw new Error(`${a}<=${e} fails : !(${t[a]}<=${t[e]})`);const i=`screen${n[r+1].toUpperCase()}Min`;if(!(t[e]<=t[i]))throw new Error(`${e}<=${i} fails : !(${t[e]}<=${t[i]})`)}})),e})(e));return t().useMemo((()=>{const e=new Map;let t=-1,r={};return{matchHandlers:{},dispatch:t=>(r=t,e.forEach((e=>e(r))),e.size>=1),subscribe(n){return e.size||this.register(),t+=1,e.set(t,n),n(r),t},unsubscribe(t){e.delete(t),e.size||this.unregister()},unregister(){Object.keys(n).forEach((e=>{const t=n[e],r=this.matchHandlers[t];null==r||r.mql.removeListener(null==r?void 0:r.listener)})),e.clear()},register(){Object.keys(n).forEach((e=>{const t=n[e],o=t=>{let{matches:n}=t;this.dispatch(Object.assign(Object.assign({},r),{[e]:n}))},i=window.matchMedia(t);i.addListener(o),this.matchHandlers[t]={mql:i,listener:o},o(i)}))},responsiveMap:n}}),[e])}();return F((()=>{const e=i.subscribe((e=>{r.current=e,n&&o()}));return()=>i.unsubscribe(e)}),[]),r.current},ts=e.createContext({}),ns=e=>{const{antCls:t,componentCls:n,iconCls:r,avatarBg:o,avatarColor:i,containerSize:a,containerSizeLG:s,containerSizeSM:c,textFontSize:l,textFontSizeLG:u,textFontSizeSM:f,borderRadius:d,borderRadiusLG:p,borderRadiusSM:h,lineWidth:m,lineType:g}=e,v=(e,t,o)=>({width:e,height:e,borderRadius:"50%",[`&${n}-square`]:{borderRadius:o},[`&${n}-icon`]:{fontSize:t,[`> ${r}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},Yi(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:i,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:o,border:`${nr(m)} ${g} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),v(a,l,d)),{"&-lg":Object.assign({},v(s,u,p)),"&-sm":Object.assign({},v(c,f,h)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},rs=e=>{const{componentCls:t,groupBorderColor:n,groupOverlapping:r,groupSpace:o}=e;return{[`${t}-group`]:{display:"inline-flex",[`${t}`]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:r}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:o}}}},os=Ca("Avatar",(e=>{const{colorTextLightSolid:t,colorTextPlaceholder:n}=e,r=da(e,{avatarBg:n,avatarColor:t});return[ns(r),rs(r)]}),(e=>{const{controlHeight:t,controlHeightLG:n,controlHeightSM:r,fontSize:o,fontSizeLG:i,fontSizeXL:a,fontSizeHeading3:s,marginXS:c,marginXXS:l,colorBorderBg:u}=e;return{containerSize:t,containerSizeLG:n,containerSizeSM:r,textFontSize:Math.round((i+a)/2),textFontSizeLG:s,textFontSizeSM:o,groupSpace:l,groupOverlapping:-c,groupBorderColor:u}}));const is=(n,r)=>{const[o,i]=e.useState(1),[a,s]=e.useState(!1),[c,u]=e.useState(!0),f=e.useRef(null),d=e.useRef(null),p=T(r,f),{getPrefixCls:h,avatar:m}=e.useContext(Xi),g=e.useContext(ts),v=()=>{if(!d.current||!f.current)return;const e=d.current.offsetWidth,t=f.current.offsetWidth;if(0!==e&&0!==t){const{gap:r=4}=n;2*r<t&&i(t-2*r<e?(t-2*r)/e:1)}};e.useEffect((()=>{s(!0)}),[]),e.useEffect((()=>{u(!0),i(1)}),[n.src]),e.useEffect(v,[n.gap]);const{prefixCls:b,shape:y,size:w,src:x,srcSet:S,icon:k,className:C,rootClassName:O,alt:E,draggable:$,children:A,crossOrigin:j}=n,M=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(n,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","alt","draggable","children","crossOrigin"]),P=(e=>{const n=t().useContext(Ja);return t().useMemo((()=>e?"string"==typeof e?null!=e?e:n:e instanceof Function?e(n):n:n),[e,n])})((e=>{var t,n;return null!==(n=null!==(t=null!=w?w:null==g?void 0:g.size)&&void 0!==t?t:e)&&void 0!==n?n:"default"})),R=Object.keys("object"==typeof P&&P||{}).some((e=>["xs","sm","md","lg","xl","xxl"].includes(e))),z=es(R),_=e.useMemo((()=>{if("object"!=typeof P)return{};const e=Ka.find((e=>z[e])),t=P[e];return t?{width:t,height:t,fontSize:t&&(k||A)?t/2:18}:{}}),[z,P]),N=h("avatar",b),I=Qa(N),[L,H,B]=os(N,I),F=l()({[`${N}-lg`]:"large"===P,[`${N}-sm`]:"small"===P}),D=e.isValidElement(x),W=y||(null==g?void 0:g.shape)||"circle",X=l()(N,F,null==m?void 0:m.className,`${N}-${W}`,{[`${N}-image`]:D||x&&c,[`${N}-icon`]:!!k},B,I,C,O,H),q="number"==typeof P?{width:P,height:P,fontSize:k?P/2:18}:{};let V;if("string"==typeof x&&c)V=e.createElement("img",{src:x,draggable:$,srcSet:S,onError:()=>{const{onError:e}=n;!1!==(null==e?void 0:e())&&u(!1)},alt:E,crossOrigin:j});else if(D)V=x;else if(k)V=k;else if(a||1!==o){const t=`scale(${o})`,n={msTransform:t,WebkitTransform:t,transform:t};V=e.createElement(Ue,{onResize:v},e.createElement("span",{className:`${N}-string`,ref:d,style:Object.assign({},n)},A))}else V=e.createElement("span",{className:`${N}-string`,style:{opacity:0},ref:d},A);return delete M.onError,delete M.gap,L(e.createElement("span",Object.assign({},M,{style:Object.assign(Object.assign(Object.assign(Object.assign({},q),_),null==m?void 0:m.style),M.style),className:X,ref:p}),V))},as=e.forwardRef(is),ss=e=>e?"function"==typeof e?e():e:null,cs=e=>{const{componentCls:t,popoverColor:n,titleMinWidth:r,fontWeightStrong:o,innerPadding:i,boxShadowSecondary:a,colorTextHeading:s,borderRadiusLG:c,zIndexPopup:l,titleMarginBottom:u,colorBgElevated:f,popoverBg:d,titleBorderBottom:p,innerContentPadding:h,titlePadding:m}=e;return[{[t]:Object.assign(Object.assign({},Yi(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:l,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text",transformOrigin:"var(--arrow-x, 50%) var(--arrow-y, 50%)","--antd-arrow-background-color":f,"&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:d,backgroundClip:"padding-box",borderRadius:c,boxShadow:a,padding:i},[`${t}-title`]:{minWidth:r,marginBottom:u,color:s,fontWeight:o,borderBottom:p,padding:m},[`${t}-inner-content`]:{color:n,padding:h}})},Li(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},ls=e=>{const{componentCls:t}=e;return{[t]:ca.map((n=>{const r=e[`${n}6`];return{[`&${t}-${n}`]:{"--antd-arrow-background-color":r,[`${t}-inner`]:{backgroundColor:r},[`${t}-arrow`]:{background:"transparent"}}}}))}},us=Ca("Popover",(e=>{const{colorBgElevated:t,colorText:n}=e,r=da(e,{popoverBg:t,popoverColor:n});return[cs(r),ls(r),sa(r,"zoom-big")]}),(e=>{const{lineWidth:t,controlHeight:n,fontHeight:r,padding:o,wireframe:i,zIndexPopupBase:a,borderRadiusLG:s,marginXS:c,lineType:l,colorSplit:u,paddingSM:f}=e,d=n-r,p=d/2,h=d/2-t,m=o;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:a+30},Ti(e)),Ni({contentRadius:s,limitVerticalRadius:!0})),{innerPadding:i?0:12,titleMarginBottom:i?0:c,titlePadding:i?`${p}px ${m}px ${h}px`:0,titleBorderBottom:i?`${t}px ${l} ${u}`:"none",innerContentPadding:i?`${f}px ${m}px`:0})}),{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});const fs=t=>{const{hashId:n,prefixCls:r,className:o,style:i,placement:a="top",title:s,content:c,children:f}=t;return e.createElement("div",{className:l()(n,r,`${r}-pure`,`${r}-placement-${a}`,o),style:i},e.createElement("div",{className:`${r}-arrow`}),e.createElement(u,Object.assign({},t,{className:n,prefixCls:r}),f||((t,n,r)=>n||r?e.createElement(e.Fragment,null,n&&e.createElement("div",{className:`${t}-title`},ss(n)),e.createElement("div",{className:`${t}-inner-content`},ss(r))):null)(r,s,c)))};const ds=t=>{let{title:n,content:r,prefixCls:o}=t;return e.createElement(e.Fragment,null,n&&e.createElement("div",{className:`${o}-title`},ss(n)),e.createElement("div",{className:`${o}-inner-content`},ss(r)))},ps=e.forwardRef(((t,n)=>{const{prefixCls:r,title:o,content:i,overlayClassName:a,placement:s="top",trigger:c="hover",mouseEnterDelay:u=.1,mouseLeaveDelay:f=.1,overlayStyle:d={}}=t,p=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(t,["prefixCls","title","content","overlayClassName","placement","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle"]),{getPrefixCls:h}=e.useContext(Xi),m=h("popover",r),[g,v,b]=us(m),y=h(),w=l()(a,v,b);return g(e.createElement(Ra,Object.assign({placement:s,trigger:c,mouseEnterDelay:u,mouseLeaveDelay:f,overlayStyle:d},p,{prefixCls:m,overlayClassName:w,ref:n,overlay:o||i?e.createElement(ds,{prefixCls:m,title:o,content:i}):null,transitionName:Ri(y,"zoom-big",p.transitionName),"data-popover-inject":!0})))}));ps._InternalPanelDoNotUseOrYouWillBeFired=t=>{const{prefixCls:n,className:r}=t,o=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(t,["prefixCls","className"]),{getPrefixCls:i}=e.useContext(Xi),a=i("popover",n),[s,c,u]=us(a);return s(e.createElement(fs,Object.assign({},o,{prefixCls:a,hashId:c,className:l()(r,u)})))};const hs=ps,ms=t=>{const{size:n,shape:r}=e.useContext(ts),o=e.useMemo((()=>({size:t.size||n,shape:t.shape||r})),[t.size,t.shape,n,r]);return e.createElement(ts.Provider,{value:o},t.children)},gs=as;gs.Group=t=>{const{getPrefixCls:n,direction:r}=e.useContext(Xi),{prefixCls:o,className:i,rootClassName:a,style:s,maxCount:c,maxStyle:u,size:f,shape:d,maxPopoverPlacement:p="top",maxPopoverTrigger:h="hover",children:m}=t,g=n("avatar",o),v=`${g}-group`,b=Qa(g),[y,w,x]=os(g,b),S=l()(v,{[`${v}-rtl`]:"rtl"===r},x,b,i,a,w),k=se(m).map(((e,t)=>Di(e,{key:`avatar-key-${t}`}))),C=k.length;if(c&&c<C){const t=k.slice(0,c),n=k.slice(c,C);return t.push(e.createElement(hs,{key:"avatar-popover-key",content:n,trigger:h,placement:p,overlayClassName:`${v}-popover`,destroyTooltipOnHide:!0},e.createElement(as,{style:u},"+"+(C-c)))),y(e.createElement(ms,{shape:d,size:f},e.createElement("div",{className:S,style:s},t)))}return y(e.createElement(ms,{shape:d,size:f},e.createElement("div",{className:S,style:s},k)))};const vs=gs,bs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M678.3 642.4c24.2-13 51.9-20.4 81.4-20.4h.1c3 0 4.4-3.6 2.2-5.6a371.67 371.67 0 00-103.7-65.8c-.4-.2-.8-.3-1.2-.5C719.2 505 759.6 431.7 759.6 349c0-137-110.8-248-247.5-248S264.7 212 264.7 349c0 82.7 40.4 156 102.6 201.1-.4.2-.8.3-1.2.5-44.7 18.9-84.8 46-119.3 80.6a373.42 373.42 0 00-80.4 119.5A373.6 373.6 0 00137 888.8a8 8 0 008 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C357 628.2 432.2 597 512.2 597c56.7 0 111.1 15.7 158 45.1a8.1 8.1 0 008.1.3zM512.2 521c-45.8 0-88.9-17.9-121.4-50.4A171.2 171.2 0 01340.5 349c0-45.9 17.9-89.1 50.3-121.6S466.3 177 512.2 177s88.9 17.9 121.4 50.4A171.2 171.2 0 01683.9 349c0 45.9-17.9 89.1-50.3 121.6C601.1 503.1 558 521 512.2 521zM880 759h-84v-84c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v84h-84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h84v84c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-84h84c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"user-add",theme:"outlined"},ys=(0,e.createContext)({});function ws(e){return"object"===d(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===d(e.icon)||"function"==typeof e.icon)}function xs(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce((function(t,n){var r,o=e[n];return"class"===n?(t.className=o,delete t.class):(delete t[n],t[(r=n,r.replace(/-(.)/g,(function(e,t){return t.toUpperCase()})))]=o),t}),{})}function Ss(e,n,r){return r?t().createElement(e.tag,g(g({key:n},xs(e.attrs)),r),(e.children||[]).map((function(t,r){return Ss(t,"".concat(n,"-").concat(e.tag,"-").concat(r))}))):t().createElement(e.tag,g({key:n},xs(e.attrs)),(e.children||[]).map((function(t,r){return Ss(t,"".concat(n,"-").concat(e.tag,"-").concat(r))})))}function ks(e){return ti(e)[0]}function Cs(e){return e?Array.isArray(e)?e:[e]:[]}var Os=["icon","className","onClick","style","primaryColor","secondaryColor"],Es={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1},$s=function(t){var n,r,o,i,a,s,c,l=t.icon,u=t.className,f=t.onClick,d=t.style,p=t.primaryColor,h=t.secondaryColor,m=v(t,Os),b=e.useRef(),y=Es;if(p&&(y={primaryColor:p,secondaryColor:h||ks(p)}),n=b,r=(0,e.useContext)(ys),o=r.csp,i=r.prefixCls,a="\n.anticon {\n  display: inline-flex;\n  alignItems: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n",i&&(a=a.replace(/anticon/g,i)),(0,e.useEffect)((function(){var e=Ke(n.current);J(a,"@ant-design-icons",{prepend:!0,csp:o,attachTo:e})}),[]),s=ws(l),c="icon should be icon definiton, but got ".concat(l),M(s,"[@ant-design/icons] ".concat(c)),!ws(l))return null;var w=l;return w&&"function"==typeof w.icon&&(w=g(g({},w),{},{icon:w.icon(y.primaryColor,y.secondaryColor)})),Ss(w.icon,"svg-".concat(w.name),g(g({className:u,onClick:f,style:d,"data-icon":w.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},m),{},{ref:b}))};$s.displayName="IconReact",$s.getTwoToneColors=function(){return g({},Es)},$s.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;Es.primaryColor=t,Es.secondaryColor=n||ks(t),Es.calculated=!!n};const As=$s;function js(e){var t=w(Cs(e),2),n=t[0],r=t[1];return As.setTwoToneColors({primaryColor:n,secondaryColor:r})}var Ms=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];js(ii.primary);var Ps=e.forwardRef((function(t,n){var r=t.className,o=t.icon,i=t.spin,a=t.rotate,s=t.tabIndex,c=t.onClick,u=t.twoToneColor,d=v(t,Ms),p=e.useContext(ys),m=p.prefixCls,g=void 0===m?"anticon":m,b=p.rootClassName,y=l()(b,g,h(h({},"".concat(g,"-").concat(o.name),!!o.name),"".concat(g,"-spin"),!!i||"loading"===o.name),r),x=s;void 0===x&&c&&(x=-1);var S=a?{msTransform:"rotate(".concat(a,"deg)"),transform:"rotate(".concat(a,"deg)")}:void 0,k=w(Cs(u),2),C=k[0],O=k[1];return e.createElement("span",f({role:"img","aria-label":o.name},d,{ref:n,tabIndex:x,onClick:c,className:y}),e.createElement(As,{icon:o,primaryColor:C,secondaryColor:O,style:S}))}));Ps.displayName="AntdIcon",Ps.getTwoToneColor=function(){var e=As.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor},Ps.setTwoToneColor=js;const Rs=Ps;var Ts=function(t,n){return e.createElement(Rs,f({},t,{ref:n,icon:bs}))};const zs=e.forwardRef(Ts);var _s=n(2833),Ns=n.n(_s);const Is=function(e){function t(e,r,c,l,d){for(var p,h,m,g,w,S=0,k=0,C=0,O=0,E=0,R=0,z=m=p=0,N=0,I=0,L=0,H=0,B=c.length,F=B-1,D="",W="",X="",q="";N<B;){if(h=c.charCodeAt(N),N===F&&0!==k+O+C+S&&(0!==k&&(h=47===k?10:47),O=C=S=0,B++,F++),0===k+O+C+S){if(N===F&&(0<I&&(D=D.replace(f,"")),0<D.trim().length)){switch(h){case 32:case 9:case 59:case 13:case 10:break;default:D+=c.charAt(N)}h=59}switch(h){case 123:for(p=(D=D.trim()).charCodeAt(0),m=1,H=++N;N<B;){switch(h=c.charCodeAt(N)){case 123:m++;break;case 125:m--;break;case 47:switch(h=c.charCodeAt(N+1)){case 42:case 47:e:{for(z=N+1;z<F;++z)switch(c.charCodeAt(z)){case 47:if(42===h&&42===c.charCodeAt(z-1)&&N+2!==z){N=z+1;break e}break;case 10:if(47===h){N=z+1;break e}}N=z}}break;case 91:h++;case 40:h++;case 34:case 39:for(;N++<F&&c.charCodeAt(N)!==h;);}if(0===m)break;N++}if(m=c.substring(H,N),0===p&&(p=(D=D.replace(u,"").trim()).charCodeAt(0)),64===p){switch(0<I&&(D=D.replace(f,"")),h=D.charCodeAt(1)){case 100:case 109:case 115:case 45:I=r;break;default:I=P}if(H=(m=t(r,I,m,h,d+1)).length,0<T&&(w=s(3,m,I=n(P,D,L),r,A,$,H,h,d,l),D=I.join(""),void 0!==w&&0===(H=(m=w.trim()).length)&&(h=0,m="")),0<H)switch(h){case 115:D=D.replace(x,a);case 100:case 109:case 45:m=D+"{"+m+"}";break;case 107:m=(D=D.replace(v,"$1 $2"))+"{"+m+"}",m=1===M||2===M&&i("@"+m,3)?"@-webkit-"+m+"@"+m:"@"+m;break;default:m=D+m,112===l&&(W+=m,m="")}else m=""}else m=t(r,n(r,D,L),m,l,d+1);X+=m,m=L=I=z=p=0,D="",h=c.charCodeAt(++N);break;case 125:case 59:if(1<(H=(D=(0<I?D.replace(f,""):D).trim()).length))switch(0===z&&(p=D.charCodeAt(0),45===p||96<p&&123>p)&&(H=(D=D.replace(" ",":")).length),0<T&&void 0!==(w=s(1,D,r,e,A,$,W.length,l,d,l))&&0===(H=(D=w.trim()).length)&&(D="\0\0"),p=D.charCodeAt(0),h=D.charCodeAt(1),p){case 0:break;case 64:if(105===h||99===h){q+=D+c.charAt(N);break}default:58!==D.charCodeAt(H-1)&&(W+=o(D,p,h,D.charCodeAt(2)))}L=I=z=p=0,D="",h=c.charCodeAt(++N)}}switch(h){case 13:case 10:47===k?k=0:0===1+p&&107!==l&&0<D.length&&(I=1,D+="\0"),0<T*_&&s(0,D,r,e,A,$,W.length,l,d,l),$=1,A++;break;case 59:case 125:if(0===k+O+C+S){$++;break}default:switch($++,g=c.charAt(N),h){case 9:case 32:if(0===O+S+k)switch(E){case 44:case 58:case 9:case 32:g="";break;default:32!==h&&(g=" ")}break;case 0:g="\\0";break;case 12:g="\\f";break;case 11:g="\\v";break;case 38:0===O+k+S&&(I=L=1,g="\f"+g);break;case 108:if(0===O+k+S+j&&0<z)switch(N-z){case 2:112===E&&58===c.charCodeAt(N-3)&&(j=E);case 8:111===R&&(j=R)}break;case 58:0===O+k+S&&(z=N);break;case 44:0===k+C+O+S&&(I=1,g+="\r");break;case 34:case 39:0===k&&(O=O===h?0:0===O?h:O);break;case 91:0===O+k+C&&S++;break;case 93:0===O+k+C&&S--;break;case 41:0===O+k+S&&C--;break;case 40:0===O+k+S&&(0===p&&(2*E+3*R==533||(p=1)),C++);break;case 64:0===k+C+O+S+z+m&&(m=1);break;case 42:case 47:if(!(0<O+S+C))switch(k){case 0:switch(2*h+3*c.charCodeAt(N+1)){case 235:k=47;break;case 220:H=N,k=42}break;case 42:47===h&&42===E&&H+2!==N&&(33===c.charCodeAt(H+2)&&(W+=c.substring(H,N+1)),g="",k=0)}}0===k&&(D+=g)}R=E,E=h,N++}if(0<(H=W.length)){if(I=r,0<T&&void 0!==(w=s(2,W,I,e,A,$,H,l,d,l))&&0===(W=w).length)return q+W+X;if(W=I.join(",")+"{"+W+"}",0!=M*j){switch(2!==M||i(W,2)||(j=0),j){case 111:W=W.replace(y,":-moz-$1")+W;break;case 112:W=W.replace(b,"::-webkit-input-$1")+W.replace(b,"::-moz-$1")+W.replace(b,":-ms-input-$1")+W}j=0}}return q+W+X}function n(e,t,n){var o=t.trim().split(m);t=o;var i=o.length,a=e.length;switch(a){case 0:case 1:var s=0;for(e=0===a?"":e[0]+" ";s<i;++s)t[s]=r(e,t[s],n).trim();break;default:var c=s=0;for(t=[];s<i;++s)for(var l=0;l<a;++l)t[c++]=r(e[l]+" ",o[s],n).trim()}return t}function r(e,t,n){var r=t.charCodeAt(0);switch(33>r&&(r=(t=t.trim()).charCodeAt(0)),r){case 38:return t.replace(g,"$1"+e.trim());case 58:return e.trim()+t.replace(g,"$1"+e.trim());default:if(0<1*n&&0<t.indexOf("\f"))return t.replace(g,(58===e.charCodeAt(0)?"":"$1")+e.trim())}return e+t}function o(e,t,n,r){var a=e+";",s=2*t+3*n+4*r;if(944===s){e=a.indexOf(":",9)+1;var c=a.substring(e,a.length-1).trim();return c=a.substring(0,e).trim()+c+";",1===M||2===M&&i(c,1)?"-webkit-"+c+c:c}if(0===M||2===M&&!i(a,1))return a;switch(s){case 1015:return 97===a.charCodeAt(10)?"-webkit-"+a+a:a;case 951:return 116===a.charCodeAt(3)?"-webkit-"+a+a:a;case 963:return 110===a.charCodeAt(5)?"-webkit-"+a+a:a;case 1009:if(100!==a.charCodeAt(4))break;case 969:case 942:return"-webkit-"+a+a;case 978:return"-webkit-"+a+"-moz-"+a+a;case 1019:case 983:return"-webkit-"+a+"-moz-"+a+"-ms-"+a+a;case 883:if(45===a.charCodeAt(8))return"-webkit-"+a+a;if(0<a.indexOf("image-set(",11))return a.replace(E,"$1-webkit-$2")+a;break;case 932:if(45===a.charCodeAt(4))switch(a.charCodeAt(5)){case 103:return"-webkit-box-"+a.replace("-grow","")+"-webkit-"+a+"-ms-"+a.replace("grow","positive")+a;case 115:return"-webkit-"+a+"-ms-"+a.replace("shrink","negative")+a;case 98:return"-webkit-"+a+"-ms-"+a.replace("basis","preferred-size")+a}return"-webkit-"+a+"-ms-"+a+a;case 964:return"-webkit-"+a+"-ms-flex-"+a+a;case 1023:if(99!==a.charCodeAt(8))break;return"-webkit-box-pack"+(c=a.substring(a.indexOf(":",15)).replace("flex-","").replace("space-between","justify"))+"-webkit-"+a+"-ms-flex-pack"+c+a;case 1005:return p.test(a)?a.replace(d,":-webkit-")+a.replace(d,":-moz-")+a:a;case 1e3:switch(t=(c=a.substring(13).trim()).indexOf("-")+1,c.charCodeAt(0)+c.charCodeAt(t)){case 226:c=a.replace(w,"tb");break;case 232:c=a.replace(w,"tb-rl");break;case 220:c=a.replace(w,"lr");break;default:return a}return"-webkit-"+a+"-ms-"+c+a;case 1017:if(-1===a.indexOf("sticky",9))break;case 975:switch(t=(a=e).length-10,s=(c=(33===a.charCodeAt(t)?a.substring(0,t):a).substring(e.indexOf(":",7)+1).trim()).charCodeAt(0)+(0|c.charCodeAt(7))){case 203:if(111>c.charCodeAt(8))break;case 115:a=a.replace(c,"-webkit-"+c)+";"+a;break;case 207:case 102:a=a.replace(c,"-webkit-"+(102<s?"inline-":"")+"box")+";"+a.replace(c,"-webkit-"+c)+";"+a.replace(c,"-ms-"+c+"box")+";"+a}return a+";";case 938:if(45===a.charCodeAt(5))switch(a.charCodeAt(6)){case 105:return c=a.replace("-items",""),"-webkit-"+a+"-webkit-box-"+c+"-ms-flex-"+c+a;case 115:return"-webkit-"+a+"-ms-flex-item-"+a.replace(k,"")+a;default:return"-webkit-"+a+"-ms-flex-line-pack"+a.replace("align-content","").replace(k,"")+a}break;case 973:case 989:if(45!==a.charCodeAt(3)||122===a.charCodeAt(4))break;case 931:case 953:if(!0===O.test(e))return 115===(c=e.substring(e.indexOf(":")+1)).charCodeAt(0)?o(e.replace("stretch","fill-available"),t,n,r).replace(":fill-available",":stretch"):a.replace(c,"-webkit-"+c)+a.replace(c,"-moz-"+c.replace("fill-",""))+a;break;case 962:if(a="-webkit-"+a+(102===a.charCodeAt(5)?"-ms-"+a:"")+a,211===n+r&&105===a.charCodeAt(13)&&0<a.indexOf("transform",10))return a.substring(0,a.indexOf(";",27)+1).replace(h,"$1-webkit-$2")+a}return a}function i(e,t){var n=e.indexOf(1===t?":":"{"),r=e.substring(0,3!==t?n:10);return n=e.substring(n+1,e.length-1),z(2!==t?r:r.replace(C,"$1"),n,t)}function a(e,t){var n=o(t,t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2));return n!==t+";"?n.replace(S," or ($1)").substring(4):"("+t+")"}function s(e,t,n,r,o,i,a,s,c,u){for(var f,d=0,p=t;d<T;++d)switch(f=R[d].call(l,e,p,n,r,o,i,a,s,c,u)){case void 0:case!1:case!0:case null:break;default:p=f}if(p!==t)return p}function c(e){return void 0!==(e=e.prefix)&&(z=null,e?"function"!=typeof e?M=1:(M=2,z=e):M=0),c}function l(e,n){var r=e;if(33>r.charCodeAt(0)&&(r=r.trim()),r=[r],0<T){var o=s(-1,n,r,r,A,$,0,0,0,0);void 0!==o&&"string"==typeof o&&(n=o)}var i=t(P,r,n,0,0);return 0<T&&void 0!==(o=s(-2,i,r,r,A,$,i.length,0,0,0))&&(i=o),j=0,$=A=1,i}var u=/^\0+/g,f=/[\0\r\f]/g,d=/: */g,p=/zoo|gra/,h=/([,: ])(transform)/g,m=/,\r+?/g,g=/([\t\r\n ])*\f?&/g,v=/@(k\w+)\s*(\S*)\s*/,b=/::(place)/g,y=/:(read-only)/g,w=/[svh]\w+-[tblr]{2}/,x=/\(\s*(.*)\s*\)/g,S=/([\s\S]*?);/g,k=/-self|flex-/g,C=/[^]*?(:[rp][el]a[\w-]+)[^]*/,O=/stretch|:\s*\w+\-(?:conte|avail)/,E=/([^-])(image-set\()/,$=1,A=1,j=0,M=1,P=[],R=[],T=0,z=null,_=0;return l.use=function e(t){switch(t){case void 0:case null:T=R.length=0;break;default:if("function"==typeof t)R[T++]=t;else if("object"==typeof t)for(var n=0,r=t.length;n<r;++n)e(t[n]);else _=0|!!t}return e},l.set=c,void 0!==e&&c(e),l};function Ls(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var Hs=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Bs=Ls((function(e){return Hs.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),Fs=n(4146),Ds=n.n(Fs);function Ws(){return(Ws=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Xs=function(e,t){for(var n=[e[0]],r=0,o=t.length;r<o;r+=1)n.push(t[r],e[r+1]);return n},qs=function(e){return null!==e&&"object"==typeof e&&"[object Object]"===(e.toString?e.toString():Object.prototype.toString.call(e))&&!(0,P.typeOf)(e)},Vs=Object.freeze([]),Gs=Object.freeze({});function Ys(e){return"function"==typeof e}function Us(e){return e.displayName||e.name||"Component"}function Zs(e){return e&&"string"==typeof e.styledComponentId}var Ks="undefined"!=typeof process&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",Qs="undefined"!=typeof window&&"HTMLElement"in window,Js=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&"false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY);function ec(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw new Error("An error occurred. See https://git.io/JUIaE#"+e+" for more information."+(n.length>0?" Args: "+n.join(", "):""))}var tc=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}var t=e.prototype;return t.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},t.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,o=r;e>=o;)(o<<=1)<0&&ec(16,""+e);this.groupSizes=new Uint32Array(o),this.groupSizes.set(n),this.length=o;for(var i=r;i<o;i++)this.groupSizes[i]=0}for(var a=this.indexOfGroup(e+1),s=0,c=t.length;s<c;s++)this.tag.insertRule(a,t[s])&&(this.groupSizes[e]++,a++)},t.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var o=n;o<r;o++)this.tag.deleteRule(n)}},t.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),o=r+n,i=r;i<o;i++)t+=this.tag.getRule(i)+"/*!sc*/\n";return t},e}(),nc=new Map,rc=new Map,oc=1,ic=function(e){if(nc.has(e))return nc.get(e);for(;rc.has(oc);)oc++;var t=oc++;return nc.set(e,t),rc.set(t,e),t},ac=function(e){return rc.get(e)},sc=function(e,t){t>=oc&&(oc=t+1),nc.set(e,t),rc.set(t,e)},cc="style["+Ks+'][data-styled-version="5.3.5"]',lc=new RegExp("^"+Ks+'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)'),uc=function(e,t,n){for(var r,o=n.split(","),i=0,a=o.length;i<a;i++)(r=o[i])&&e.registerName(t,r)},fc=function(e,t){for(var n=(t.textContent||"").split("/*!sc*/\n"),r=[],o=0,i=n.length;o<i;o++){var a=n[o].trim();if(a){var s=a.match(lc);if(s){var c=0|parseInt(s[1],10),l=s[2];0!==c&&(sc(l,c),uc(e,l,s[3]),e.getTag().insertRules(c,r)),r.length=0}else r.push(a)}}},dc=function(){return"undefined"!=typeof window&&void 0!==window.__webpack_nonce__?window.__webpack_nonce__:null},pc=function(e){var t=document.head,n=e||t,r=document.createElement("style"),o=function(e){for(var t=e.childNodes,n=t.length;n>=0;n--){var r=t[n];if(r&&1===r.nodeType&&r.hasAttribute(Ks))return r}}(n),i=void 0!==o?o.nextSibling:null;r.setAttribute(Ks,"active"),r.setAttribute("data-styled-version","5.3.5");var a=dc();return a&&r.setAttribute("nonce",a),n.insertBefore(r,i),r},hc=function(){function e(e){var t=this.element=pc(e);t.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var o=t[n];if(o.ownerNode===e)return o}ec(17)}(t),this.length=0}var t=e.prototype;return t.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},t.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},t.getRule=function(e){var t=this.sheet.cssRules[e];return void 0!==t&&"string"==typeof t.cssText?t.cssText:""},e}(),mc=function(){function e(e){var t=this.element=pc(e);this.nodes=t.childNodes,this.length=0}var t=e.prototype;return t.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t),r=this.nodes[e];return this.element.insertBefore(n,r||null),this.length++,!0}return!1},t.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},t.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),gc=function(){function e(e){this.rules=[],this.length=0}var t=e.prototype;return t.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},t.deleteRule=function(e){this.rules.splice(e,1),this.length--},t.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),vc=Qs,bc={isServer:!Qs,useCSSOMInjection:!Js},yc=function(){function e(e,t,n){void 0===e&&(e=Gs),void 0===t&&(t={}),this.options=Ws({},bc,{},e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&Qs&&vc&&(vc=!1,function(e){for(var t=document.querySelectorAll(cc),n=0,r=t.length;n<r;n++){var o=t[n];o&&"active"!==o.getAttribute(Ks)&&(fc(e,o),o.parentNode&&o.parentNode.removeChild(o))}}(this))}e.registerId=function(e){return ic(e)};var t=e.prototype;return t.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(Ws({},this.options,{},t),this.gs,n&&this.names||void 0)},t.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},t.getTag=function(){return this.tag||(this.tag=(n=(t=this.options).isServer,r=t.useCSSOMInjection,o=t.target,e=n?new gc(o):r?new hc(o):new mc(o),new tc(e)));var e,t,n,r,o},t.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},t.registerName=function(e,t){if(ic(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},t.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(ic(e),n)},t.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},t.clearRules=function(e){this.getTag().clearGroup(ic(e)),this.clearNames(e)},t.clearTag=function(){this.tag=void 0},t.toString=function(){return function(e){for(var t=e.getTag(),n=t.length,r="",o=0;o<n;o++){var i=ac(o);if(void 0!==i){var a=e.names.get(i),s=t.getGroup(o);if(a&&s&&a.size){var c=Ks+".g"+o+'[id="'+i+'"]',l="";void 0!==a&&a.forEach((function(e){e.length>0&&(l+=e+",")})),r+=""+s+c+'{content:"'+l+'"}/*!sc*/\n'}}}return r}(this)},e}(),wc=/(a)(d)/gi,xc=function(e){return String.fromCharCode(e+(e>25?39:97))};function Sc(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=xc(t%52)+n;return(xc(t%52)+n).replace(wc,"$1-$2")}var kc=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},Cc=function(e){return kc(5381,e)};function Oc(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(Ys(n)&&!Zs(n))return!1}return!0}var Ec=Cc("5.3.5"),$c=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&Oc(e),this.componentId=t,this.baseHash=kc(Ec,t),this.baseStyle=n,yc.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.componentId,o=[];if(this.baseStyle&&o.push(this.baseStyle.generateAndInjectStyles(e,t,n)),this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(r,this.staticRulesId))o.push(this.staticRulesId);else{var i=Xc(this.rules,e,t,n).join(""),a=Sc(kc(this.baseHash,i)>>>0);if(!t.hasNameForId(r,a)){var s=n(i,"."+a,void 0,r);t.insertRules(r,a,s)}o.push(a),this.staticRulesId=a}else{for(var c=this.rules.length,l=kc(this.baseHash,n.hash),u="",f=0;f<c;f++){var d=this.rules[f];if("string"==typeof d)u+=d;else if(d){var p=Xc(d,e,t,n),h=Array.isArray(p)?p.join(""):p;l=kc(l,h+f),u+=h}}if(u){var m=Sc(l>>>0);if(!t.hasNameForId(r,m)){var g=n(u,"."+m,void 0,r);t.insertRules(r,m,g)}o.push(m)}}return o.join(" ")},e}(),Ac=/^\s*\/\/.*$/gm,jc=[":","[",".","#"];function Mc(e){var t,n,r,o,i=void 0===e?Gs:e,a=i.options,s=void 0===a?Gs:a,c=i.plugins,l=void 0===c?Vs:c,u=new Is(s),f=[],d=function(e){function t(t){if(t)try{e(t+"}")}catch(e){}}return function(n,r,o,i,a,s,c,l,u,f){switch(n){case 1:if(0===u&&64===r.charCodeAt(0))return e(r+";"),"";break;case 2:if(0===l)return r+"/*|*/";break;case 3:switch(l){case 102:case 112:return e(o[0]+r),"";default:return r+(0===f?"/*|*/":"")}case-2:r.split("/*|*/}").forEach(t)}}}((function(e){f.push(e)})),p=function(e,r,i){return 0===r&&-1!==jc.indexOf(i[n.length])||i.match(o)?e:"."+t};function h(e,i,a,s){void 0===s&&(s="&");var c=e.replace(Ac,""),l=i&&a?a+" "+i+" { "+c+" }":c;return t=s,n=i,r=new RegExp("\\"+n+"\\b","g"),o=new RegExp("(\\"+n+"\\b){2,}"),u(a||!i?"":i,l)}return u.use([].concat(l,[function(e,t,o){2===e&&o.length&&o[0].lastIndexOf(n)>0&&(o[0]=o[0].replace(r,p))},d,function(e){if(-2===e){var t=f;return f=[],t}}])),h.hash=l.length?l.reduce((function(e,t){return t.name||ec(15),kc(e,t.name)}),5381).toString():"",h}var Pc=t().createContext(),Rc=(Pc.Consumer,t().createContext()),Tc=(Rc.Consumer,new yc),zc=Mc();function _c(){return(0,e.useContext)(Pc)||Tc}function Nc(n){var r=(0,e.useState)(n.stylisPlugins),o=r[0],i=r[1],a=_c(),s=(0,e.useMemo)((function(){var e=a;return n.sheet?e=n.sheet:n.target&&(e=e.reconstructWithOptions({target:n.target},!1)),n.disableCSSOMInjection&&(e=e.reconstructWithOptions({useCSSOMInjection:!1})),e}),[n.disableCSSOMInjection,n.sheet,n.target]),c=(0,e.useMemo)((function(){return Mc({options:{prefix:!n.disableVendorPrefixes},plugins:o})}),[n.disableVendorPrefixes,o]);return(0,e.useEffect)((function(){Ns()(o,n.stylisPlugins)||i(n.stylisPlugins)}),[n.stylisPlugins]),t().createElement(Pc.Provider,{value:s},t().createElement(Rc.Provider,{value:c},n.children))}var Ic=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=zc);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.toString=function(){return ec(12,String(n.name))},this.name=e,this.id="sc-keyframes-"+e,this.rules=t}return e.prototype.getName=function(e){return void 0===e&&(e=zc),this.name+e.hash},e}(),Lc=/([A-Z])/,Hc=/([A-Z])/g,Bc=/^ms-/,Fc=function(e){return"-"+e.toLowerCase()};function Dc(e){return Lc.test(e)?e.replace(Hc,Fc).replace(Bc,"-ms-"):e}var Wc=function(e){return null==e||!1===e||""===e};function Xc(e,t,n,r){if(Array.isArray(e)){for(var o,i=[],a=0,s=e.length;a<s;a+=1)""!==(o=Xc(e[a],t,n,r))&&(Array.isArray(o)?i.push.apply(i,o):i.push(o));return i}return Wc(e)?"":Zs(e)?"."+e.styledComponentId:Ys(e)?"function"!=typeof(c=e)||c.prototype&&c.prototype.isReactComponent||!t?e:Xc(e(t),t,n,r):e instanceof Ic?n?(e.inject(n,r),e.getName(r)):e:qs(e)?function e(t,n){var r,o,i=[];for(var a in t)t.hasOwnProperty(a)&&!Wc(t[a])&&(Array.isArray(t[a])&&t[a].isCss||Ys(t[a])?i.push(Dc(a)+":",t[a],";"):qs(t[a])?i.push.apply(i,e(t[a],a)):i.push(Dc(a)+": "+(r=a,(null==(o=t[a])||"boolean"==typeof o||""===o?"":"number"!=typeof o||0===o||r in yr?String(o).trim():o+"px")+";")));return n?[n+" {"].concat(i,["}"]):i}(e):e.toString();var c}var qc=function(e){return Array.isArray(e)&&(e.isCss=!0),e};function Vc(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Ys(e)||qs(e)?qc(Xc(Xs(Vs,[e].concat(n)))):0===n.length&&1===e.length&&"string"==typeof e[0]?e:qc(Xc(Xs(e,n)))}new Set;var Gc=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,Yc=/(^-|-$)/g;function Uc(e){return e.replace(Gc,"-").replace(Yc,"")}function Zc(e){return"string"==typeof e&&!0}var Kc=function(e){return"function"==typeof e||"object"==typeof e&&null!==e&&!Array.isArray(e)},Qc=function(e){return"__proto__"!==e&&"constructor"!==e&&"prototype"!==e};function Jc(e,t,n){var r=e[n];Kc(t)&&Kc(r)?el(r,t):e[n]=t}function el(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var o=0,i=n;o<i.length;o++){var a=i[o];if(Kc(a))for(var s in a)Qc(s)&&Jc(e,a[s],s)}return e}var tl=t().createContext();tl.Consumer;var nl={};function rl(n,r,o){var i=Zs(n),a=!Zc(n),s=r.attrs,c=void 0===s?Vs:s,l=r.componentId,u=void 0===l?function(e,t){var n="string"!=typeof e?"sc":Uc(e);nl[n]=(nl[n]||0)+1;var r=n+"-"+function(e){return Sc(Cc(e)>>>0)}("5.3.5"+n+nl[n]);return t?t+"-"+r:r}(r.displayName,r.parentComponentId):l,f=r.displayName,d=void 0===f?function(e){return Zc(e)?"styled."+e:"Styled("+Us(e)+")"}(n):f,p=r.displayName&&r.componentId?Uc(r.displayName)+"-"+r.componentId:r.componentId||u,h=i&&n.attrs?Array.prototype.concat(n.attrs,c).filter(Boolean):c,m=r.shouldForwardProp;i&&n.shouldForwardProp&&(m=r.shouldForwardProp?function(e,t,o){return n.shouldForwardProp(e,t,o)&&r.shouldForwardProp(e,t,o)}:n.shouldForwardProp);var g,v=new $c(o,p,i?n.componentStyle:void 0),b=v.isStatic&&0===c.length,y=function(t,n){return function(t,n,r,o){var i=t.attrs,a=t.componentStyle,s=t.defaultProps,c=t.foldedComponentIds,l=t.shouldForwardProp,u=t.styledComponentId,f=t.target,d=function(e,t,n){void 0===e&&(e=Gs);var r=Ws({},t,{theme:e}),o={};return n.forEach((function(e){var t,n,i,a=e;for(t in Ys(a)&&(a=a(r)),a)r[t]=o[t]="className"===t?(n=o[t],i=a[t],n&&i?n+" "+i:n||i):a[t]})),[r,o]}(function(e,t,n){return void 0===n&&(n=Gs),e.theme!==n.theme&&e.theme||t||n.theme}(n,(0,e.useContext)(tl),s)||Gs,n,i),p=d[0],h=d[1],m=function(t,n,r,o){var i=_c(),a=(0,e.useContext)(Rc)||zc;return n?t.generateAndInjectStyles(Gs,i,a):t.generateAndInjectStyles(r,i,a)}(a,o,p),g=r,v=h.$as||n.$as||h.as||n.as||f,b=Zc(v),y=h!==n?Ws({},n,{},h):n,w={};for(var x in y)"$"!==x[0]&&"as"!==x&&("forwardedAs"===x?w.as=y[x]:(l?l(x,Bs,v):!b||Bs(x))&&(w[x]=y[x]));return n.style&&h.style!==n.style&&(w.style=Ws({},n.style,{},h.style)),w.className=Array.prototype.concat(c,u,m!==u?m:null,n.className,h.className).filter(Boolean).join(" "),w.ref=g,(0,e.createElement)(v,w)}(g,t,n,b)};return y.displayName=d,(g=t().forwardRef(y)).attrs=h,g.componentStyle=v,g.displayName=d,g.shouldForwardProp=m,g.foldedComponentIds=i?Array.prototype.concat(n.foldedComponentIds,n.styledComponentId):Vs,g.styledComponentId=p,g.target=i?n.target:n,g.withComponent=function(e){var t=r.componentId,n=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(r,["componentId"]),i=t&&t+"-"+(Zc(e)?e:Uc(Us(e)));return rl(e,Ws({},n,{attrs:h,componentId:i}),o)},Object.defineProperty(g,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=i?el({},n.defaultProps,e):e}}),g.toString=function(){return"."+g.styledComponentId},a&&Ds()(g,n,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0,withComponent:!0}),g}var ol,il=function(e){return function e(t,n,r){if(void 0===r&&(r=Gs),!(0,P.isValidElementType)(n))return ec(1,String(n));var o=function(){return t(n,r,Vc.apply(void 0,arguments))};return o.withConfig=function(o){return e(t,n,Ws({},r,{},o))},o.attrs=function(o){return e(t,n,Ws({},r,{attrs:Array.prototype.concat(r.attrs,o).filter(Boolean)}))},o}(rl,e)};["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","textPath","tspan"].forEach((function(e){il[e]=il(e)})),(ol=function(e,t){this.rules=e,this.componentId=t,this.isStatic=Oc(e),yc.registerId(this.componentId+1)}.prototype).createStyles=function(e,t,n,r){var o=r(Xc(this.rules,t,n,r).join(""),""),i=this.componentId+e;n.insertRules(i,i,o)},ol.removeStyles=function(e,t){t.clearRules(this.componentId+e)},ol.renderStyles=function(e,t,n,r){e>2&&yc.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,r)},function(){var e=function(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var n=dc();return"<style "+[n&&'nonce="'+n+'"',Ks+'="true"','data-styled-version="5.3.5"'].filter(Boolean).join(" ")+">"+t+"</style>"},this.getStyleTags=function(){return e.sealed?ec(2):e._emitSheetCSS()},this.getStyleElement=function(){var n;if(e.sealed)return ec(2);var r=((n={})[Ks]="",n["data-styled-version"]="5.3.5",n.dangerouslySetInnerHTML={__html:e.instance.toString()},n),o=dc();return o&&(r.nonce=o),[t().createElement("style",Ws({},r,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new yc({isServer:!0}),this.sealed=!1}.prototype;e.collectStyles=function(e){return this.sealed?ec(2):t().createElement(Nc,{sheet:this.instance},e)},e.interleaveWithNodeStream=function(e){return ec(3)}}();const al=il.div`
  .ant-avatar > img {
    filter: grayscale(${e=>e?.usePublicFetcher?100:0});
  }
`,sl=()=>{var t;const n=a(),{usePublicFetcher:r,toggleUsePublicFetcher:o}=n,i=r?"Switch to execute as the logged-in user":"Switch to execute as a public user";return(0,e.createElement)(al,{usePublicFetcher:r,className:"antd-app graphiql-auth-toggle","data-testid":"auth-switch"},(0,e.createElement)("span",{style:{margin:"0 5px"}},(0,e.createElement)(Ra,{getPopupContainer:()=>window.document.getElementsByClassName("graphiql-auth-toggle")[0],placement:"bottom",title:i},(0,e.createElement)("button",{"aria-label":i,type:"button",onClick:o,className:"toolbar-button"},(0,e.createElement)(Za,{dot:!r,status:"success"},(0,e.createElement)(vs,{shape:"circle",size:"small",title:i,src:null!==(t=window?.wpGraphiQLSettings?.avatarUrl)&&void 0!==t?t:null,icon:window?.wpGraphiQLSettings?.avatarUrl?null:(0,e.createElement)(zs,null)}))))))},{hooks:cl,useAppContext:ll}=window.wpGraphiQL;cl.addFilter("graphiql_fetcher","graphiql-auth-switch",((e,t)=>{const{usePublicFetcher:n}=a(),{endpoint:r}=ll();return n?(e=>t=>{const n={method:"POST",headers:{Accept:"application/json","content-type":"application/json"},body:JSON.stringify(t),credentials:"omit"};return fetch(e,n).then((e=>e.json()))})(r):e})),cl.addFilter("graphiql_app","graphiql-auth",(t=>(0,e.createElement)(s,null,t))),cl.addFilter("graphiql_toolbar_before_buttons","graphiql-auth-switch",(t=>(t.push((0,e.createElement)(sl,{key:"auth-switch"})),t)),1)})()})();