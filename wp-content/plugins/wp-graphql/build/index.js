(()=>{"use strict";var e={4291:(e,t,n)=>{n.d(t,{QG:()=>u,Us:()=>s});var r=n(1609),i=n(6087),o=n(3408);const a=(0,i.createContext)(),s=()=>(0,i.useContext)(a),u=({children:e,setQueryParams:t,queryParams:n})=>{const[s,u]=(0,i.useState)(null),[c,p]=(0,i.useState)(!0),[l,d]=(0,i.useState)(null!==(f=window?.wpGraphiQLSettings?.nonce)&&void 0!==f?f:null);var f;const[y,m]=(0,i.useState)(null!==(h=window?.wpGraphiQLSettings?.graphqlEndpoint)&&void 0!==h?h:null);var h;const[T,b]=(0,i.useState)(n);let v={endpoint:y,setEndpoint:m,nonce:l,setNonce:d,schema:s,setSchema:u,schemaLoading:c,setSchemaLoading:p,queryParams:T,setQueryParams:e=>{b(e),t(e)}},g=o.J.applyFilters("graphiql_app_context",v);return(0,r.createElement)(a.Provider,{value:g},e)}},3408:(e,t,n)=>{n.d(t,{J:()=>a});var r=n(3574);const i=window.wp.hooks;var o=n(4291);const a=(0,i.createHooks)();window.wpGraphiQL={GraphQL:r,hooks:a,useAppContext:o.Us,AppContextProvider:o.QG}},1702:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GraphQLError=void 0,t.formatError=function(e){return e.toJSON()},t.printError=function(e){return e.toString()};var r=n(5569),i=n(9530),o=n(825);class a extends Error{constructor(e,...t){var n,o,u;const{nodes:c,source:p,positions:l,path:d,originalError:f,extensions:y}=function(e){const t=e[0];return null==t||"kind"in t||"length"in t?{nodes:t,source:e[1],positions:e[2],path:e[3],originalError:e[4],extensions:e[5]}:t}(t);super(e),this.name="GraphQLError",this.path=null!=d?d:void 0,this.originalError=null!=f?f:void 0,this.nodes=s(Array.isArray(c)?c:c?[c]:void 0);const m=s(null===(n=this.nodes)||void 0===n?void 0:n.map((e=>e.loc)).filter((e=>null!=e)));this.source=null!=p?p:null==m||null===(o=m[0])||void 0===o?void 0:o.source,this.positions=null!=l?l:null==m?void 0:m.map((e=>e.start)),this.locations=l&&p?l.map((e=>(0,i.getLocation)(p,e))):null==m?void 0:m.map((e=>(0,i.getLocation)(e.source,e.start)));const h=(0,r.isObjectLike)(null==f?void 0:f.extensions)?null==f?void 0:f.extensions:void 0;this.extensions=null!==(u=null!=y?y:h)&&void 0!==u?u:Object.create(null),Object.defineProperties(this,{message:{writable:!0,enumerable:!0},name:{enumerable:!1},nodes:{enumerable:!1},source:{enumerable:!1},positions:{enumerable:!1},originalError:{enumerable:!1}}),null!=f&&f.stack?Object.defineProperty(this,"stack",{value:f.stack,writable:!0,configurable:!0}):Error.captureStackTrace?Error.captureStackTrace(this,a):Object.defineProperty(this,"stack",{value:Error().stack,writable:!0,configurable:!0})}get[Symbol.toStringTag](){return"GraphQLError"}toString(){let e=this.message;if(this.nodes)for(const t of this.nodes)t.loc&&(e+="\n\n"+(0,o.printLocation)(t.loc));else if(this.source&&this.locations)for(const t of this.locations)e+="\n\n"+(0,o.printSourceLocation)(this.source,t);return e}toJSON(){const e={message:this.message};return null!=this.locations&&(e.locations=this.locations),null!=this.path&&(e.path=this.path),null!=this.extensions&&Object.keys(this.extensions).length>0&&(e.extensions=this.extensions),e}}function s(e){return void 0===e||0===e.length?void 0:e}t.GraphQLError=a},9211:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"GraphQLError",{enumerable:!0,get:function(){return r.GraphQLError}}),Object.defineProperty(t,"formatError",{enumerable:!0,get:function(){return r.formatError}}),Object.defineProperty(t,"locatedError",{enumerable:!0,get:function(){return o.locatedError}}),Object.defineProperty(t,"printError",{enumerable:!0,get:function(){return r.printError}}),Object.defineProperty(t,"syntaxError",{enumerable:!0,get:function(){return i.syntaxError}});var r=n(1702),i=n(1352),o=n(6107)},6107:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.locatedError=function(e,t,n){var o;const a=(0,r.toError)(e);return s=a,Array.isArray(s.path)?a:new i.GraphQLError(a.message,{nodes:null!==(o=a.nodes)&&void 0!==o?o:t,source:a.source,positions:a.positions,path:n,originalError:a});var s};var r=n(2036),i=n(1702)},1352:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.syntaxError=function(e,t,n){return new r.GraphQLError(`Syntax Error: ${n}`,{source:e,positions:[t]})};var r=n(1702)},1516:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.collectFields=function(e,t,n,r,i){const o=new Map;return u(e,t,n,r,i,o,new Set),o},t.collectSubfields=function(e,t,n,r,i){const o=new Map,a=new Set;for(const s of i)s.selectionSet&&u(e,t,n,r,s.selectionSet,o,a);return o};var r=n(7030),i=n(3754),o=n(8685),a=n(6693),s=n(8113);function u(e,t,n,i,o,a,s){for(const d of o.selections)switch(d.kind){case r.Kind.FIELD:{if(!c(n,d))continue;const e=(l=d).alias?l.alias.value:l.name.value,t=a.get(e);void 0!==t?t.push(d):a.set(e,[d]);break}case r.Kind.INLINE_FRAGMENT:if(!c(n,d)||!p(e,d,i))continue;u(e,t,n,i,d.selectionSet,a,s);break;case r.Kind.FRAGMENT_SPREAD:{const r=d.name.value;if(s.has(r)||!c(n,d))continue;s.add(r);const o=t[r];if(!o||!p(e,o,i))continue;u(e,t,n,i,o.selectionSet,a,s);break}}var l}function c(e,t){const n=(0,s.getDirectiveValues)(o.GraphQLSkipDirective,t,e);if(!0===(null==n?void 0:n.if))return!1;const r=(0,s.getDirectiveValues)(o.GraphQLIncludeDirective,t,e);return!1!==(null==r?void 0:r.if)}function p(e,t,n){const r=t.typeCondition;if(!r)return!0;const o=(0,a.typeFromAST)(e,r);return o===n||!!(0,i.isAbstractType)(o)&&e.isSubType(o,n)}},6892:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.assertValidExecutionArguments=_,t.buildExecutionContext=L,t.buildResolveInfo=j,t.defaultTypeResolver=t.defaultFieldResolver=void 0,t.execute=O,t.executeSync=function(e){const t=O(e);if((0,u.isPromise)(t))throw new Error("GraphQL execution failed to complete synchronously.");return t},t.getFieldDef=G;var r=n(3028),i=n(9657),o=n(1321),a=n(4820),s=n(5569),u=n(7724),c=n(2104),p=n(6506),l=n(4702),d=n(5662),f=n(1702),y=n(6107),m=n(6257),h=n(7030),T=n(3754),b=n(8364),v=n(9873),g=n(1516),E=n(8113);const N=(0,c.memoize3)(((e,t,n)=>(0,g.collectSubfields)(e.schema,e.fragments,e.variableValues,t,n)));function O(e){arguments.length<2||(0,r.devAssert)(!1,"graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.");const{schema:t,document:n,variableValues:i,rootValue:o}=e;_(t,n,i);const a=L(e);if(!("schema"in a))return{errors:a};try{const{operation:e}=a,t=function(e,t,n){const r=e.schema.getRootType(t.operation);if(null==r)throw new f.GraphQLError(`Schema is not configured to execute ${t.operation} operation.`,{nodes:t});const i=(0,g.collectFields)(e.schema,e.fragments,e.variableValues,r,t.selectionSet),o=void 0;switch(t.operation){case m.OperationTypeNode.QUERY:return S(e,r,n,o,i);case m.OperationTypeNode.MUTATION:return function(e,t,n,r,i){return(0,d.promiseReduce)(i.entries(),((i,[o,a])=>{const s=(0,p.addPath)(r,o,t.name),c=D(e,t,n,a,s);return void 0===c?i:(0,u.isPromise)(c)?c.then((e=>(i[o]=e,i))):(i[o]=c,i)}),Object.create(null))}(e,r,n,o,i);case m.OperationTypeNode.SUBSCRIPTION:return S(e,r,n,o,i)}}(a,e,o);return(0,u.isPromise)(t)?t.then((e=>I(e,a.errors)),(e=>(a.errors.push(e),I(null,a.errors)))):I(t,a.errors)}catch(e){return a.errors.push(e),I(null,a.errors)}}function I(e,t){return 0===t.length?{data:e}:{errors:t,data:e}}function _(e,t,n){t||(0,r.devAssert)(!1,"Must provide document."),(0,v.assertValidSchema)(e),null==n||(0,s.isObjectLike)(n)||(0,r.devAssert)(!1,"Variables must be provided as an Object where each property is a variable value. Perhaps look to see if an unparsed JSON string was provided.")}function L(e){var t,n;const{schema:r,document:i,rootValue:o,contextValue:a,variableValues:s,operationName:u,fieldResolver:c,typeResolver:p,subscribeFieldResolver:l}=e;let d;const y=Object.create(null);for(const e of i.definitions)switch(e.kind){case h.Kind.OPERATION_DEFINITION:if(null==u){if(void 0!==d)return[new f.GraphQLError("Must provide operation name if query contains multiple operations.")];d=e}else(null===(t=e.name)||void 0===t?void 0:t.value)===u&&(d=e);break;case h.Kind.FRAGMENT_DEFINITION:y[e.name.value]=e}if(!d)return null!=u?[new f.GraphQLError(`Unknown operation named "${u}".`)]:[new f.GraphQLError("Must provide an operation.")];const m=null!==(n=d.variableDefinitions)&&void 0!==n?n:[],T=(0,E.getVariableValues)(r,m,null!=s?s:{},{maxErrors:50});return T.errors?T.errors:{schema:r,fragments:y,rootValue:o,contextValue:a,operation:d,variableValues:T.coerced,fieldResolver:null!=c?c:x,typeResolver:null!=p?p:F,subscribeFieldResolver:null!=l?l:x,errors:[]}}function S(e,t,n,r,i){const o=Object.create(null);let a=!1;try{for(const[s,c]of i.entries()){const i=D(e,t,n,c,(0,p.addPath)(r,s,t.name));void 0!==i&&(o[s]=i,(0,u.isPromise)(i)&&(a=!0))}}catch(e){if(a)return(0,l.promiseForObject)(o).finally((()=>{throw e}));throw e}return a?(0,l.promiseForObject)(o):o}function D(e,t,n,r,i){var o;const a=G(e.schema,t,r[0]);if(!a)return;const s=a.type,c=null!==(o=a.resolve)&&void 0!==o?o:e.fieldResolver,l=j(e,a,r,t,i);try{const t=c(n,(0,E.getArgumentValues)(a,r[0],e.variableValues),e.contextValue,l);let o;return o=(0,u.isPromise)(t)?t.then((t=>P(e,s,r,l,i,t))):P(e,s,r,l,i,t),(0,u.isPromise)(o)?o.then(void 0,(t=>A((0,y.locatedError)(t,r,(0,p.pathToArray)(i)),s,e))):o}catch(t){return A((0,y.locatedError)(t,r,(0,p.pathToArray)(i)),s,e)}}function j(e,t,n,r,i){return{fieldName:t.name,fieldNodes:n,returnType:t.type,parentType:r,path:i,schema:e.schema,fragments:e.fragments,rootValue:e.rootValue,operation:e.operation,variableValues:e.variableValues}}function A(e,t,n){if((0,T.isNonNullType)(t))throw e;return n.errors.push(e),null}function P(e,t,n,r,s,c){if(c instanceof Error)throw c;if((0,T.isNonNullType)(t)){const i=P(e,t.ofType,n,r,s,c);if(null===i)throw new Error(`Cannot return null for non-nullable field ${r.parentType.name}.${r.fieldName}.`);return i}return null==c?null:(0,T.isListType)(t)?function(e,t,n,r,i,o){if(!(0,a.isIterableObject)(o))throw new f.GraphQLError(`Expected Iterable, but did not find one for field "${r.parentType.name}.${r.fieldName}".`);const s=t.ofType;let c=!1;const l=Array.from(o,((t,o)=>{const a=(0,p.addPath)(i,o,void 0);try{let i;return i=(0,u.isPromise)(t)?t.then((t=>P(e,s,n,r,a,t))):P(e,s,n,r,a,t),(0,u.isPromise)(i)?(c=!0,i.then(void 0,(t=>A((0,y.locatedError)(t,n,(0,p.pathToArray)(a)),s,e)))):i}catch(t){return A((0,y.locatedError)(t,n,(0,p.pathToArray)(a)),s,e)}}));return c?Promise.all(l):l}(e,t,n,r,s,c):(0,T.isLeafType)(t)?function(e,t){const n=e.serialize(t);if(null==n)throw new Error(`Expected \`${(0,i.inspect)(e)}.serialize(${(0,i.inspect)(t)})\` to return non-nullable value, returned: ${(0,i.inspect)(n)}`);return n}(t,c):(0,T.isAbstractType)(t)?function(e,t,n,r,i,o){var a;const s=null!==(a=t.resolveType)&&void 0!==a?a:e.typeResolver,c=e.contextValue,p=s(o,c,r,t);return(0,u.isPromise)(p)?p.then((a=>w(e,R(a,e,t,n,r,o),n,r,i,o))):w(e,R(p,e,t,n,r,o),n,r,i,o)}(e,t,n,r,s,c):(0,T.isObjectType)(t)?w(e,t,n,r,s,c):void(0,o.invariant)(!1,"Cannot complete value of unexpected output type: "+(0,i.inspect)(t))}function R(e,t,n,r,o,a){if(null==e)throw new f.GraphQLError(`Abstract type "${n.name}" must resolve to an Object type at runtime for field "${o.parentType.name}.${o.fieldName}". Either the "${n.name}" type should provide a "resolveType" function or each possible type should provide an "isTypeOf" function.`,r);if((0,T.isObjectType)(e))throw new f.GraphQLError("Support for returning GraphQLObjectType from resolveType was removed in graphql-js@16.0.0 please return type name instead.");if("string"!=typeof e)throw new f.GraphQLError(`Abstract type "${n.name}" must resolve to an Object type at runtime for field "${o.parentType.name}.${o.fieldName}" with value ${(0,i.inspect)(a)}, received "${(0,i.inspect)(e)}".`);const s=t.schema.getType(e);if(null==s)throw new f.GraphQLError(`Abstract type "${n.name}" was resolved to a type "${e}" that does not exist inside the schema.`,{nodes:r});if(!(0,T.isObjectType)(s))throw new f.GraphQLError(`Abstract type "${n.name}" was resolved to a non-object type "${e}".`,{nodes:r});if(!t.schema.isSubType(n,s))throw new f.GraphQLError(`Runtime Object type "${s.name}" is not a possible type for "${n.name}".`,{nodes:r});return s}function w(e,t,n,r,i,o){const a=N(e,t,n);if(t.isTypeOf){const s=t.isTypeOf(o,e.contextValue,r);if((0,u.isPromise)(s))return s.then((r=>{if(!r)throw k(t,o,n);return S(e,t,o,i,a)}));if(!s)throw k(t,o,n)}return S(e,t,o,i,a)}function k(e,t,n){return new f.GraphQLError(`Expected value of type "${e.name}" but got: ${(0,i.inspect)(t)}.`,{nodes:n})}const F=function(e,t,n,r){if((0,s.isObjectLike)(e)&&"string"==typeof e.__typename)return e.__typename;const i=n.schema.getPossibleTypes(r),o=[];for(let r=0;r<i.length;r++){const a=i[r];if(a.isTypeOf){const i=a.isTypeOf(e,t,n);if((0,u.isPromise)(i))o[r]=i;else if(i)return a.name}}return o.length?Promise.all(o).then((e=>{for(let t=0;t<e.length;t++)if(e[t])return i[t].name})):void 0};t.defaultTypeResolver=F;const x=function(e,t,n,r){if((0,s.isObjectLike)(e)||"function"==typeof e){const i=e[r.fieldName];return"function"==typeof i?e[r.fieldName](t,n,r):i}};function G(e,t,n){const r=n.name.value;return r===b.SchemaMetaFieldDef.name&&e.getQueryType()===t?b.SchemaMetaFieldDef:r===b.TypeMetaFieldDef.name&&e.getQueryType()===t?b.TypeMetaFieldDef:r===b.TypeNameMetaFieldDef.name?b.TypeNameMetaFieldDef:t.getFields()[r]}t.defaultFieldResolver=x},8259:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createSourceEventStream",{enumerable:!0,get:function(){return o.createSourceEventStream}}),Object.defineProperty(t,"defaultFieldResolver",{enumerable:!0,get:function(){return i.defaultFieldResolver}}),Object.defineProperty(t,"defaultTypeResolver",{enumerable:!0,get:function(){return i.defaultTypeResolver}}),Object.defineProperty(t,"execute",{enumerable:!0,get:function(){return i.execute}}),Object.defineProperty(t,"executeSync",{enumerable:!0,get:function(){return i.executeSync}}),Object.defineProperty(t,"getArgumentValues",{enumerable:!0,get:function(){return a.getArgumentValues}}),Object.defineProperty(t,"getDirectiveValues",{enumerable:!0,get:function(){return a.getDirectiveValues}}),Object.defineProperty(t,"getVariableValues",{enumerable:!0,get:function(){return a.getVariableValues}}),Object.defineProperty(t,"responsePathAsArray",{enumerable:!0,get:function(){return r.pathToArray}}),Object.defineProperty(t,"subscribe",{enumerable:!0,get:function(){return o.subscribe}});var r=n(6506),i=n(6892),o=n(9567),a=n(8113)},1215:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.mapAsyncIterator=function(e,t){const n=e[Symbol.asyncIterator]();async function r(e){if(e.done)return e;try{return{value:await t(e.value),done:!1}}catch(e){if("function"==typeof n.return)try{await n.return()}catch(e){}throw e}}return{next:async()=>r(await n.next()),return:async()=>"function"==typeof n.return?r(await n.return()):{value:void 0,done:!0},async throw(e){if("function"==typeof n.throw)return r(await n.throw(e));throw e},[Symbol.asyncIterator](){return this}}}},9567:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createSourceEventStream=f,t.subscribe=async function(e){arguments.length<2||(0,r.devAssert)(!1,"graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.");const t=await f(e);return(0,o.isAsyncIterable)(t)?(0,l.mapAsyncIterator)(t,(t=>(0,p.execute)({...e,rootValue:t}))):t};var r=n(3028),i=n(9657),o=n(1619),a=n(6506),s=n(1702),u=n(6107),c=n(1516),p=n(6892),l=n(1215),d=n(8113);async function f(...e){const t=function(e){const t=e[0];return t&&"document"in t?t:{schema:t,document:e[1],rootValue:e[2],contextValue:e[3],variableValues:e[4],operationName:e[5],subscribeFieldResolver:e[6]}}(e),{schema:n,document:r,variableValues:l}=t;(0,p.assertValidExecutionArguments)(n,r,l);const f=(0,p.buildExecutionContext)(t);if(!("schema"in f))return{errors:f};try{const e=await async function(e){const{schema:t,fragments:n,operation:r,variableValues:i,rootValue:o}=e,l=t.getSubscriptionType();if(null==l)throw new s.GraphQLError("Schema is not configured to execute subscription operation.",{nodes:r});const f=(0,c.collectFields)(t,n,i,l,r.selectionSet),[y,m]=[...f.entries()][0],h=(0,p.getFieldDef)(t,l,m[0]);if(!h){const e=m[0].name.value;throw new s.GraphQLError(`The subscription field "${e}" is not defined.`,{nodes:m})}const T=(0,a.addPath)(void 0,y,l.name),b=(0,p.buildResolveInfo)(e,h,m,l,T);try{var v;const t=(0,d.getArgumentValues)(h,m[0],i),n=e.contextValue,r=null!==(v=h.subscribe)&&void 0!==v?v:e.subscribeFieldResolver,a=await r(o,t,n,b);if(a instanceof Error)throw a;return a}catch(e){throw(0,u.locatedError)(e,m,(0,a.pathToArray)(T))}}(f);if(!(0,o.isAsyncIterable)(e))throw new Error(`Subscription field must return Async Iterable. Received: ${(0,i.inspect)(e)}.`);return e}catch(e){if(e instanceof s.GraphQLError)return{errors:[e]};throw e}}},8113:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getArgumentValues=f,t.getDirectiveValues=function(e,t,n){var r;const i=null===(r=t.directives)||void 0===r?void 0:r.find((t=>t.name.value===e.name));if(i)return f(e,i,n)},t.getVariableValues=function(e,t,n,i){const s=[],f=null==i?void 0:i.maxErrors;try{const i=function(e,t,n,i){const s={};for(const f of t){const t=f.variable.name.value,m=(0,l.typeFromAST)(e,f.type);if(!(0,c.isInputType)(m)){const e=(0,u.print)(f.type);i(new a.GraphQLError(`Variable "$${t}" expected value of type "${e}" which cannot be used as an input type.`,{nodes:f.type}));continue}if(!y(n,t)){if(f.defaultValue)s[t]=(0,d.valueFromAST)(f.defaultValue,m);else if((0,c.isNonNullType)(m)){const e=(0,r.inspect)(m);i(new a.GraphQLError(`Variable "$${t}" of required type "${e}" was not provided.`,{nodes:f}))}continue}const h=n[t];if(null===h&&(0,c.isNonNullType)(m)){const e=(0,r.inspect)(m);i(new a.GraphQLError(`Variable "$${t}" of non-null type "${e}" must not be null.`,{nodes:f}))}else s[t]=(0,p.coerceInputValue)(h,m,((e,n,s)=>{let u=`Variable "$${t}" got invalid value `+(0,r.inspect)(n);e.length>0&&(u+=` at "${t}${(0,o.printPathArray)(e)}"`),i(new a.GraphQLError(u+"; "+s.message,{nodes:f,originalError:s}))}))}return s}(e,t,n,(e=>{if(null!=f&&s.length>=f)throw new a.GraphQLError("Too many errors processing variables, error limit reached. Execution aborted.");s.push(e)}));if(0===s.length)return{coerced:i}}catch(e){s.push(e)}return{errors:s}};var r=n(9657),i=n(4590),o=n(636),a=n(1702),s=n(7030),u=n(585),c=n(3754),p=n(4090),l=n(6693),d=n(2302);function f(e,t,n){var o;const p={},l=null!==(o=t.arguments)&&void 0!==o?o:[],f=(0,i.keyMap)(l,(e=>e.name.value));for(const i of e.args){const e=i.name,o=i.type,l=f[e];if(!l){if(void 0!==i.defaultValue)p[e]=i.defaultValue;else if((0,c.isNonNullType)(o))throw new a.GraphQLError(`Argument "${e}" of required type "${(0,r.inspect)(o)}" was not provided.`,{nodes:t});continue}const m=l.value;let h=m.kind===s.Kind.NULL;if(m.kind===s.Kind.VARIABLE){const t=m.name.value;if(null==n||!y(n,t)){if(void 0!==i.defaultValue)p[e]=i.defaultValue;else if((0,c.isNonNullType)(o))throw new a.GraphQLError(`Argument "${e}" of required type "${(0,r.inspect)(o)}" was provided the variable "$${t}" which was not provided a runtime value.`,{nodes:m});continue}h=null==n[t]}if(h&&(0,c.isNonNullType)(o))throw new a.GraphQLError(`Argument "${e}" of non-null type "${(0,r.inspect)(o)}" must not be null.`,{nodes:m});const T=(0,d.valueFromAST)(m,o,n);if(void 0===T)throw new a.GraphQLError(`Argument "${e}" has invalid value ${(0,u.print)(m)}.`,{nodes:m});p[e]=T}return p}function y(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},9151:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.graphql=function(e){return new Promise((t=>t(c(e))))},t.graphqlSync=function(e){const t=c(e);if((0,i.isPromise)(t))throw new Error("GraphQL execution failed to complete synchronously.");return t};var r=n(3028),i=n(7724),o=n(246),a=n(9873),s=n(9040),u=n(6892);function c(e){arguments.length<2||(0,r.devAssert)(!1,"graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.");const{schema:t,source:n,rootValue:i,contextValue:c,variableValues:p,operationName:l,fieldResolver:d,typeResolver:f}=e,y=(0,a.validateSchema)(t);if(y.length>0)return{errors:y};let m;try{m=(0,o.parse)(n)}catch(e){return{errors:[e]}}const h=(0,s.validate)(t,m);return h.length>0?{errors:h}:(0,u.execute)({schema:t,document:m,rootValue:i,contextValue:c,variableValues:p,operationName:l,fieldResolver:d,typeResolver:f})}},3574:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BREAK",{enumerable:!0,get:function(){return a.BREAK}}),Object.defineProperty(t,"BreakingChangeType",{enumerable:!0,get:function(){return p.BreakingChangeType}}),Object.defineProperty(t,"DEFAULT_DEPRECATION_REASON",{enumerable:!0,get:function(){return o.DEFAULT_DEPRECATION_REASON}}),Object.defineProperty(t,"DangerousChangeType",{enumerable:!0,get:function(){return p.DangerousChangeType}}),Object.defineProperty(t,"DirectiveLocation",{enumerable:!0,get:function(){return a.DirectiveLocation}}),Object.defineProperty(t,"ExecutableDefinitionsRule",{enumerable:!0,get:function(){return u.ExecutableDefinitionsRule}}),Object.defineProperty(t,"FieldsOnCorrectTypeRule",{enumerable:!0,get:function(){return u.FieldsOnCorrectTypeRule}}),Object.defineProperty(t,"FragmentsOnCompositeTypesRule",{enumerable:!0,get:function(){return u.FragmentsOnCompositeTypesRule}}),Object.defineProperty(t,"GRAPHQL_MAX_INT",{enumerable:!0,get:function(){return o.GRAPHQL_MAX_INT}}),Object.defineProperty(t,"GRAPHQL_MIN_INT",{enumerable:!0,get:function(){return o.GRAPHQL_MIN_INT}}),Object.defineProperty(t,"GraphQLBoolean",{enumerable:!0,get:function(){return o.GraphQLBoolean}}),Object.defineProperty(t,"GraphQLDeprecatedDirective",{enumerable:!0,get:function(){return o.GraphQLDeprecatedDirective}}),Object.defineProperty(t,"GraphQLDirective",{enumerable:!0,get:function(){return o.GraphQLDirective}}),Object.defineProperty(t,"GraphQLEnumType",{enumerable:!0,get:function(){return o.GraphQLEnumType}}),Object.defineProperty(t,"GraphQLError",{enumerable:!0,get:function(){return c.GraphQLError}}),Object.defineProperty(t,"GraphQLFloat",{enumerable:!0,get:function(){return o.GraphQLFloat}}),Object.defineProperty(t,"GraphQLID",{enumerable:!0,get:function(){return o.GraphQLID}}),Object.defineProperty(t,"GraphQLIncludeDirective",{enumerable:!0,get:function(){return o.GraphQLIncludeDirective}}),Object.defineProperty(t,"GraphQLInputObjectType",{enumerable:!0,get:function(){return o.GraphQLInputObjectType}}),Object.defineProperty(t,"GraphQLInt",{enumerable:!0,get:function(){return o.GraphQLInt}}),Object.defineProperty(t,"GraphQLInterfaceType",{enumerable:!0,get:function(){return o.GraphQLInterfaceType}}),Object.defineProperty(t,"GraphQLList",{enumerable:!0,get:function(){return o.GraphQLList}}),Object.defineProperty(t,"GraphQLNonNull",{enumerable:!0,get:function(){return o.GraphQLNonNull}}),Object.defineProperty(t,"GraphQLObjectType",{enumerable:!0,get:function(){return o.GraphQLObjectType}}),Object.defineProperty(t,"GraphQLScalarType",{enumerable:!0,get:function(){return o.GraphQLScalarType}}),Object.defineProperty(t,"GraphQLSchema",{enumerable:!0,get:function(){return o.GraphQLSchema}}),Object.defineProperty(t,"GraphQLSkipDirective",{enumerable:!0,get:function(){return o.GraphQLSkipDirective}}),Object.defineProperty(t,"GraphQLSpecifiedByDirective",{enumerable:!0,get:function(){return o.GraphQLSpecifiedByDirective}}),Object.defineProperty(t,"GraphQLString",{enumerable:!0,get:function(){return o.GraphQLString}}),Object.defineProperty(t,"GraphQLUnionType",{enumerable:!0,get:function(){return o.GraphQLUnionType}}),Object.defineProperty(t,"Kind",{enumerable:!0,get:function(){return a.Kind}}),Object.defineProperty(t,"KnownArgumentNamesRule",{enumerable:!0,get:function(){return u.KnownArgumentNamesRule}}),Object.defineProperty(t,"KnownDirectivesRule",{enumerable:!0,get:function(){return u.KnownDirectivesRule}}),Object.defineProperty(t,"KnownFragmentNamesRule",{enumerable:!0,get:function(){return u.KnownFragmentNamesRule}}),Object.defineProperty(t,"KnownTypeNamesRule",{enumerable:!0,get:function(){return u.KnownTypeNamesRule}}),Object.defineProperty(t,"Lexer",{enumerable:!0,get:function(){return a.Lexer}}),Object.defineProperty(t,"Location",{enumerable:!0,get:function(){return a.Location}}),Object.defineProperty(t,"LoneAnonymousOperationRule",{enumerable:!0,get:function(){return u.LoneAnonymousOperationRule}}),Object.defineProperty(t,"LoneSchemaDefinitionRule",{enumerable:!0,get:function(){return u.LoneSchemaDefinitionRule}}),Object.defineProperty(t,"NoDeprecatedCustomRule",{enumerable:!0,get:function(){return u.NoDeprecatedCustomRule}}),Object.defineProperty(t,"NoFragmentCyclesRule",{enumerable:!0,get:function(){return u.NoFragmentCyclesRule}}),Object.defineProperty(t,"NoSchemaIntrospectionCustomRule",{enumerable:!0,get:function(){return u.NoSchemaIntrospectionCustomRule}}),Object.defineProperty(t,"NoUndefinedVariablesRule",{enumerable:!0,get:function(){return u.NoUndefinedVariablesRule}}),Object.defineProperty(t,"NoUnusedFragmentsRule",{enumerable:!0,get:function(){return u.NoUnusedFragmentsRule}}),Object.defineProperty(t,"NoUnusedVariablesRule",{enumerable:!0,get:function(){return u.NoUnusedVariablesRule}}),Object.defineProperty(t,"OperationTypeNode",{enumerable:!0,get:function(){return a.OperationTypeNode}}),Object.defineProperty(t,"OverlappingFieldsCanBeMergedRule",{enumerable:!0,get:function(){return u.OverlappingFieldsCanBeMergedRule}}),Object.defineProperty(t,"PossibleFragmentSpreadsRule",{enumerable:!0,get:function(){return u.PossibleFragmentSpreadsRule}}),Object.defineProperty(t,"PossibleTypeExtensionsRule",{enumerable:!0,get:function(){return u.PossibleTypeExtensionsRule}}),Object.defineProperty(t,"ProvidedRequiredArgumentsRule",{enumerable:!0,get:function(){return u.ProvidedRequiredArgumentsRule}}),Object.defineProperty(t,"ScalarLeafsRule",{enumerable:!0,get:function(){return u.ScalarLeafsRule}}),Object.defineProperty(t,"SchemaMetaFieldDef",{enumerable:!0,get:function(){return o.SchemaMetaFieldDef}}),Object.defineProperty(t,"SingleFieldSubscriptionsRule",{enumerable:!0,get:function(){return u.SingleFieldSubscriptionsRule}}),Object.defineProperty(t,"Source",{enumerable:!0,get:function(){return a.Source}}),Object.defineProperty(t,"Token",{enumerable:!0,get:function(){return a.Token}}),Object.defineProperty(t,"TokenKind",{enumerable:!0,get:function(){return a.TokenKind}}),Object.defineProperty(t,"TypeInfo",{enumerable:!0,get:function(){return p.TypeInfo}}),Object.defineProperty(t,"TypeKind",{enumerable:!0,get:function(){return o.TypeKind}}),Object.defineProperty(t,"TypeMetaFieldDef",{enumerable:!0,get:function(){return o.TypeMetaFieldDef}}),Object.defineProperty(t,"TypeNameMetaFieldDef",{enumerable:!0,get:function(){return o.TypeNameMetaFieldDef}}),Object.defineProperty(t,"UniqueArgumentDefinitionNamesRule",{enumerable:!0,get:function(){return u.UniqueArgumentDefinitionNamesRule}}),Object.defineProperty(t,"UniqueArgumentNamesRule",{enumerable:!0,get:function(){return u.UniqueArgumentNamesRule}}),Object.defineProperty(t,"UniqueDirectiveNamesRule",{enumerable:!0,get:function(){return u.UniqueDirectiveNamesRule}}),Object.defineProperty(t,"UniqueDirectivesPerLocationRule",{enumerable:!0,get:function(){return u.UniqueDirectivesPerLocationRule}}),Object.defineProperty(t,"UniqueEnumValueNamesRule",{enumerable:!0,get:function(){return u.UniqueEnumValueNamesRule}}),Object.defineProperty(t,"UniqueFieldDefinitionNamesRule",{enumerable:!0,get:function(){return u.UniqueFieldDefinitionNamesRule}}),Object.defineProperty(t,"UniqueFragmentNamesRule",{enumerable:!0,get:function(){return u.UniqueFragmentNamesRule}}),Object.defineProperty(t,"UniqueInputFieldNamesRule",{enumerable:!0,get:function(){return u.UniqueInputFieldNamesRule}}),Object.defineProperty(t,"UniqueOperationNamesRule",{enumerable:!0,get:function(){return u.UniqueOperationNamesRule}}),Object.defineProperty(t,"UniqueOperationTypesRule",{enumerable:!0,get:function(){return u.UniqueOperationTypesRule}}),Object.defineProperty(t,"UniqueTypeNamesRule",{enumerable:!0,get:function(){return u.UniqueTypeNamesRule}}),Object.defineProperty(t,"UniqueVariableNamesRule",{enumerable:!0,get:function(){return u.UniqueVariableNamesRule}}),Object.defineProperty(t,"ValidationContext",{enumerable:!0,get:function(){return u.ValidationContext}}),Object.defineProperty(t,"ValuesOfCorrectTypeRule",{enumerable:!0,get:function(){return u.ValuesOfCorrectTypeRule}}),Object.defineProperty(t,"VariablesAreInputTypesRule",{enumerable:!0,get:function(){return u.VariablesAreInputTypesRule}}),Object.defineProperty(t,"VariablesInAllowedPositionRule",{enumerable:!0,get:function(){return u.VariablesInAllowedPositionRule}}),Object.defineProperty(t,"__Directive",{enumerable:!0,get:function(){return o.__Directive}}),Object.defineProperty(t,"__DirectiveLocation",{enumerable:!0,get:function(){return o.__DirectiveLocation}}),Object.defineProperty(t,"__EnumValue",{enumerable:!0,get:function(){return o.__EnumValue}}),Object.defineProperty(t,"__Field",{enumerable:!0,get:function(){return o.__Field}}),Object.defineProperty(t,"__InputValue",{enumerable:!0,get:function(){return o.__InputValue}}),Object.defineProperty(t,"__Schema",{enumerable:!0,get:function(){return o.__Schema}}),Object.defineProperty(t,"__Type",{enumerable:!0,get:function(){return o.__Type}}),Object.defineProperty(t,"__TypeKind",{enumerable:!0,get:function(){return o.__TypeKind}}),Object.defineProperty(t,"assertAbstractType",{enumerable:!0,get:function(){return o.assertAbstractType}}),Object.defineProperty(t,"assertCompositeType",{enumerable:!0,get:function(){return o.assertCompositeType}}),Object.defineProperty(t,"assertDirective",{enumerable:!0,get:function(){return o.assertDirective}}),Object.defineProperty(t,"assertEnumType",{enumerable:!0,get:function(){return o.assertEnumType}}),Object.defineProperty(t,"assertEnumValueName",{enumerable:!0,get:function(){return o.assertEnumValueName}}),Object.defineProperty(t,"assertInputObjectType",{enumerable:!0,get:function(){return o.assertInputObjectType}}),Object.defineProperty(t,"assertInputType",{enumerable:!0,get:function(){return o.assertInputType}}),Object.defineProperty(t,"assertInterfaceType",{enumerable:!0,get:function(){return o.assertInterfaceType}}),Object.defineProperty(t,"assertLeafType",{enumerable:!0,get:function(){return o.assertLeafType}}),Object.defineProperty(t,"assertListType",{enumerable:!0,get:function(){return o.assertListType}}),Object.defineProperty(t,"assertName",{enumerable:!0,get:function(){return o.assertName}}),Object.defineProperty(t,"assertNamedType",{enumerable:!0,get:function(){return o.assertNamedType}}),Object.defineProperty(t,"assertNonNullType",{enumerable:!0,get:function(){return o.assertNonNullType}}),Object.defineProperty(t,"assertNullableType",{enumerable:!0,get:function(){return o.assertNullableType}}),Object.defineProperty(t,"assertObjectType",{enumerable:!0,get:function(){return o.assertObjectType}}),Object.defineProperty(t,"assertOutputType",{enumerable:!0,get:function(){return o.assertOutputType}}),Object.defineProperty(t,"assertScalarType",{enumerable:!0,get:function(){return o.assertScalarType}}),Object.defineProperty(t,"assertSchema",{enumerable:!0,get:function(){return o.assertSchema}}),Object.defineProperty(t,"assertType",{enumerable:!0,get:function(){return o.assertType}}),Object.defineProperty(t,"assertUnionType",{enumerable:!0,get:function(){return o.assertUnionType}}),Object.defineProperty(t,"assertValidName",{enumerable:!0,get:function(){return p.assertValidName}}),Object.defineProperty(t,"assertValidSchema",{enumerable:!0,get:function(){return o.assertValidSchema}}),Object.defineProperty(t,"assertWrappingType",{enumerable:!0,get:function(){return o.assertWrappingType}}),Object.defineProperty(t,"astFromValue",{enumerable:!0,get:function(){return p.astFromValue}}),Object.defineProperty(t,"buildASTSchema",{enumerable:!0,get:function(){return p.buildASTSchema}}),Object.defineProperty(t,"buildClientSchema",{enumerable:!0,get:function(){return p.buildClientSchema}}),Object.defineProperty(t,"buildSchema",{enumerable:!0,get:function(){return p.buildSchema}}),Object.defineProperty(t,"coerceInputValue",{enumerable:!0,get:function(){return p.coerceInputValue}}),Object.defineProperty(t,"concatAST",{enumerable:!0,get:function(){return p.concatAST}}),Object.defineProperty(t,"createSourceEventStream",{enumerable:!0,get:function(){return s.createSourceEventStream}}),Object.defineProperty(t,"defaultFieldResolver",{enumerable:!0,get:function(){return s.defaultFieldResolver}}),Object.defineProperty(t,"defaultTypeResolver",{enumerable:!0,get:function(){return s.defaultTypeResolver}}),Object.defineProperty(t,"doTypesOverlap",{enumerable:!0,get:function(){return p.doTypesOverlap}}),Object.defineProperty(t,"execute",{enumerable:!0,get:function(){return s.execute}}),Object.defineProperty(t,"executeSync",{enumerable:!0,get:function(){return s.executeSync}}),Object.defineProperty(t,"extendSchema",{enumerable:!0,get:function(){return p.extendSchema}}),Object.defineProperty(t,"findBreakingChanges",{enumerable:!0,get:function(){return p.findBreakingChanges}}),Object.defineProperty(t,"findDangerousChanges",{enumerable:!0,get:function(){return p.findDangerousChanges}}),Object.defineProperty(t,"formatError",{enumerable:!0,get:function(){return c.formatError}}),Object.defineProperty(t,"getArgumentValues",{enumerable:!0,get:function(){return s.getArgumentValues}}),Object.defineProperty(t,"getDirectiveValues",{enumerable:!0,get:function(){return s.getDirectiveValues}}),Object.defineProperty(t,"getEnterLeaveForKind",{enumerable:!0,get:function(){return a.getEnterLeaveForKind}}),Object.defineProperty(t,"getIntrospectionQuery",{enumerable:!0,get:function(){return p.getIntrospectionQuery}}),Object.defineProperty(t,"getLocation",{enumerable:!0,get:function(){return a.getLocation}}),Object.defineProperty(t,"getNamedType",{enumerable:!0,get:function(){return o.getNamedType}}),Object.defineProperty(t,"getNullableType",{enumerable:!0,get:function(){return o.getNullableType}}),Object.defineProperty(t,"getOperationAST",{enumerable:!0,get:function(){return p.getOperationAST}}),Object.defineProperty(t,"getOperationRootType",{enumerable:!0,get:function(){return p.getOperationRootType}}),Object.defineProperty(t,"getVariableValues",{enumerable:!0,get:function(){return s.getVariableValues}}),Object.defineProperty(t,"getVisitFn",{enumerable:!0,get:function(){return a.getVisitFn}}),Object.defineProperty(t,"graphql",{enumerable:!0,get:function(){return i.graphql}}),Object.defineProperty(t,"graphqlSync",{enumerable:!0,get:function(){return i.graphqlSync}}),Object.defineProperty(t,"introspectionFromSchema",{enumerable:!0,get:function(){return p.introspectionFromSchema}}),Object.defineProperty(t,"introspectionTypes",{enumerable:!0,get:function(){return o.introspectionTypes}}),Object.defineProperty(t,"isAbstractType",{enumerable:!0,get:function(){return o.isAbstractType}}),Object.defineProperty(t,"isCompositeType",{enumerable:!0,get:function(){return o.isCompositeType}}),Object.defineProperty(t,"isConstValueNode",{enumerable:!0,get:function(){return a.isConstValueNode}}),Object.defineProperty(t,"isDefinitionNode",{enumerable:!0,get:function(){return a.isDefinitionNode}}),Object.defineProperty(t,"isDirective",{enumerable:!0,get:function(){return o.isDirective}}),Object.defineProperty(t,"isEnumType",{enumerable:!0,get:function(){return o.isEnumType}}),Object.defineProperty(t,"isEqualType",{enumerable:!0,get:function(){return p.isEqualType}}),Object.defineProperty(t,"isExecutableDefinitionNode",{enumerable:!0,get:function(){return a.isExecutableDefinitionNode}}),Object.defineProperty(t,"isInputObjectType",{enumerable:!0,get:function(){return o.isInputObjectType}}),Object.defineProperty(t,"isInputType",{enumerable:!0,get:function(){return o.isInputType}}),Object.defineProperty(t,"isInterfaceType",{enumerable:!0,get:function(){return o.isInterfaceType}}),Object.defineProperty(t,"isIntrospectionType",{enumerable:!0,get:function(){return o.isIntrospectionType}}),Object.defineProperty(t,"isLeafType",{enumerable:!0,get:function(){return o.isLeafType}}),Object.defineProperty(t,"isListType",{enumerable:!0,get:function(){return o.isListType}}),Object.defineProperty(t,"isNamedType",{enumerable:!0,get:function(){return o.isNamedType}}),Object.defineProperty(t,"isNonNullType",{enumerable:!0,get:function(){return o.isNonNullType}}),Object.defineProperty(t,"isNullableType",{enumerable:!0,get:function(){return o.isNullableType}}),Object.defineProperty(t,"isObjectType",{enumerable:!0,get:function(){return o.isObjectType}}),Object.defineProperty(t,"isOutputType",{enumerable:!0,get:function(){return o.isOutputType}}),Object.defineProperty(t,"isRequiredArgument",{enumerable:!0,get:function(){return o.isRequiredArgument}}),Object.defineProperty(t,"isRequiredInputField",{enumerable:!0,get:function(){return o.isRequiredInputField}}),Object.defineProperty(t,"isScalarType",{enumerable:!0,get:function(){return o.isScalarType}}),Object.defineProperty(t,"isSchema",{enumerable:!0,get:function(){return o.isSchema}}),Object.defineProperty(t,"isSelectionNode",{enumerable:!0,get:function(){return a.isSelectionNode}}),Object.defineProperty(t,"isSpecifiedDirective",{enumerable:!0,get:function(){return o.isSpecifiedDirective}}),Object.defineProperty(t,"isSpecifiedScalarType",{enumerable:!0,get:function(){return o.isSpecifiedScalarType}}),Object.defineProperty(t,"isType",{enumerable:!0,get:function(){return o.isType}}),Object.defineProperty(t,"isTypeDefinitionNode",{enumerable:!0,get:function(){return a.isTypeDefinitionNode}}),Object.defineProperty(t,"isTypeExtensionNode",{enumerable:!0,get:function(){return a.isTypeExtensionNode}}),Object.defineProperty(t,"isTypeNode",{enumerable:!0,get:function(){return a.isTypeNode}}),Object.defineProperty(t,"isTypeSubTypeOf",{enumerable:!0,get:function(){return p.isTypeSubTypeOf}}),Object.defineProperty(t,"isTypeSystemDefinitionNode",{enumerable:!0,get:function(){return a.isTypeSystemDefinitionNode}}),Object.defineProperty(t,"isTypeSystemExtensionNode",{enumerable:!0,get:function(){return a.isTypeSystemExtensionNode}}),Object.defineProperty(t,"isUnionType",{enumerable:!0,get:function(){return o.isUnionType}}),Object.defineProperty(t,"isValidNameError",{enumerable:!0,get:function(){return p.isValidNameError}}),Object.defineProperty(t,"isValueNode",{enumerable:!0,get:function(){return a.isValueNode}}),Object.defineProperty(t,"isWrappingType",{enumerable:!0,get:function(){return o.isWrappingType}}),Object.defineProperty(t,"lexicographicSortSchema",{enumerable:!0,get:function(){return p.lexicographicSortSchema}}),Object.defineProperty(t,"locatedError",{enumerable:!0,get:function(){return c.locatedError}}),Object.defineProperty(t,"parse",{enumerable:!0,get:function(){return a.parse}}),Object.defineProperty(t,"parseConstValue",{enumerable:!0,get:function(){return a.parseConstValue}}),Object.defineProperty(t,"parseType",{enumerable:!0,get:function(){return a.parseType}}),Object.defineProperty(t,"parseValue",{enumerable:!0,get:function(){return a.parseValue}}),Object.defineProperty(t,"print",{enumerable:!0,get:function(){return a.print}}),Object.defineProperty(t,"printError",{enumerable:!0,get:function(){return c.printError}}),Object.defineProperty(t,"printIntrospectionSchema",{enumerable:!0,get:function(){return p.printIntrospectionSchema}}),Object.defineProperty(t,"printLocation",{enumerable:!0,get:function(){return a.printLocation}}),Object.defineProperty(t,"printSchema",{enumerable:!0,get:function(){return p.printSchema}}),Object.defineProperty(t,"printSourceLocation",{enumerable:!0,get:function(){return a.printSourceLocation}}),Object.defineProperty(t,"printType",{enumerable:!0,get:function(){return p.printType}}),Object.defineProperty(t,"resolveObjMapThunk",{enumerable:!0,get:function(){return o.resolveObjMapThunk}}),Object.defineProperty(t,"resolveReadonlyArrayThunk",{enumerable:!0,get:function(){return o.resolveReadonlyArrayThunk}}),Object.defineProperty(t,"responsePathAsArray",{enumerable:!0,get:function(){return s.responsePathAsArray}}),Object.defineProperty(t,"separateOperations",{enumerable:!0,get:function(){return p.separateOperations}}),Object.defineProperty(t,"specifiedDirectives",{enumerable:!0,get:function(){return o.specifiedDirectives}}),Object.defineProperty(t,"specifiedRules",{enumerable:!0,get:function(){return u.specifiedRules}}),Object.defineProperty(t,"specifiedScalarTypes",{enumerable:!0,get:function(){return o.specifiedScalarTypes}}),Object.defineProperty(t,"stripIgnoredCharacters",{enumerable:!0,get:function(){return p.stripIgnoredCharacters}}),Object.defineProperty(t,"subscribe",{enumerable:!0,get:function(){return s.subscribe}}),Object.defineProperty(t,"syntaxError",{enumerable:!0,get:function(){return c.syntaxError}}),Object.defineProperty(t,"typeFromAST",{enumerable:!0,get:function(){return p.typeFromAST}}),Object.defineProperty(t,"validate",{enumerable:!0,get:function(){return u.validate}}),Object.defineProperty(t,"validateSchema",{enumerable:!0,get:function(){return o.validateSchema}}),Object.defineProperty(t,"valueFromAST",{enumerable:!0,get:function(){return p.valueFromAST}}),Object.defineProperty(t,"valueFromASTUntyped",{enumerable:!0,get:function(){return p.valueFromASTUntyped}}),Object.defineProperty(t,"version",{enumerable:!0,get:function(){return r.version}}),Object.defineProperty(t,"versionInfo",{enumerable:!0,get:function(){return r.versionInfo}}),Object.defineProperty(t,"visit",{enumerable:!0,get:function(){return a.visit}}),Object.defineProperty(t,"visitInParallel",{enumerable:!0,get:function(){return a.visitInParallel}}),Object.defineProperty(t,"visitWithTypeInfo",{enumerable:!0,get:function(){return p.visitWithTypeInfo}});var r=n(4274),i=n(9151),o=n(219),a=n(425),s=n(8259),u=n(4360),c=n(9211),p=n(4889)},6506:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.addPath=function(e,t,n){return{prev:e,key:t,typename:n}},t.pathToArray=function(e){const t=[];let n=e;for(;n;)t.push(n.key),n=n.prev;return t.reverse()}},3028:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.devAssert=function(e,t){if(!Boolean(e))throw new Error(t)}},2832:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.didYouMean=function(e,t){const[r,i]=t?[e,t]:[void 0,e];let o=" Did you mean ";r&&(o+=r+" ");const a=i.map((e=>`"${e}"`));switch(a.length){case 0:return"";case 1:return o+a[0]+"?";case 2:return o+a[0]+" or "+a[1]+"?"}const s=a.slice(0,n),u=s.pop();return o+s.join(", ")+", or "+u+"?"};const n=5},4947:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.groupBy=function(e,t){const n=new Map;for(const r of e){const e=t(r),i=n.get(e);void 0===i?n.set(e,[r]):i.push(r)}return n}},6033:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.identityFunc=function(e){return e}},9657:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.inspect=function(e){return i(e,[])};const n=10,r=2;function i(e,t){switch(typeof e){case"string":return JSON.stringify(e);case"function":return e.name?`[function ${e.name}]`:"[function]";case"object":return function(e,t){if(null===e)return"null";if(t.includes(e))return"[Circular]";const o=[...t,e];if(function(e){return"function"==typeof e.toJSON}(e)){const t=e.toJSON();if(t!==e)return"string"==typeof t?t:i(t,o)}else if(Array.isArray(e))return function(e,t){if(0===e.length)return"[]";if(t.length>r)return"[Array]";const o=Math.min(n,e.length),a=e.length-o,s=[];for(let n=0;n<o;++n)s.push(i(e[n],t));return 1===a?s.push("... 1 more item"):a>1&&s.push(`... ${a} more items`),"["+s.join(", ")+"]"}(e,o);return function(e,t){const n=Object.entries(e);if(0===n.length)return"{}";if(t.length>r)return"["+function(e){const t=Object.prototype.toString.call(e).replace(/^\[object /,"").replace(/]$/,"");if("Object"===t&&"function"==typeof e.constructor){const t=e.constructor.name;if("string"==typeof t&&""!==t)return t}return t}(e)+"]";const o=n.map((([e,n])=>e+": "+i(n,t)));return"{ "+o.join(", ")+" }"}(e,o)}(e,t);default:return String(e)}}},9527:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.instanceOf=void 0;var r=n(9657);const i=globalThis.process&&"production"===globalThis.process.env.NODE_ENV?function(e,t){return e instanceof t}:function(e,t){if(e instanceof t)return!0;if("object"==typeof e&&null!==e){var n;const i=t.prototype[Symbol.toStringTag];if(i===(Symbol.toStringTag in e?e[Symbol.toStringTag]:null===(n=e.constructor)||void 0===n?void 0:n.name)){const t=(0,r.inspect)(e);throw new Error(`Cannot use ${i} "${t}" from another module or realm.\n\nEnsure that there is only one instance of "graphql" in the node_modules\ndirectory. If different versions of "graphql" are the dependencies of other\nrelied on modules, use "resolutions" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate "graphql" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`)}}return!1};t.instanceOf=i},1321:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.invariant=function(e,t){if(!Boolean(e))throw new Error(null!=t?t:"Unexpected invariant triggered.")}},1619:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isAsyncIterable=function(e){return"function"==typeof(null==e?void 0:e[Symbol.asyncIterator])}},4820:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isIterableObject=function(e){return"object"==typeof e&&"function"==typeof(null==e?void 0:e[Symbol.iterator])}},5569:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},7724:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isPromise=function(e){return"function"==typeof(null==e?void 0:e.then)}},4590:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.keyMap=function(e,t){const n=Object.create(null);for(const r of e)n[t(r)]=r;return n}},5785:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.keyValMap=function(e,t,n){const r=Object.create(null);for(const i of e)r[t(i)]=n(i);return r}},3430:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.mapValue=function(e,t){const n=Object.create(null);for(const r of Object.keys(e))n[r]=t(e[r],r);return n}},2104:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.memoize3=function(e){let t;return function(n,r,i){void 0===t&&(t=new WeakMap);let o=t.get(n);void 0===o&&(o=new WeakMap,t.set(n,o));let a=o.get(r);void 0===a&&(a=new WeakMap,o.set(r,a));let s=a.get(i);return void 0===s&&(s=e(n,r,i),a.set(i,s)),s}}},5745:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.naturalCompare=function(e,t){let r=0,o=0;for(;r<e.length&&o<t.length;){let a=e.charCodeAt(r),s=t.charCodeAt(o);if(i(a)&&i(s)){let u=0;do{++r,u=10*u+a-n,a=e.charCodeAt(r)}while(i(a)&&u>0);let c=0;do{++o,c=10*c+s-n,s=t.charCodeAt(o)}while(i(s)&&c>0);if(u<c)return-1;if(u>c)return 1}else{if(a<s)return-1;if(a>s)return 1;++r,++o}}return e.length-t.length};const n=48,r=57;function i(e){return!isNaN(e)&&n<=e&&e<=r}},636:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.printPathArray=function(e){return e.map((e=>"number"==typeof e?"["+e.toString()+"]":"."+e)).join("")}},4702:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.promiseForObject=function(e){return Promise.all(Object.values(e)).then((t=>{const n=Object.create(null);for(const[r,i]of Object.keys(e).entries())n[i]=t[r];return n}))}},5662:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.promiseReduce=function(e,t,n){let i=n;for(const n of e)i=(0,r.isPromise)(i)?i.then((e=>t(e,n))):t(i,n);return i};var r=n(7724)},1709:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.suggestionList=function(e,t){const n=Object.create(null),o=new i(e),a=Math.floor(.4*e.length)+1;for(const e of t){const t=o.measure(e,a);void 0!==t&&(n[e]=t)}return Object.keys(n).sort(((e,t)=>{const i=n[e]-n[t];return 0!==i?i:(0,r.naturalCompare)(e,t)}))};var r=n(5745);class i{constructor(e){this._input=e,this._inputLowerCase=e.toLowerCase(),this._inputArray=o(this._inputLowerCase),this._rows=[new Array(e.length+1).fill(0),new Array(e.length+1).fill(0),new Array(e.length+1).fill(0)]}measure(e,t){if(this._input===e)return 0;const n=e.toLowerCase();if(this._inputLowerCase===n)return 1;let r=o(n),i=this._inputArray;if(r.length<i.length){const e=r;r=i,i=e}const a=r.length,s=i.length;if(a-s>t)return;const u=this._rows;for(let e=0;e<=s;e++)u[0][e]=e;for(let e=1;e<=a;e++){const n=u[(e-1)%3],o=u[e%3];let a=o[0]=e;for(let t=1;t<=s;t++){const s=r[e-1]===i[t-1]?0:1;let c=Math.min(n[t]+1,o[t-1]+1,n[t-1]+s);if(e>1&&t>1&&r[e-1]===i[t-2]&&r[e-2]===i[t-1]){const n=u[(e-2)%3][t-2];c=Math.min(c,n+1)}c<a&&(a=c),o[t]=c}if(a>t)return}const c=u[a%3][s];return c<=t?c:void 0}}function o(e){const t=e.length,n=new Array(t);for(let r=0;r<t;++r)n[r]=e.charCodeAt(r);return n}},2036:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.toError=function(e){return e instanceof Error?e:new i(e)};var r=n(9657);class i extends Error{constructor(e){super("Unexpected error value: "+(0,r.inspect)(e)),this.name="NonErrorThrown",this.thrownValue=e}}},3101:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.toObjMap=function(e){if(null==e)return Object.create(null);if(null===Object.getPrototypeOf(e))return e;const t=Object.create(null);for(const[n,r]of Object.entries(e))t[n]=r;return t}},6257:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Token=t.QueryDocumentKeys=t.OperationTypeNode=t.Location=void 0,t.isNode=function(e){const t=null==e?void 0:e.kind;return"string"==typeof t&&o.has(t)};class n{constructor(e,t,n){this.start=e.start,this.end=t.end,this.startToken=e,this.endToken=t,this.source=n}get[Symbol.toStringTag](){return"Location"}toJSON(){return{start:this.start,end:this.end}}}t.Location=n;class r{constructor(e,t,n,r,i,o){this.kind=e,this.start=t,this.end=n,this.line=r,this.column=i,this.value=o,this.prev=null,this.next=null}get[Symbol.toStringTag](){return"Token"}toJSON(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}}}t.Token=r;const i={Name:[],Document:["definitions"],OperationDefinition:["name","variableDefinitions","directives","selectionSet"],VariableDefinition:["variable","type","defaultValue","directives"],Variable:["name"],SelectionSet:["selections"],Field:["alias","name","arguments","directives","selectionSet"],Argument:["name","value"],FragmentSpread:["name","directives"],InlineFragment:["typeCondition","directives","selectionSet"],FragmentDefinition:["name","variableDefinitions","typeCondition","directives","selectionSet"],IntValue:[],FloatValue:[],StringValue:[],BooleanValue:[],NullValue:[],EnumValue:[],ListValue:["values"],ObjectValue:["fields"],ObjectField:["name","value"],Directive:["name","arguments"],NamedType:["name"],ListType:["type"],NonNullType:["type"],SchemaDefinition:["description","directives","operationTypes"],OperationTypeDefinition:["type"],ScalarTypeDefinition:["description","name","directives"],ObjectTypeDefinition:["description","name","interfaces","directives","fields"],FieldDefinition:["description","name","arguments","type","directives"],InputValueDefinition:["description","name","type","defaultValue","directives"],InterfaceTypeDefinition:["description","name","interfaces","directives","fields"],UnionTypeDefinition:["description","name","directives","types"],EnumTypeDefinition:["description","name","directives","values"],EnumValueDefinition:["description","name","directives"],InputObjectTypeDefinition:["description","name","directives","fields"],DirectiveDefinition:["description","name","arguments","locations"],SchemaExtension:["directives","operationTypes"],ScalarTypeExtension:["name","directives"],ObjectTypeExtension:["name","interfaces","directives","fields"],InterfaceTypeExtension:["name","interfaces","directives","fields"],UnionTypeExtension:["name","directives","types"],EnumTypeExtension:["name","directives","values"],InputObjectTypeExtension:["name","directives","fields"]};t.QueryDocumentKeys=i;const o=new Set(Object.keys(i));var a;t.OperationTypeNode=a,function(e){e.QUERY="query",e.MUTATION="mutation",e.SUBSCRIPTION="subscription"}(a||(t.OperationTypeNode=a={}))},9165:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.dedentBlockStringLines=function(e){var t;let n=Number.MAX_SAFE_INTEGER,r=null,o=-1;for(let t=0;t<e.length;++t){var a;const s=e[t],u=i(s);u!==s.length&&(r=null!==(a=r)&&void 0!==a?a:t,o=t,0!==t&&u<n&&(n=u))}return e.map(((e,t)=>0===t?e:e.slice(n))).slice(null!==(t=r)&&void 0!==t?t:0,o+1)},t.isPrintableAsBlockString=function(e){if(""===e)return!0;let t=!0,n=!1,r=!0,i=!1;for(let o=0;o<e.length;++o)switch(e.codePointAt(o)){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 11:case 12:case 14:case 15:case 13:return!1;case 10:if(t&&!i)return!1;i=!0,t=!0,n=!1;break;case 9:case 32:n||(n=t);break;default:r&&(r=n),t=!1}return!t&&(!r||!i)},t.printBlockString=function(e,t){const n=e.replace(/"""/g,'\\"""'),i=n.split(/\r\n|[\n\r]/g),o=1===i.length,a=i.length>1&&i.slice(1).every((e=>0===e.length||(0,r.isWhiteSpace)(e.charCodeAt(0)))),s=n.endsWith('\\"""'),u=e.endsWith('"')&&!s,c=e.endsWith("\\"),p=u||c,l=!(null!=t&&t.minimize)&&(!o||e.length>70||p||a||s);let d="";const f=o&&(0,r.isWhiteSpace)(e.charCodeAt(0));return(l&&!f||a)&&(d+="\n"),d+=n,(l||p)&&(d+="\n"),'"""'+d+'"""'};var r=n(3932);function i(e){let t=0;for(;t<e.length&&(0,r.isWhiteSpace)(e.charCodeAt(t));)++t;return t}},3932:(e,t)=>{function n(e){return e>=48&&e<=57}function r(e){return e>=97&&e<=122||e>=65&&e<=90}Object.defineProperty(t,"__esModule",{value:!0}),t.isDigit=n,t.isLetter=r,t.isNameContinue=function(e){return r(e)||n(e)||95===e},t.isNameStart=function(e){return r(e)||95===e},t.isWhiteSpace=function(e){return 9===e||32===e}},5919:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.DirectiveLocation=void 0,t.DirectiveLocation=n,function(e){e.QUERY="QUERY",e.MUTATION="MUTATION",e.SUBSCRIPTION="SUBSCRIPTION",e.FIELD="FIELD",e.FRAGMENT_DEFINITION="FRAGMENT_DEFINITION",e.FRAGMENT_SPREAD="FRAGMENT_SPREAD",e.INLINE_FRAGMENT="INLINE_FRAGMENT",e.VARIABLE_DEFINITION="VARIABLE_DEFINITION",e.SCHEMA="SCHEMA",e.SCALAR="SCALAR",e.OBJECT="OBJECT",e.FIELD_DEFINITION="FIELD_DEFINITION",e.ARGUMENT_DEFINITION="ARGUMENT_DEFINITION",e.INTERFACE="INTERFACE",e.UNION="UNION",e.ENUM="ENUM",e.ENUM_VALUE="ENUM_VALUE",e.INPUT_OBJECT="INPUT_OBJECT",e.INPUT_FIELD_DEFINITION="INPUT_FIELD_DEFINITION"}(n||(t.DirectiveLocation=n={}))},425:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BREAK",{enumerable:!0,get:function(){return l.BREAK}}),Object.defineProperty(t,"DirectiveLocation",{enumerable:!0,get:function(){return y.DirectiveLocation}}),Object.defineProperty(t,"Kind",{enumerable:!0,get:function(){return a.Kind}}),Object.defineProperty(t,"Lexer",{enumerable:!0,get:function(){return u.Lexer}}),Object.defineProperty(t,"Location",{enumerable:!0,get:function(){return d.Location}}),Object.defineProperty(t,"OperationTypeNode",{enumerable:!0,get:function(){return d.OperationTypeNode}}),Object.defineProperty(t,"Source",{enumerable:!0,get:function(){return r.Source}}),Object.defineProperty(t,"Token",{enumerable:!0,get:function(){return d.Token}}),Object.defineProperty(t,"TokenKind",{enumerable:!0,get:function(){return s.TokenKind}}),Object.defineProperty(t,"getEnterLeaveForKind",{enumerable:!0,get:function(){return l.getEnterLeaveForKind}}),Object.defineProperty(t,"getLocation",{enumerable:!0,get:function(){return i.getLocation}}),Object.defineProperty(t,"getVisitFn",{enumerable:!0,get:function(){return l.getVisitFn}}),Object.defineProperty(t,"isConstValueNode",{enumerable:!0,get:function(){return f.isConstValueNode}}),Object.defineProperty(t,"isDefinitionNode",{enumerable:!0,get:function(){return f.isDefinitionNode}}),Object.defineProperty(t,"isExecutableDefinitionNode",{enumerable:!0,get:function(){return f.isExecutableDefinitionNode}}),Object.defineProperty(t,"isSelectionNode",{enumerable:!0,get:function(){return f.isSelectionNode}}),Object.defineProperty(t,"isTypeDefinitionNode",{enumerable:!0,get:function(){return f.isTypeDefinitionNode}}),Object.defineProperty(t,"isTypeExtensionNode",{enumerable:!0,get:function(){return f.isTypeExtensionNode}}),Object.defineProperty(t,"isTypeNode",{enumerable:!0,get:function(){return f.isTypeNode}}),Object.defineProperty(t,"isTypeSystemDefinitionNode",{enumerable:!0,get:function(){return f.isTypeSystemDefinitionNode}}),Object.defineProperty(t,"isTypeSystemExtensionNode",{enumerable:!0,get:function(){return f.isTypeSystemExtensionNode}}),Object.defineProperty(t,"isValueNode",{enumerable:!0,get:function(){return f.isValueNode}}),Object.defineProperty(t,"parse",{enumerable:!0,get:function(){return c.parse}}),Object.defineProperty(t,"parseConstValue",{enumerable:!0,get:function(){return c.parseConstValue}}),Object.defineProperty(t,"parseType",{enumerable:!0,get:function(){return c.parseType}}),Object.defineProperty(t,"parseValue",{enumerable:!0,get:function(){return c.parseValue}}),Object.defineProperty(t,"print",{enumerable:!0,get:function(){return p.print}}),Object.defineProperty(t,"printLocation",{enumerable:!0,get:function(){return o.printLocation}}),Object.defineProperty(t,"printSourceLocation",{enumerable:!0,get:function(){return o.printSourceLocation}}),Object.defineProperty(t,"visit",{enumerable:!0,get:function(){return l.visit}}),Object.defineProperty(t,"visitInParallel",{enumerable:!0,get:function(){return l.visitInParallel}});var r=n(6876),i=n(9530),o=n(825),a=n(7030),s=n(3038),u=n(6083),c=n(246),p=n(585),l=n(9111),d=n(6257),f=n(9187),y=n(5919)},7030:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Kind=void 0,t.Kind=n,function(e){e.NAME="Name",e.DOCUMENT="Document",e.OPERATION_DEFINITION="OperationDefinition",e.VARIABLE_DEFINITION="VariableDefinition",e.SELECTION_SET="SelectionSet",e.FIELD="Field",e.ARGUMENT="Argument",e.FRAGMENT_SPREAD="FragmentSpread",e.INLINE_FRAGMENT="InlineFragment",e.FRAGMENT_DEFINITION="FragmentDefinition",e.VARIABLE="Variable",e.INT="IntValue",e.FLOAT="FloatValue",e.STRING="StringValue",e.BOOLEAN="BooleanValue",e.NULL="NullValue",e.ENUM="EnumValue",e.LIST="ListValue",e.OBJECT="ObjectValue",e.OBJECT_FIELD="ObjectField",e.DIRECTIVE="Directive",e.NAMED_TYPE="NamedType",e.LIST_TYPE="ListType",e.NON_NULL_TYPE="NonNullType",e.SCHEMA_DEFINITION="SchemaDefinition",e.OPERATION_TYPE_DEFINITION="OperationTypeDefinition",e.SCALAR_TYPE_DEFINITION="ScalarTypeDefinition",e.OBJECT_TYPE_DEFINITION="ObjectTypeDefinition",e.FIELD_DEFINITION="FieldDefinition",e.INPUT_VALUE_DEFINITION="InputValueDefinition",e.INTERFACE_TYPE_DEFINITION="InterfaceTypeDefinition",e.UNION_TYPE_DEFINITION="UnionTypeDefinition",e.ENUM_TYPE_DEFINITION="EnumTypeDefinition",e.ENUM_VALUE_DEFINITION="EnumValueDefinition",e.INPUT_OBJECT_TYPE_DEFINITION="InputObjectTypeDefinition",e.DIRECTIVE_DEFINITION="DirectiveDefinition",e.SCHEMA_EXTENSION="SchemaExtension",e.SCALAR_TYPE_EXTENSION="ScalarTypeExtension",e.OBJECT_TYPE_EXTENSION="ObjectTypeExtension",e.INTERFACE_TYPE_EXTENSION="InterfaceTypeExtension",e.UNION_TYPE_EXTENSION="UnionTypeExtension",e.ENUM_TYPE_EXTENSION="EnumTypeExtension",e.INPUT_OBJECT_TYPE_EXTENSION="InputObjectTypeExtension"}(n||(t.Kind=n={}))},6083:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Lexer=void 0,t.isPunctuatorTokenKind=function(e){return e===s.TokenKind.BANG||e===s.TokenKind.DOLLAR||e===s.TokenKind.AMP||e===s.TokenKind.PAREN_L||e===s.TokenKind.PAREN_R||e===s.TokenKind.SPREAD||e===s.TokenKind.COLON||e===s.TokenKind.EQUALS||e===s.TokenKind.AT||e===s.TokenKind.BRACKET_L||e===s.TokenKind.BRACKET_R||e===s.TokenKind.BRACE_L||e===s.TokenKind.PIPE||e===s.TokenKind.BRACE_R};var r=n(1352),i=n(6257),o=n(9165),a=n(3932),s=n(3038);class u{constructor(e){const t=new i.Token(s.TokenKind.SOF,0,0,0,0);this.source=e,this.lastToken=t,this.token=t,this.line=1,this.lineStart=0}get[Symbol.toStringTag](){return"Lexer"}advance(){return this.lastToken=this.token,this.token=this.lookahead()}lookahead(){let e=this.token;if(e.kind!==s.TokenKind.EOF)do{if(e.next)e=e.next;else{const t=m(this,e.end);e.next=t,t.prev=e,e=t}}while(e.kind===s.TokenKind.COMMENT);return e}}function c(e){return e>=0&&e<=55295||e>=57344&&e<=1114111}function p(e,t){return l(e.charCodeAt(t))&&d(e.charCodeAt(t+1))}function l(e){return e>=55296&&e<=56319}function d(e){return e>=56320&&e<=57343}function f(e,t){const n=e.source.body.codePointAt(t);if(void 0===n)return s.TokenKind.EOF;if(n>=32&&n<=126){const e=String.fromCodePoint(n);return'"'===e?"'\"'":`"${e}"`}return"U+"+n.toString(16).toUpperCase().padStart(4,"0")}function y(e,t,n,r,o){const a=e.line,s=1+n-e.lineStart;return new i.Token(t,n,r,a,s,o)}function m(e,t){const n=e.source.body,i=n.length;let o=t;for(;o<i;){const t=n.charCodeAt(o);switch(t){case 65279:case 9:case 32:case 44:++o;continue;case 10:++o,++e.line,e.lineStart=o;continue;case 13:10===n.charCodeAt(o+1)?o+=2:++o,++e.line,e.lineStart=o;continue;case 35:return h(e,o);case 33:return y(e,s.TokenKind.BANG,o,o+1);case 36:return y(e,s.TokenKind.DOLLAR,o,o+1);case 38:return y(e,s.TokenKind.AMP,o,o+1);case 40:return y(e,s.TokenKind.PAREN_L,o,o+1);case 41:return y(e,s.TokenKind.PAREN_R,o,o+1);case 46:if(46===n.charCodeAt(o+1)&&46===n.charCodeAt(o+2))return y(e,s.TokenKind.SPREAD,o,o+3);break;case 58:return y(e,s.TokenKind.COLON,o,o+1);case 61:return y(e,s.TokenKind.EQUALS,o,o+1);case 64:return y(e,s.TokenKind.AT,o,o+1);case 91:return y(e,s.TokenKind.BRACKET_L,o,o+1);case 93:return y(e,s.TokenKind.BRACKET_R,o,o+1);case 123:return y(e,s.TokenKind.BRACE_L,o,o+1);case 124:return y(e,s.TokenKind.PIPE,o,o+1);case 125:return y(e,s.TokenKind.BRACE_R,o,o+1);case 34:return 34===n.charCodeAt(o+1)&&34===n.charCodeAt(o+2)?_(e,o):v(e,o)}if((0,a.isDigit)(t)||45===t)return T(e,o,t);if((0,a.isNameStart)(t))return L(e,o);throw(0,r.syntaxError)(e.source,o,39===t?"Unexpected single quote character ('), did you mean to use a double quote (\")?":c(t)||p(n,o)?`Unexpected character: ${f(e,o)}.`:`Invalid character: ${f(e,o)}.`)}return y(e,s.TokenKind.EOF,i,i)}function h(e,t){const n=e.source.body,r=n.length;let i=t+1;for(;i<r;){const e=n.charCodeAt(i);if(10===e||13===e)break;if(c(e))++i;else{if(!p(n,i))break;i+=2}}return y(e,s.TokenKind.COMMENT,t,i,n.slice(t+1,i))}function T(e,t,n){const i=e.source.body;let o=t,u=n,c=!1;if(45===u&&(u=i.charCodeAt(++o)),48===u){if(u=i.charCodeAt(++o),(0,a.isDigit)(u))throw(0,r.syntaxError)(e.source,o,`Invalid number, unexpected digit after 0: ${f(e,o)}.`)}else o=b(e,o,u),u=i.charCodeAt(o);if(46===u&&(c=!0,u=i.charCodeAt(++o),o=b(e,o,u),u=i.charCodeAt(o)),69!==u&&101!==u||(c=!0,u=i.charCodeAt(++o),43!==u&&45!==u||(u=i.charCodeAt(++o)),o=b(e,o,u),u=i.charCodeAt(o)),46===u||(0,a.isNameStart)(u))throw(0,r.syntaxError)(e.source,o,`Invalid number, expected digit but got: ${f(e,o)}.`);return y(e,c?s.TokenKind.FLOAT:s.TokenKind.INT,t,o,i.slice(t,o))}function b(e,t,n){if(!(0,a.isDigit)(n))throw(0,r.syntaxError)(e.source,t,`Invalid number, expected digit but got: ${f(e,t)}.`);const i=e.source.body;let o=t+1;for(;(0,a.isDigit)(i.charCodeAt(o));)++o;return o}function v(e,t){const n=e.source.body,i=n.length;let o=t+1,a=o,u="";for(;o<i;){const i=n.charCodeAt(o);if(34===i)return u+=n.slice(a,o),y(e,s.TokenKind.STRING,t,o+1,u);if(92!==i){if(10===i||13===i)break;if(c(i))++o;else{if(!p(n,o))throw(0,r.syntaxError)(e.source,o,`Invalid character within String: ${f(e,o)}.`);o+=2}}else{u+=n.slice(a,o);const t=117===n.charCodeAt(o+1)?123===n.charCodeAt(o+2)?g(e,o):E(e,o):I(e,o);u+=t.value,o+=t.size,a=o}}throw(0,r.syntaxError)(e.source,o,"Unterminated string.")}function g(e,t){const n=e.source.body;let i=0,o=3;for(;o<12;){const e=n.charCodeAt(t+o++);if(125===e){if(o<5||!c(i))break;return{value:String.fromCodePoint(i),size:o}}if(i=i<<4|O(e),i<0)break}throw(0,r.syntaxError)(e.source,t,`Invalid Unicode escape sequence: "${n.slice(t,t+o)}".`)}function E(e,t){const n=e.source.body,i=N(n,t+2);if(c(i))return{value:String.fromCodePoint(i),size:6};if(l(i)&&92===n.charCodeAt(t+6)&&117===n.charCodeAt(t+7)){const e=N(n,t+8);if(d(e))return{value:String.fromCodePoint(i,e),size:12}}throw(0,r.syntaxError)(e.source,t,`Invalid Unicode escape sequence: "${n.slice(t,t+6)}".`)}function N(e,t){return O(e.charCodeAt(t))<<12|O(e.charCodeAt(t+1))<<8|O(e.charCodeAt(t+2))<<4|O(e.charCodeAt(t+3))}function O(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:-1}function I(e,t){const n=e.source.body;switch(n.charCodeAt(t+1)){case 34:return{value:'"',size:2};case 92:return{value:"\\",size:2};case 47:return{value:"/",size:2};case 98:return{value:"\b",size:2};case 102:return{value:"\f",size:2};case 110:return{value:"\n",size:2};case 114:return{value:"\r",size:2};case 116:return{value:"\t",size:2}}throw(0,r.syntaxError)(e.source,t,`Invalid character escape sequence: "${n.slice(t,t+2)}".`)}function _(e,t){const n=e.source.body,i=n.length;let a=e.lineStart,u=t+3,l=u,d="";const m=[];for(;u<i;){const i=n.charCodeAt(u);if(34===i&&34===n.charCodeAt(u+1)&&34===n.charCodeAt(u+2)){d+=n.slice(l,u),m.push(d);const r=y(e,s.TokenKind.BLOCK_STRING,t,u+3,(0,o.dedentBlockStringLines)(m).join("\n"));return e.line+=m.length-1,e.lineStart=a,r}if(92!==i||34!==n.charCodeAt(u+1)||34!==n.charCodeAt(u+2)||34!==n.charCodeAt(u+3))if(10!==i&&13!==i)if(c(i))++u;else{if(!p(n,u))throw(0,r.syntaxError)(e.source,u,`Invalid character within String: ${f(e,u)}.`);u+=2}else d+=n.slice(l,u),m.push(d),13===i&&10===n.charCodeAt(u+1)?u+=2:++u,d="",l=u,a=u;else d+=n.slice(l,u),l=u+1,u+=4}throw(0,r.syntaxError)(e.source,u,"Unterminated string.")}function L(e,t){const n=e.source.body,r=n.length;let i=t+1;for(;i<r;){const e=n.charCodeAt(i);if(!(0,a.isNameContinue)(e))break;++i}return y(e,s.TokenKind.NAME,t,i,n.slice(t,i))}t.Lexer=u},9530:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getLocation=function(e,t){let n=0,o=1;for(const a of e.body.matchAll(i)){if("number"==typeof a.index||(0,r.invariant)(!1),a.index>=t)break;n=a.index+a[0].length,o+=1}return{line:o,column:t+1-n}};var r=n(1321);const i=/\r\n|[\n\r]/g},246:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Parser=void 0,t.parse=function(e,t){return new p(e,t).parseDocument()},t.parseConstValue=function(e,t){const n=new p(e,t);n.expectToken(c.TokenKind.SOF);const r=n.parseConstValueLiteral();return n.expectToken(c.TokenKind.EOF),r},t.parseType=function(e,t){const n=new p(e,t);n.expectToken(c.TokenKind.SOF);const r=n.parseTypeReference();return n.expectToken(c.TokenKind.EOF),r},t.parseValue=function(e,t){const n=new p(e,t);n.expectToken(c.TokenKind.SOF);const r=n.parseValueLiteral(!1);return n.expectToken(c.TokenKind.EOF),r};var r=n(1352),i=n(6257),o=n(5919),a=n(7030),s=n(6083),u=n(6876),c=n(3038);class p{constructor(e,t={}){const n=(0,u.isSource)(e)?e:new u.Source(e);this._lexer=new s.Lexer(n),this._options=t,this._tokenCounter=0}parseName(){const e=this.expectToken(c.TokenKind.NAME);return this.node(e,{kind:a.Kind.NAME,value:e.value})}parseDocument(){return this.node(this._lexer.token,{kind:a.Kind.DOCUMENT,definitions:this.many(c.TokenKind.SOF,this.parseDefinition,c.TokenKind.EOF)})}parseDefinition(){if(this.peek(c.TokenKind.BRACE_L))return this.parseOperationDefinition();const e=this.peekDescription(),t=e?this._lexer.lookahead():this._lexer.token;if(t.kind===c.TokenKind.NAME){switch(t.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}if(e)throw(0,r.syntaxError)(this._lexer.source,this._lexer.token.start,"Unexpected description, descriptions are supported only on type definitions.");switch(t.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"extend":return this.parseTypeSystemExtension()}}throw this.unexpected(t)}parseOperationDefinition(){const e=this._lexer.token;if(this.peek(c.TokenKind.BRACE_L))return this.node(e,{kind:a.Kind.OPERATION_DEFINITION,operation:i.OperationTypeNode.QUERY,name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet()});const t=this.parseOperationType();let n;return this.peek(c.TokenKind.NAME)&&(n=this.parseName()),this.node(e,{kind:a.Kind.OPERATION_DEFINITION,operation:t,name:n,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseOperationType(){const e=this.expectToken(c.TokenKind.NAME);switch(e.value){case"query":return i.OperationTypeNode.QUERY;case"mutation":return i.OperationTypeNode.MUTATION;case"subscription":return i.OperationTypeNode.SUBSCRIPTION}throw this.unexpected(e)}parseVariableDefinitions(){return this.optionalMany(c.TokenKind.PAREN_L,this.parseVariableDefinition,c.TokenKind.PAREN_R)}parseVariableDefinition(){return this.node(this._lexer.token,{kind:a.Kind.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(c.TokenKind.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(c.TokenKind.EQUALS)?this.parseConstValueLiteral():void 0,directives:this.parseConstDirectives()})}parseVariable(){const e=this._lexer.token;return this.expectToken(c.TokenKind.DOLLAR),this.node(e,{kind:a.Kind.VARIABLE,name:this.parseName()})}parseSelectionSet(){return this.node(this._lexer.token,{kind:a.Kind.SELECTION_SET,selections:this.many(c.TokenKind.BRACE_L,this.parseSelection,c.TokenKind.BRACE_R)})}parseSelection(){return this.peek(c.TokenKind.SPREAD)?this.parseFragment():this.parseField()}parseField(){const e=this._lexer.token,t=this.parseName();let n,r;return this.expectOptionalToken(c.TokenKind.COLON)?(n=t,r=this.parseName()):r=t,this.node(e,{kind:a.Kind.FIELD,alias:n,name:r,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(c.TokenKind.BRACE_L)?this.parseSelectionSet():void 0})}parseArguments(e){const t=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(c.TokenKind.PAREN_L,t,c.TokenKind.PAREN_R)}parseArgument(e=!1){const t=this._lexer.token,n=this.parseName();return this.expectToken(c.TokenKind.COLON),this.node(t,{kind:a.Kind.ARGUMENT,name:n,value:this.parseValueLiteral(e)})}parseConstArgument(){return this.parseArgument(!0)}parseFragment(){const e=this._lexer.token;this.expectToken(c.TokenKind.SPREAD);const t=this.expectOptionalKeyword("on");return!t&&this.peek(c.TokenKind.NAME)?this.node(e,{kind:a.Kind.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1)}):this.node(e,{kind:a.Kind.INLINE_FRAGMENT,typeCondition:t?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentDefinition(){const e=this._lexer.token;return this.expectKeyword("fragment"),!0===this._options.allowLegacyFragmentVariables?this.node(e,{kind:a.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()}):this.node(e,{kind:a.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentName(){if("on"===this._lexer.token.value)throw this.unexpected();return this.parseName()}parseValueLiteral(e){const t=this._lexer.token;switch(t.kind){case c.TokenKind.BRACKET_L:return this.parseList(e);case c.TokenKind.BRACE_L:return this.parseObject(e);case c.TokenKind.INT:return this.advanceLexer(),this.node(t,{kind:a.Kind.INT,value:t.value});case c.TokenKind.FLOAT:return this.advanceLexer(),this.node(t,{kind:a.Kind.FLOAT,value:t.value});case c.TokenKind.STRING:case c.TokenKind.BLOCK_STRING:return this.parseStringLiteral();case c.TokenKind.NAME:switch(this.advanceLexer(),t.value){case"true":return this.node(t,{kind:a.Kind.BOOLEAN,value:!0});case"false":return this.node(t,{kind:a.Kind.BOOLEAN,value:!1});case"null":return this.node(t,{kind:a.Kind.NULL});default:return this.node(t,{kind:a.Kind.ENUM,value:t.value})}case c.TokenKind.DOLLAR:if(e){if(this.expectToken(c.TokenKind.DOLLAR),this._lexer.token.kind===c.TokenKind.NAME){const e=this._lexer.token.value;throw(0,r.syntaxError)(this._lexer.source,t.start,`Unexpected variable "$${e}" in constant value.`)}throw this.unexpected(t)}return this.parseVariable();default:throw this.unexpected()}}parseConstValueLiteral(){return this.parseValueLiteral(!0)}parseStringLiteral(){const e=this._lexer.token;return this.advanceLexer(),this.node(e,{kind:a.Kind.STRING,value:e.value,block:e.kind===c.TokenKind.BLOCK_STRING})}parseList(e){return this.node(this._lexer.token,{kind:a.Kind.LIST,values:this.any(c.TokenKind.BRACKET_L,(()=>this.parseValueLiteral(e)),c.TokenKind.BRACKET_R)})}parseObject(e){return this.node(this._lexer.token,{kind:a.Kind.OBJECT,fields:this.any(c.TokenKind.BRACE_L,(()=>this.parseObjectField(e)),c.TokenKind.BRACE_R)})}parseObjectField(e){const t=this._lexer.token,n=this.parseName();return this.expectToken(c.TokenKind.COLON),this.node(t,{kind:a.Kind.OBJECT_FIELD,name:n,value:this.parseValueLiteral(e)})}parseDirectives(e){const t=[];for(;this.peek(c.TokenKind.AT);)t.push(this.parseDirective(e));return t}parseConstDirectives(){return this.parseDirectives(!0)}parseDirective(e){const t=this._lexer.token;return this.expectToken(c.TokenKind.AT),this.node(t,{kind:a.Kind.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e)})}parseTypeReference(){const e=this._lexer.token;let t;if(this.expectOptionalToken(c.TokenKind.BRACKET_L)){const n=this.parseTypeReference();this.expectToken(c.TokenKind.BRACKET_R),t=this.node(e,{kind:a.Kind.LIST_TYPE,type:n})}else t=this.parseNamedType();return this.expectOptionalToken(c.TokenKind.BANG)?this.node(e,{kind:a.Kind.NON_NULL_TYPE,type:t}):t}parseNamedType(){return this.node(this._lexer.token,{kind:a.Kind.NAMED_TYPE,name:this.parseName()})}peekDescription(){return this.peek(c.TokenKind.STRING)||this.peek(c.TokenKind.BLOCK_STRING)}parseDescription(){if(this.peekDescription())return this.parseStringLiteral()}parseSchemaDefinition(){const e=this._lexer.token,t=this.parseDescription();this.expectKeyword("schema");const n=this.parseConstDirectives(),r=this.many(c.TokenKind.BRACE_L,this.parseOperationTypeDefinition,c.TokenKind.BRACE_R);return this.node(e,{kind:a.Kind.SCHEMA_DEFINITION,description:t,directives:n,operationTypes:r})}parseOperationTypeDefinition(){const e=this._lexer.token,t=this.parseOperationType();this.expectToken(c.TokenKind.COLON);const n=this.parseNamedType();return this.node(e,{kind:a.Kind.OPERATION_TYPE_DEFINITION,operation:t,type:n})}parseScalarTypeDefinition(){const e=this._lexer.token,t=this.parseDescription();this.expectKeyword("scalar");const n=this.parseName(),r=this.parseConstDirectives();return this.node(e,{kind:a.Kind.SCALAR_TYPE_DEFINITION,description:t,name:n,directives:r})}parseObjectTypeDefinition(){const e=this._lexer.token,t=this.parseDescription();this.expectKeyword("type");const n=this.parseName(),r=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),o=this.parseFieldsDefinition();return this.node(e,{kind:a.Kind.OBJECT_TYPE_DEFINITION,description:t,name:n,interfaces:r,directives:i,fields:o})}parseImplementsInterfaces(){return this.expectOptionalKeyword("implements")?this.delimitedMany(c.TokenKind.AMP,this.parseNamedType):[]}parseFieldsDefinition(){return this.optionalMany(c.TokenKind.BRACE_L,this.parseFieldDefinition,c.TokenKind.BRACE_R)}parseFieldDefinition(){const e=this._lexer.token,t=this.parseDescription(),n=this.parseName(),r=this.parseArgumentDefs();this.expectToken(c.TokenKind.COLON);const i=this.parseTypeReference(),o=this.parseConstDirectives();return this.node(e,{kind:a.Kind.FIELD_DEFINITION,description:t,name:n,arguments:r,type:i,directives:o})}parseArgumentDefs(){return this.optionalMany(c.TokenKind.PAREN_L,this.parseInputValueDef,c.TokenKind.PAREN_R)}parseInputValueDef(){const e=this._lexer.token,t=this.parseDescription(),n=this.parseName();this.expectToken(c.TokenKind.COLON);const r=this.parseTypeReference();let i;this.expectOptionalToken(c.TokenKind.EQUALS)&&(i=this.parseConstValueLiteral());const o=this.parseConstDirectives();return this.node(e,{kind:a.Kind.INPUT_VALUE_DEFINITION,description:t,name:n,type:r,defaultValue:i,directives:o})}parseInterfaceTypeDefinition(){const e=this._lexer.token,t=this.parseDescription();this.expectKeyword("interface");const n=this.parseName(),r=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),o=this.parseFieldsDefinition();return this.node(e,{kind:a.Kind.INTERFACE_TYPE_DEFINITION,description:t,name:n,interfaces:r,directives:i,fields:o})}parseUnionTypeDefinition(){const e=this._lexer.token,t=this.parseDescription();this.expectKeyword("union");const n=this.parseName(),r=this.parseConstDirectives(),i=this.parseUnionMemberTypes();return this.node(e,{kind:a.Kind.UNION_TYPE_DEFINITION,description:t,name:n,directives:r,types:i})}parseUnionMemberTypes(){return this.expectOptionalToken(c.TokenKind.EQUALS)?this.delimitedMany(c.TokenKind.PIPE,this.parseNamedType):[]}parseEnumTypeDefinition(){const e=this._lexer.token,t=this.parseDescription();this.expectKeyword("enum");const n=this.parseName(),r=this.parseConstDirectives(),i=this.parseEnumValuesDefinition();return this.node(e,{kind:a.Kind.ENUM_TYPE_DEFINITION,description:t,name:n,directives:r,values:i})}parseEnumValuesDefinition(){return this.optionalMany(c.TokenKind.BRACE_L,this.parseEnumValueDefinition,c.TokenKind.BRACE_R)}parseEnumValueDefinition(){const e=this._lexer.token,t=this.parseDescription(),n=this.parseEnumValueName(),r=this.parseConstDirectives();return this.node(e,{kind:a.Kind.ENUM_VALUE_DEFINITION,description:t,name:n,directives:r})}parseEnumValueName(){if("true"===this._lexer.token.value||"false"===this._lexer.token.value||"null"===this._lexer.token.value)throw(0,r.syntaxError)(this._lexer.source,this._lexer.token.start,`${l(this._lexer.token)} is reserved and cannot be used for an enum value.`);return this.parseName()}parseInputObjectTypeDefinition(){const e=this._lexer.token,t=this.parseDescription();this.expectKeyword("input");const n=this.parseName(),r=this.parseConstDirectives(),i=this.parseInputFieldsDefinition();return this.node(e,{kind:a.Kind.INPUT_OBJECT_TYPE_DEFINITION,description:t,name:n,directives:r,fields:i})}parseInputFieldsDefinition(){return this.optionalMany(c.TokenKind.BRACE_L,this.parseInputValueDef,c.TokenKind.BRACE_R)}parseTypeSystemExtension(){const e=this._lexer.lookahead();if(e.kind===c.TokenKind.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)}parseSchemaExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");const t=this.parseConstDirectives(),n=this.optionalMany(c.TokenKind.BRACE_L,this.parseOperationTypeDefinition,c.TokenKind.BRACE_R);if(0===t.length&&0===n.length)throw this.unexpected();return this.node(e,{kind:a.Kind.SCHEMA_EXTENSION,directives:t,operationTypes:n})}parseScalarTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");const t=this.parseName(),n=this.parseConstDirectives();if(0===n.length)throw this.unexpected();return this.node(e,{kind:a.Kind.SCALAR_TYPE_EXTENSION,name:t,directives:n})}parseObjectTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");const t=this.parseName(),n=this.parseImplementsInterfaces(),r=this.parseConstDirectives(),i=this.parseFieldsDefinition();if(0===n.length&&0===r.length&&0===i.length)throw this.unexpected();return this.node(e,{kind:a.Kind.OBJECT_TYPE_EXTENSION,name:t,interfaces:n,directives:r,fields:i})}parseInterfaceTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");const t=this.parseName(),n=this.parseImplementsInterfaces(),r=this.parseConstDirectives(),i=this.parseFieldsDefinition();if(0===n.length&&0===r.length&&0===i.length)throw this.unexpected();return this.node(e,{kind:a.Kind.INTERFACE_TYPE_EXTENSION,name:t,interfaces:n,directives:r,fields:i})}parseUnionTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");const t=this.parseName(),n=this.parseConstDirectives(),r=this.parseUnionMemberTypes();if(0===n.length&&0===r.length)throw this.unexpected();return this.node(e,{kind:a.Kind.UNION_TYPE_EXTENSION,name:t,directives:n,types:r})}parseEnumTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");const t=this.parseName(),n=this.parseConstDirectives(),r=this.parseEnumValuesDefinition();if(0===n.length&&0===r.length)throw this.unexpected();return this.node(e,{kind:a.Kind.ENUM_TYPE_EXTENSION,name:t,directives:n,values:r})}parseInputObjectTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");const t=this.parseName(),n=this.parseConstDirectives(),r=this.parseInputFieldsDefinition();if(0===n.length&&0===r.length)throw this.unexpected();return this.node(e,{kind:a.Kind.INPUT_OBJECT_TYPE_EXTENSION,name:t,directives:n,fields:r})}parseDirectiveDefinition(){const e=this._lexer.token,t=this.parseDescription();this.expectKeyword("directive"),this.expectToken(c.TokenKind.AT);const n=this.parseName(),r=this.parseArgumentDefs(),i=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");const o=this.parseDirectiveLocations();return this.node(e,{kind:a.Kind.DIRECTIVE_DEFINITION,description:t,name:n,arguments:r,repeatable:i,locations:o})}parseDirectiveLocations(){return this.delimitedMany(c.TokenKind.PIPE,this.parseDirectiveLocation)}parseDirectiveLocation(){const e=this._lexer.token,t=this.parseName();if(Object.prototype.hasOwnProperty.call(o.DirectiveLocation,t.value))return t;throw this.unexpected(e)}node(e,t){return!0!==this._options.noLocation&&(t.loc=new i.Location(e,this._lexer.lastToken,this._lexer.source)),t}peek(e){return this._lexer.token.kind===e}expectToken(e){const t=this._lexer.token;if(t.kind===e)return this.advanceLexer(),t;throw(0,r.syntaxError)(this._lexer.source,t.start,`Expected ${d(e)}, found ${l(t)}.`)}expectOptionalToken(e){return this._lexer.token.kind===e&&(this.advanceLexer(),!0)}expectKeyword(e){const t=this._lexer.token;if(t.kind!==c.TokenKind.NAME||t.value!==e)throw(0,r.syntaxError)(this._lexer.source,t.start,`Expected "${e}", found ${l(t)}.`);this.advanceLexer()}expectOptionalKeyword(e){const t=this._lexer.token;return t.kind===c.TokenKind.NAME&&t.value===e&&(this.advanceLexer(),!0)}unexpected(e){const t=null!=e?e:this._lexer.token;return(0,r.syntaxError)(this._lexer.source,t.start,`Unexpected ${l(t)}.`)}any(e,t,n){this.expectToken(e);const r=[];for(;!this.expectOptionalToken(n);)r.push(t.call(this));return r}optionalMany(e,t,n){if(this.expectOptionalToken(e)){const e=[];do{e.push(t.call(this))}while(!this.expectOptionalToken(n));return e}return[]}many(e,t,n){this.expectToken(e);const r=[];do{r.push(t.call(this))}while(!this.expectOptionalToken(n));return r}delimitedMany(e,t){this.expectOptionalToken(e);const n=[];do{n.push(t.call(this))}while(this.expectOptionalToken(e));return n}advanceLexer(){const{maxTokens:e}=this._options,t=this._lexer.advance();if(void 0!==e&&t.kind!==c.TokenKind.EOF&&(++this._tokenCounter,this._tokenCounter>e))throw(0,r.syntaxError)(this._lexer.source,t.start,`Document contains more that ${e} tokens. Parsing aborted.`)}}function l(e){const t=e.value;return d(e.kind)+(null!=t?` "${t}"`:"")}function d(e){return(0,s.isPunctuatorTokenKind)(e)?`"${e}"`:e}t.Parser=p},9187:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isConstValueNode=function e(t){return o(t)&&(t.kind===r.Kind.LIST?t.values.some(e):t.kind===r.Kind.OBJECT?t.fields.some((t=>e(t.value))):t.kind!==r.Kind.VARIABLE)},t.isDefinitionNode=function(e){return i(e)||a(e)||u(e)},t.isExecutableDefinitionNode=i,t.isSelectionNode=function(e){return e.kind===r.Kind.FIELD||e.kind===r.Kind.FRAGMENT_SPREAD||e.kind===r.Kind.INLINE_FRAGMENT},t.isTypeDefinitionNode=s,t.isTypeExtensionNode=c,t.isTypeNode=function(e){return e.kind===r.Kind.NAMED_TYPE||e.kind===r.Kind.LIST_TYPE||e.kind===r.Kind.NON_NULL_TYPE},t.isTypeSystemDefinitionNode=a,t.isTypeSystemExtensionNode=u,t.isValueNode=o;var r=n(7030);function i(e){return e.kind===r.Kind.OPERATION_DEFINITION||e.kind===r.Kind.FRAGMENT_DEFINITION}function o(e){return e.kind===r.Kind.VARIABLE||e.kind===r.Kind.INT||e.kind===r.Kind.FLOAT||e.kind===r.Kind.STRING||e.kind===r.Kind.BOOLEAN||e.kind===r.Kind.NULL||e.kind===r.Kind.ENUM||e.kind===r.Kind.LIST||e.kind===r.Kind.OBJECT}function a(e){return e.kind===r.Kind.SCHEMA_DEFINITION||s(e)||e.kind===r.Kind.DIRECTIVE_DEFINITION}function s(e){return e.kind===r.Kind.SCALAR_TYPE_DEFINITION||e.kind===r.Kind.OBJECT_TYPE_DEFINITION||e.kind===r.Kind.INTERFACE_TYPE_DEFINITION||e.kind===r.Kind.UNION_TYPE_DEFINITION||e.kind===r.Kind.ENUM_TYPE_DEFINITION||e.kind===r.Kind.INPUT_OBJECT_TYPE_DEFINITION}function u(e){return e.kind===r.Kind.SCHEMA_EXTENSION||c(e)}function c(e){return e.kind===r.Kind.SCALAR_TYPE_EXTENSION||e.kind===r.Kind.OBJECT_TYPE_EXTENSION||e.kind===r.Kind.INTERFACE_TYPE_EXTENSION||e.kind===r.Kind.UNION_TYPE_EXTENSION||e.kind===r.Kind.ENUM_TYPE_EXTENSION||e.kind===r.Kind.INPUT_OBJECT_TYPE_EXTENSION}},825:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.printLocation=function(e){return i(e.source,(0,r.getLocation)(e.source,e.start))},t.printSourceLocation=i;var r=n(9530);function i(e,t){const n=e.locationOffset.column-1,r="".padStart(n)+e.body,i=t.line-1,a=e.locationOffset.line-1,s=t.line+a,u=1===t.line?n:0,c=t.column+u,p=`${e.name}:${s}:${c}\n`,l=r.split(/\r\n|[\n\r]/g),d=l[i];if(d.length>120){const e=Math.floor(c/80),t=c%80,n=[];for(let e=0;e<d.length;e+=80)n.push(d.slice(e,e+80));return p+o([[`${s} |`,n[0]],...n.slice(1,e+1).map((e=>["|",e])),["|","^".padStart(t)],["|",n[e+1]]])}return p+o([[s-1+" |",l[i-1]],[`${s} |`,d],["|","^".padStart(c)],[`${s+1} |`,l[i+1]]])}function o(e){const t=e.filter((([e,t])=>void 0!==t)),n=Math.max(...t.map((([e])=>e.length)));return t.map((([e,t])=>e.padStart(n)+(t?" "+t:""))).join("\n")}},7583:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.printString=function(e){return`"${e.replace(n,r)}"`};const n=/[\x00-\x1f\x22\x5c\x7f-\x9f]/g;function r(e){return i[e.charCodeAt(0)]}const i=["\\u0000","\\u0001","\\u0002","\\u0003","\\u0004","\\u0005","\\u0006","\\u0007","\\b","\\t","\\n","\\u000B","\\f","\\r","\\u000E","\\u000F","\\u0010","\\u0011","\\u0012","\\u0013","\\u0014","\\u0015","\\u0016","\\u0017","\\u0018","\\u0019","\\u001A","\\u001B","\\u001C","\\u001D","\\u001E","\\u001F","","",'\\"',"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\\\","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\u007F","\\u0080","\\u0081","\\u0082","\\u0083","\\u0084","\\u0085","\\u0086","\\u0087","\\u0088","\\u0089","\\u008A","\\u008B","\\u008C","\\u008D","\\u008E","\\u008F","\\u0090","\\u0091","\\u0092","\\u0093","\\u0094","\\u0095","\\u0096","\\u0097","\\u0098","\\u0099","\\u009A","\\u009B","\\u009C","\\u009D","\\u009E","\\u009F"]},585:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.print=function(e){return(0,o.visit)(e,a)};var r=n(9165),i=n(7583),o=n(9111);const a={Name:{leave:e=>e.value},Variable:{leave:e=>"$"+e.name},Document:{leave:e=>s(e.definitions,"\n\n")},OperationDefinition:{leave(e){const t=c("(",s(e.variableDefinitions,", "),")"),n=s([e.operation,s([e.name,t]),s(e.directives," ")]," ");return("query"===n?"":n+" ")+e.selectionSet}},VariableDefinition:{leave:({variable:e,type:t,defaultValue:n,directives:r})=>e+": "+t+c(" = ",n)+c(" ",s(r," "))},SelectionSet:{leave:({selections:e})=>u(e)},Field:{leave({alias:e,name:t,arguments:n,directives:r,selectionSet:i}){const o=c("",e,": ")+t;let a=o+c("(",s(n,", "),")");return a.length>80&&(a=o+c("(\n",p(s(n,"\n")),"\n)")),s([a,s(r," "),i]," ")}},Argument:{leave:({name:e,value:t})=>e+": "+t},FragmentSpread:{leave:({name:e,directives:t})=>"..."+e+c(" ",s(t," "))},InlineFragment:{leave:({typeCondition:e,directives:t,selectionSet:n})=>s(["...",c("on ",e),s(t," "),n]," ")},FragmentDefinition:{leave:({name:e,typeCondition:t,variableDefinitions:n,directives:r,selectionSet:i})=>`fragment ${e}${c("(",s(n,", "),")")} on ${t} ${c("",s(r," ")," ")}`+i},IntValue:{leave:({value:e})=>e},FloatValue:{leave:({value:e})=>e},StringValue:{leave:({value:e,block:t})=>t?(0,r.printBlockString)(e):(0,i.printString)(e)},BooleanValue:{leave:({value:e})=>e?"true":"false"},NullValue:{leave:()=>"null"},EnumValue:{leave:({value:e})=>e},ListValue:{leave:({values:e})=>"["+s(e,", ")+"]"},ObjectValue:{leave:({fields:e})=>"{"+s(e,", ")+"}"},ObjectField:{leave:({name:e,value:t})=>e+": "+t},Directive:{leave:({name:e,arguments:t})=>"@"+e+c("(",s(t,", "),")")},NamedType:{leave:({name:e})=>e},ListType:{leave:({type:e})=>"["+e+"]"},NonNullType:{leave:({type:e})=>e+"!"},SchemaDefinition:{leave:({description:e,directives:t,operationTypes:n})=>c("",e,"\n")+s(["schema",s(t," "),u(n)]," ")},OperationTypeDefinition:{leave:({operation:e,type:t})=>e+": "+t},ScalarTypeDefinition:{leave:({description:e,name:t,directives:n})=>c("",e,"\n")+s(["scalar",t,s(n," ")]," ")},ObjectTypeDefinition:{leave:({description:e,name:t,interfaces:n,directives:r,fields:i})=>c("",e,"\n")+s(["type",t,c("implements ",s(n," & ")),s(r," "),u(i)]," ")},FieldDefinition:{leave:({description:e,name:t,arguments:n,type:r,directives:i})=>c("",e,"\n")+t+(l(n)?c("(\n",p(s(n,"\n")),"\n)"):c("(",s(n,", "),")"))+": "+r+c(" ",s(i," "))},InputValueDefinition:{leave:({description:e,name:t,type:n,defaultValue:r,directives:i})=>c("",e,"\n")+s([t+": "+n,c("= ",r),s(i," ")]," ")},InterfaceTypeDefinition:{leave:({description:e,name:t,interfaces:n,directives:r,fields:i})=>c("",e,"\n")+s(["interface",t,c("implements ",s(n," & ")),s(r," "),u(i)]," ")},UnionTypeDefinition:{leave:({description:e,name:t,directives:n,types:r})=>c("",e,"\n")+s(["union",t,s(n," "),c("= ",s(r," | "))]," ")},EnumTypeDefinition:{leave:({description:e,name:t,directives:n,values:r})=>c("",e,"\n")+s(["enum",t,s(n," "),u(r)]," ")},EnumValueDefinition:{leave:({description:e,name:t,directives:n})=>c("",e,"\n")+s([t,s(n," ")]," ")},InputObjectTypeDefinition:{leave:({description:e,name:t,directives:n,fields:r})=>c("",e,"\n")+s(["input",t,s(n," "),u(r)]," ")},DirectiveDefinition:{leave:({description:e,name:t,arguments:n,repeatable:r,locations:i})=>c("",e,"\n")+"directive @"+t+(l(n)?c("(\n",p(s(n,"\n")),"\n)"):c("(",s(n,", "),")"))+(r?" repeatable":"")+" on "+s(i," | ")},SchemaExtension:{leave:({directives:e,operationTypes:t})=>s(["extend schema",s(e," "),u(t)]," ")},ScalarTypeExtension:{leave:({name:e,directives:t})=>s(["extend scalar",e,s(t," ")]," ")},ObjectTypeExtension:{leave:({name:e,interfaces:t,directives:n,fields:r})=>s(["extend type",e,c("implements ",s(t," & ")),s(n," "),u(r)]," ")},InterfaceTypeExtension:{leave:({name:e,interfaces:t,directives:n,fields:r})=>s(["extend interface",e,c("implements ",s(t," & ")),s(n," "),u(r)]," ")},UnionTypeExtension:{leave:({name:e,directives:t,types:n})=>s(["extend union",e,s(t," "),c("= ",s(n," | "))]," ")},EnumTypeExtension:{leave:({name:e,directives:t,values:n})=>s(["extend enum",e,s(t," "),u(n)]," ")},InputObjectTypeExtension:{leave:({name:e,directives:t,fields:n})=>s(["extend input",e,s(t," "),u(n)]," ")}};function s(e,t=""){var n;return null!==(n=null==e?void 0:e.filter((e=>e)).join(t))&&void 0!==n?n:""}function u(e){return c("{\n",p(s(e,"\n")),"\n}")}function c(e,t,n=""){return null!=t&&""!==t?e+t+n:""}function p(e){return c("  ",e.replace(/\n/g,"\n  "))}function l(e){var t;return null!==(t=null==e?void 0:e.some((e=>e.includes("\n"))))&&void 0!==t&&t}},6876:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Source=void 0,t.isSource=function(e){return(0,o.instanceOf)(e,a)};var r=n(3028),i=n(9657),o=n(9527);class a{constructor(e,t="GraphQL request",n={line:1,column:1}){"string"==typeof e||(0,r.devAssert)(!1,`Body must be a string. Received: ${(0,i.inspect)(e)}.`),this.body=e,this.name=t,this.locationOffset=n,this.locationOffset.line>0||(0,r.devAssert)(!1,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||(0,r.devAssert)(!1,"column in locationOffset is 1-indexed and must be positive.")}get[Symbol.toStringTag](){return"Source"}}t.Source=a},3038:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.TokenKind=void 0,t.TokenKind=n,function(e){e.SOF="<SOF>",e.EOF="<EOF>",e.BANG="!",e.DOLLAR="$",e.AMP="&",e.PAREN_L="(",e.PAREN_R=")",e.SPREAD="...",e.COLON=":",e.EQUALS="=",e.AT="@",e.BRACKET_L="[",e.BRACKET_R="]",e.BRACE_L="{",e.PIPE="|",e.BRACE_R="}",e.NAME="Name",e.INT="Int",e.FLOAT="Float",e.STRING="String",e.BLOCK_STRING="BlockString",e.COMMENT="Comment"}(n||(t.TokenKind=n={}))},9111:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BREAK=void 0,t.getEnterLeaveForKind=u,t.getVisitFn=function(e,t,n){const{enter:r,leave:i}=u(e,t);return n?i:r},t.visit=function(e,t,n=o.QueryDocumentKeys){const c=new Map;for(const e of Object.values(a.Kind))c.set(e,u(t,e));let p,l,d,f=Array.isArray(e),y=[e],m=-1,h=[],T=e;const b=[],v=[];do{m++;const e=m===y.length,a=e&&0!==h.length;if(e){if(l=0===v.length?void 0:b[b.length-1],T=d,d=v.pop(),a)if(f){T=T.slice();let e=0;for(const[t,n]of h){const r=t-e;null===n?(T.splice(r,1),e++):T[r]=n}}else{T=Object.defineProperties({},Object.getOwnPropertyDescriptors(T));for(const[e,t]of h)T[e]=t}m=p.index,y=p.keys,h=p.edits,f=p.inArray,p=p.prev}else if(d){if(l=f?m:y[m],T=d[l],null==T)continue;b.push(l)}let u;if(!Array.isArray(T)){var g,E;(0,o.isNode)(T)||(0,r.devAssert)(!1,`Invalid AST Node: ${(0,i.inspect)(T)}.`);const n=e?null===(g=c.get(T.kind))||void 0===g?void 0:g.leave:null===(E=c.get(T.kind))||void 0===E?void 0:E.enter;if(u=null==n?void 0:n.call(t,T,l,d,b,v),u===s)break;if(!1===u){if(!e){b.pop();continue}}else if(void 0!==u&&(h.push([l,u]),!e)){if(!(0,o.isNode)(u)){b.pop();continue}T=u}}var N;void 0===u&&a&&h.push([l,T]),e?b.pop():(p={inArray:f,index:m,keys:y,edits:h,prev:p},f=Array.isArray(T),y=f?T:null!==(N=n[T.kind])&&void 0!==N?N:[],m=-1,h=[],d&&v.push(d),d=T)}while(void 0!==p);return 0!==h.length?h[h.length-1][1]:e},t.visitInParallel=function(e){const t=new Array(e.length).fill(null),n=Object.create(null);for(const r of Object.values(a.Kind)){let i=!1;const o=new Array(e.length).fill(void 0),a=new Array(e.length).fill(void 0);for(let t=0;t<e.length;++t){const{enter:n,leave:s}=u(e[t],r);i||(i=null!=n||null!=s),o[t]=n,a[t]=s}if(!i)continue;const c={enter(...n){const r=n[0];for(let a=0;a<e.length;a++)if(null===t[a]){var i;const u=null===(i=o[a])||void 0===i?void 0:i.apply(e[a],n);if(!1===u)t[a]=r;else if(u===s)t[a]=s;else if(void 0!==u)return u}},leave(...n){const r=n[0];for(let o=0;o<e.length;o++)if(null===t[o]){var i;const r=null===(i=a[o])||void 0===i?void 0:i.apply(e[o],n);if(r===s)t[o]=s;else if(void 0!==r&&!1!==r)return r}else t[o]===r&&(t[o]=null)}};n[r]=c}return n};var r=n(3028),i=n(9657),o=n(6257),a=n(7030);const s=Object.freeze({});function u(e,t){const n=e[t];return"object"==typeof n?n:"function"==typeof n?{enter:n,leave:void 0}:{enter:e.enter,leave:e.leave}}t.BREAK=s},3506:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.assertEnumValueName=function(e){if("true"===e||"false"===e||"null"===e)throw new i.GraphQLError(`Enum values cannot be named: ${e}`);return a(e)},t.assertName=a;var r=n(3028),i=n(1702),o=n(3932);function a(e){if(null!=e||(0,r.devAssert)(!1,"Must provide name."),"string"==typeof e||(0,r.devAssert)(!1,"Expected name to be a string."),0===e.length)throw new i.GraphQLError("Expected name to be a non-empty string.");for(let t=1;t<e.length;++t)if(!(0,o.isNameContinue)(e.charCodeAt(t)))throw new i.GraphQLError(`Names must only contain [_a-zA-Z0-9] but "${e}" does not.`);if(!(0,o.isNameStart)(e.charCodeAt(0)))throw new i.GraphQLError(`Names must start with [_a-zA-Z] but "${e}" does not.`);return e}},3754:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GraphQLUnionType=t.GraphQLScalarType=t.GraphQLObjectType=t.GraphQLNonNull=t.GraphQLList=t.GraphQLInterfaceType=t.GraphQLInputObjectType=t.GraphQLEnumType=void 0,t.argsToArgsConfig=Y,t.assertAbstractType=function(e){if(!R(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL abstract type.`);return e},t.assertCompositeType=function(e){if(!P(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL composite type.`);return e},t.assertEnumType=function(e){if(!I(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL Enum type.`);return e},t.assertInputObjectType=function(e){if(!_(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL Input Object type.`);return e},t.assertInputType=function(e){if(!D(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL input type.`);return e},t.assertInterfaceType=function(e){if(!N(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL Interface type.`);return e},t.assertLeafType=function(e){if(!A(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL leaf type.`);return e},t.assertListType=function(e){if(!L(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL List type.`);return e},t.assertNamedType=function(e){if(!G(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL named type.`);return e},t.assertNonNullType=function(e){if(!S(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL Non-Null type.`);return e},t.assertNullableType=function(e){if(!x(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL nullable type.`);return e},t.assertObjectType=function(e){if(!E(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL Object type.`);return e},t.assertOutputType=function(e){if(!j(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL output type.`);return e},t.assertScalarType=function(e){if(!g(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL Scalar type.`);return e},t.assertType=function(e){if(!v(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL type.`);return e},t.assertUnionType=function(e){if(!O(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL Union type.`);return e},t.assertWrappingType=function(e){if(!F(e))throw new Error(`Expected ${(0,a.inspect)(e)} to be a GraphQL wrapping type.`);return e},t.defineArguments=U,t.getNamedType=function(e){if(e){let t=e;for(;F(t);)t=t.ofType;return t}},t.getNullableType=function(e){if(e)return S(e)?e.ofType:e},t.isAbstractType=R,t.isCompositeType=P,t.isEnumType=I,t.isInputObjectType=_,t.isInputType=D,t.isInterfaceType=N,t.isLeafType=A,t.isListType=L,t.isNamedType=G,t.isNonNullType=S,t.isNullableType=x,t.isObjectType=E,t.isOutputType=j,t.isRequiredArgument=function(e){return S(e.type)&&void 0===e.defaultValue},t.isRequiredInputField=function(e){return S(e.type)&&void 0===e.defaultValue},t.isScalarType=g,t.isType=v,t.isUnionType=O,t.isWrappingType=F,t.resolveObjMapThunk=C,t.resolveReadonlyArrayThunk=V;var r=n(3028),i=n(2832),o=n(6033),a=n(9657),s=n(9527),u=n(5569),c=n(4590),p=n(5785),l=n(3430),d=n(1709),f=n(3101),y=n(1702),m=n(7030),h=n(585),T=n(8805),b=n(3506);function v(e){return g(e)||E(e)||N(e)||O(e)||I(e)||_(e)||L(e)||S(e)}function g(e){return(0,s.instanceOf)(e,M)}function E(e){return(0,s.instanceOf)(e,K)}function N(e){return(0,s.instanceOf)(e,J)}function O(e){return(0,s.instanceOf)(e,X)}function I(e){return(0,s.instanceOf)(e,z)}function _(e){return(0,s.instanceOf)(e,Z)}function L(e){return(0,s.instanceOf)(e,w)}function S(e){return(0,s.instanceOf)(e,k)}function D(e){return g(e)||I(e)||_(e)||F(e)&&D(e.ofType)}function j(e){return g(e)||E(e)||N(e)||O(e)||I(e)||F(e)&&j(e.ofType)}function A(e){return g(e)||I(e)}function P(e){return E(e)||N(e)||O(e)}function R(e){return N(e)||O(e)}class w{constructor(e){v(e)||(0,r.devAssert)(!1,`Expected ${(0,a.inspect)(e)} to be a GraphQL type.`),this.ofType=e}get[Symbol.toStringTag](){return"GraphQLList"}toString(){return"["+String(this.ofType)+"]"}toJSON(){return this.toString()}}t.GraphQLList=w;class k{constructor(e){x(e)||(0,r.devAssert)(!1,`Expected ${(0,a.inspect)(e)} to be a GraphQL nullable type.`),this.ofType=e}get[Symbol.toStringTag](){return"GraphQLNonNull"}toString(){return String(this.ofType)+"!"}toJSON(){return this.toString()}}function F(e){return L(e)||S(e)}function x(e){return v(e)&&!S(e)}function G(e){return g(e)||E(e)||N(e)||O(e)||I(e)||_(e)}function V(e){return"function"==typeof e?e():e}function C(e){return"function"==typeof e?e():e}t.GraphQLNonNull=k;class M{constructor(e){var t,n,i,s;const u=null!==(t=e.parseValue)&&void 0!==t?t:o.identityFunc;this.name=(0,b.assertName)(e.name),this.description=e.description,this.specifiedByURL=e.specifiedByURL,this.serialize=null!==(n=e.serialize)&&void 0!==n?n:o.identityFunc,this.parseValue=u,this.parseLiteral=null!==(i=e.parseLiteral)&&void 0!==i?i:(e,t)=>u((0,T.valueFromASTUntyped)(e,t)),this.extensions=(0,f.toObjMap)(e.extensions),this.astNode=e.astNode,this.extensionASTNodes=null!==(s=e.extensionASTNodes)&&void 0!==s?s:[],null==e.specifiedByURL||"string"==typeof e.specifiedByURL||(0,r.devAssert)(!1,`${this.name} must provide "specifiedByURL" as a string, but got: ${(0,a.inspect)(e.specifiedByURL)}.`),null==e.serialize||"function"==typeof e.serialize||(0,r.devAssert)(!1,`${this.name} must provide "serialize" function. If this custom Scalar is also used as an input type, ensure "parseValue" and "parseLiteral" functions are also provided.`),e.parseLiteral&&("function"==typeof e.parseValue&&"function"==typeof e.parseLiteral||(0,r.devAssert)(!1,`${this.name} must provide both "parseValue" and "parseLiteral" functions.`))}get[Symbol.toStringTag](){return"GraphQLScalarType"}toConfig(){return{name:this.name,description:this.description,specifiedByURL:this.specifiedByURL,serialize:this.serialize,parseValue:this.parseValue,parseLiteral:this.parseLiteral,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}}t.GraphQLScalarType=M;class K{constructor(e){var t;this.name=(0,b.assertName)(e.name),this.description=e.description,this.isTypeOf=e.isTypeOf,this.extensions=(0,f.toObjMap)(e.extensions),this.astNode=e.astNode,this.extensionASTNodes=null!==(t=e.extensionASTNodes)&&void 0!==t?t:[],this._fields=()=>$(e),this._interfaces=()=>Q(e),null==e.isTypeOf||"function"==typeof e.isTypeOf||(0,r.devAssert)(!1,`${this.name} must provide "isTypeOf" as a function, but got: ${(0,a.inspect)(e.isTypeOf)}.`)}get[Symbol.toStringTag](){return"GraphQLObjectType"}getFields(){return"function"==typeof this._fields&&(this._fields=this._fields()),this._fields}getInterfaces(){return"function"==typeof this._interfaces&&(this._interfaces=this._interfaces()),this._interfaces}toConfig(){return{name:this.name,description:this.description,interfaces:this.getInterfaces(),fields:q(this.getFields()),isTypeOf:this.isTypeOf,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}}function Q(e){var t;const n=V(null!==(t=e.interfaces)&&void 0!==t?t:[]);return Array.isArray(n)||(0,r.devAssert)(!1,`${e.name} interfaces must be an Array or a function which returns an Array.`),n}function $(e){const t=C(e.fields);return B(t)||(0,r.devAssert)(!1,`${e.name} fields must be an object with field names as keys or a function which returns such an object.`),(0,l.mapValue)(t,((t,n)=>{var i;B(t)||(0,r.devAssert)(!1,`${e.name}.${n} field config must be an object.`),null==t.resolve||"function"==typeof t.resolve||(0,r.devAssert)(!1,`${e.name}.${n} field resolver must be a function if provided, but got: ${(0,a.inspect)(t.resolve)}.`);const o=null!==(i=t.args)&&void 0!==i?i:{};return B(o)||(0,r.devAssert)(!1,`${e.name}.${n} args must be an object with argument names as keys.`),{name:(0,b.assertName)(n),description:t.description,type:t.type,args:U(o),resolve:t.resolve,subscribe:t.subscribe,deprecationReason:t.deprecationReason,extensions:(0,f.toObjMap)(t.extensions),astNode:t.astNode}}))}function U(e){return Object.entries(e).map((([e,t])=>({name:(0,b.assertName)(e),description:t.description,type:t.type,defaultValue:t.defaultValue,deprecationReason:t.deprecationReason,extensions:(0,f.toObjMap)(t.extensions),astNode:t.astNode})))}function B(e){return(0,u.isObjectLike)(e)&&!Array.isArray(e)}function q(e){return(0,l.mapValue)(e,(e=>({description:e.description,type:e.type,args:Y(e.args),resolve:e.resolve,subscribe:e.subscribe,deprecationReason:e.deprecationReason,extensions:e.extensions,astNode:e.astNode})))}function Y(e){return(0,p.keyValMap)(e,(e=>e.name),(e=>({description:e.description,type:e.type,defaultValue:e.defaultValue,deprecationReason:e.deprecationReason,extensions:e.extensions,astNode:e.astNode})))}t.GraphQLObjectType=K;class J{constructor(e){var t;this.name=(0,b.assertName)(e.name),this.description=e.description,this.resolveType=e.resolveType,this.extensions=(0,f.toObjMap)(e.extensions),this.astNode=e.astNode,this.extensionASTNodes=null!==(t=e.extensionASTNodes)&&void 0!==t?t:[],this._fields=$.bind(void 0,e),this._interfaces=Q.bind(void 0,e),null==e.resolveType||"function"==typeof e.resolveType||(0,r.devAssert)(!1,`${this.name} must provide "resolveType" as a function, but got: ${(0,a.inspect)(e.resolveType)}.`)}get[Symbol.toStringTag](){return"GraphQLInterfaceType"}getFields(){return"function"==typeof this._fields&&(this._fields=this._fields()),this._fields}getInterfaces(){return"function"==typeof this._interfaces&&(this._interfaces=this._interfaces()),this._interfaces}toConfig(){return{name:this.name,description:this.description,interfaces:this.getInterfaces(),fields:q(this.getFields()),resolveType:this.resolveType,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}}t.GraphQLInterfaceType=J;class X{constructor(e){var t;this.name=(0,b.assertName)(e.name),this.description=e.description,this.resolveType=e.resolveType,this.extensions=(0,f.toObjMap)(e.extensions),this.astNode=e.astNode,this.extensionASTNodes=null!==(t=e.extensionASTNodes)&&void 0!==t?t:[],this._types=H.bind(void 0,e),null==e.resolveType||"function"==typeof e.resolveType||(0,r.devAssert)(!1,`${this.name} must provide "resolveType" as a function, but got: ${(0,a.inspect)(e.resolveType)}.`)}get[Symbol.toStringTag](){return"GraphQLUnionType"}getTypes(){return"function"==typeof this._types&&(this._types=this._types()),this._types}toConfig(){return{name:this.name,description:this.description,types:this.getTypes(),resolveType:this.resolveType,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}}function H(e){const t=V(e.types);return Array.isArray(t)||(0,r.devAssert)(!1,`Must provide Array of types or a function which returns such an array for Union ${e.name}.`),t}t.GraphQLUnionType=X;class z{constructor(e){var t,n,i;this.name=(0,b.assertName)(e.name),this.description=e.description,this.extensions=(0,f.toObjMap)(e.extensions),this.astNode=e.astNode,this.extensionASTNodes=null!==(t=e.extensionASTNodes)&&void 0!==t?t:[],this._values=(n=this.name,B(i=e.values)||(0,r.devAssert)(!1,`${n} values must be an object with value names as keys.`),Object.entries(i).map((([e,t])=>(B(t)||(0,r.devAssert)(!1,`${n}.${e} must refer to an object with a "value" key representing an internal value but got: ${(0,a.inspect)(t)}.`),{name:(0,b.assertEnumValueName)(e),description:t.description,value:void 0!==t.value?t.value:e,deprecationReason:t.deprecationReason,extensions:(0,f.toObjMap)(t.extensions),astNode:t.astNode})))),this._valueLookup=new Map(this._values.map((e=>[e.value,e]))),this._nameLookup=(0,c.keyMap)(this._values,(e=>e.name))}get[Symbol.toStringTag](){return"GraphQLEnumType"}getValues(){return this._values}getValue(e){return this._nameLookup[e]}serialize(e){const t=this._valueLookup.get(e);if(void 0===t)throw new y.GraphQLError(`Enum "${this.name}" cannot represent value: ${(0,a.inspect)(e)}`);return t.name}parseValue(e){if("string"!=typeof e){const t=(0,a.inspect)(e);throw new y.GraphQLError(`Enum "${this.name}" cannot represent non-string value: ${t}.`+W(this,t))}const t=this.getValue(e);if(null==t)throw new y.GraphQLError(`Value "${e}" does not exist in "${this.name}" enum.`+W(this,e));return t.value}parseLiteral(e,t){if(e.kind!==m.Kind.ENUM){const t=(0,h.print)(e);throw new y.GraphQLError(`Enum "${this.name}" cannot represent non-enum value: ${t}.`+W(this,t),{nodes:e})}const n=this.getValue(e.value);if(null==n){const t=(0,h.print)(e);throw new y.GraphQLError(`Value "${t}" does not exist in "${this.name}" enum.`+W(this,t),{nodes:e})}return n.value}toConfig(){const e=(0,p.keyValMap)(this.getValues(),(e=>e.name),(e=>({description:e.description,value:e.value,deprecationReason:e.deprecationReason,extensions:e.extensions,astNode:e.astNode})));return{name:this.name,description:this.description,values:e,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}}function W(e,t){const n=e.getValues().map((e=>e.name)),r=(0,d.suggestionList)(t,n);return(0,i.didYouMean)("the enum value",r)}t.GraphQLEnumType=z;class Z{constructor(e){var t;this.name=(0,b.assertName)(e.name),this.description=e.description,this.extensions=(0,f.toObjMap)(e.extensions),this.astNode=e.astNode,this.extensionASTNodes=null!==(t=e.extensionASTNodes)&&void 0!==t?t:[],this._fields=ee.bind(void 0,e)}get[Symbol.toStringTag](){return"GraphQLInputObjectType"}getFields(){return"function"==typeof this._fields&&(this._fields=this._fields()),this._fields}toConfig(){const e=(0,l.mapValue)(this.getFields(),(e=>({description:e.description,type:e.type,defaultValue:e.defaultValue,deprecationReason:e.deprecationReason,extensions:e.extensions,astNode:e.astNode})));return{name:this.name,description:this.description,fields:e,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}}function ee(e){const t=C(e.fields);return B(t)||(0,r.devAssert)(!1,`${e.name} fields must be an object with field names as keys or a function which returns such an object.`),(0,l.mapValue)(t,((t,n)=>(!("resolve"in t)||(0,r.devAssert)(!1,`${e.name}.${n} field has a resolve property, but Input Types cannot define resolvers.`),{name:(0,b.assertName)(n),description:t.description,type:t.type,defaultValue:t.defaultValue,deprecationReason:t.deprecationReason,extensions:(0,f.toObjMap)(t.extensions),astNode:t.astNode})))}t.GraphQLInputObjectType=Z},8685:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GraphQLSpecifiedByDirective=t.GraphQLSkipDirective=t.GraphQLIncludeDirective=t.GraphQLDirective=t.GraphQLDeprecatedDirective=t.DEFAULT_DEPRECATION_REASON=void 0,t.assertDirective=function(e){if(!d(e))throw new Error(`Expected ${(0,i.inspect)(e)} to be a GraphQL directive.`);return e},t.isDirective=d,t.isSpecifiedDirective=function(e){return v.some((({name:t})=>t===e.name))},t.specifiedDirectives=void 0;var r=n(3028),i=n(9657),o=n(9527),a=n(5569),s=n(3101),u=n(5919),c=n(3506),p=n(3754),l=n(1062);function d(e){return(0,o.instanceOf)(e,f)}class f{constructor(e){var t,n;this.name=(0,c.assertName)(e.name),this.description=e.description,this.locations=e.locations,this.isRepeatable=null!==(t=e.isRepeatable)&&void 0!==t&&t,this.extensions=(0,s.toObjMap)(e.extensions),this.astNode=e.astNode,Array.isArray(e.locations)||(0,r.devAssert)(!1,`@${e.name} locations must be an Array.`);const i=null!==(n=e.args)&&void 0!==n?n:{};(0,a.isObjectLike)(i)&&!Array.isArray(i)||(0,r.devAssert)(!1,`@${e.name} args must be an object with argument names as keys.`),this.args=(0,p.defineArguments)(i)}get[Symbol.toStringTag](){return"GraphQLDirective"}toConfig(){return{name:this.name,description:this.description,locations:this.locations,args:(0,p.argsToArgsConfig)(this.args),isRepeatable:this.isRepeatable,extensions:this.extensions,astNode:this.astNode}}toString(){return"@"+this.name}toJSON(){return this.toString()}}t.GraphQLDirective=f;const y=new f({name:"include",description:"Directs the executor to include this field or fragment only when the `if` argument is true.",locations:[u.DirectiveLocation.FIELD,u.DirectiveLocation.FRAGMENT_SPREAD,u.DirectiveLocation.INLINE_FRAGMENT],args:{if:{type:new p.GraphQLNonNull(l.GraphQLBoolean),description:"Included when true."}}});t.GraphQLIncludeDirective=y;const m=new f({name:"skip",description:"Directs the executor to skip this field or fragment when the `if` argument is true.",locations:[u.DirectiveLocation.FIELD,u.DirectiveLocation.FRAGMENT_SPREAD,u.DirectiveLocation.INLINE_FRAGMENT],args:{if:{type:new p.GraphQLNonNull(l.GraphQLBoolean),description:"Skipped when true."}}});t.GraphQLSkipDirective=m;const h="No longer supported";t.DEFAULT_DEPRECATION_REASON=h;const T=new f({name:"deprecated",description:"Marks an element of a GraphQL schema as no longer supported.",locations:[u.DirectiveLocation.FIELD_DEFINITION,u.DirectiveLocation.ARGUMENT_DEFINITION,u.DirectiveLocation.INPUT_FIELD_DEFINITION,u.DirectiveLocation.ENUM_VALUE],args:{reason:{type:l.GraphQLString,description:"Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).",defaultValue:h}}});t.GraphQLDeprecatedDirective=T;const b=new f({name:"specifiedBy",description:"Exposes a URL that specifies the behavior of this scalar.",locations:[u.DirectiveLocation.SCALAR],args:{url:{type:new p.GraphQLNonNull(l.GraphQLString),description:"The URL that specifies the behavior of this scalar."}}});t.GraphQLSpecifiedByDirective=b;const v=Object.freeze([y,m,T,b]);t.specifiedDirectives=v},219:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DEFAULT_DEPRECATION_REASON",{enumerable:!0,get:function(){return o.DEFAULT_DEPRECATION_REASON}}),Object.defineProperty(t,"GRAPHQL_MAX_INT",{enumerable:!0,get:function(){return a.GRAPHQL_MAX_INT}}),Object.defineProperty(t,"GRAPHQL_MIN_INT",{enumerable:!0,get:function(){return a.GRAPHQL_MIN_INT}}),Object.defineProperty(t,"GraphQLBoolean",{enumerable:!0,get:function(){return a.GraphQLBoolean}}),Object.defineProperty(t,"GraphQLDeprecatedDirective",{enumerable:!0,get:function(){return o.GraphQLDeprecatedDirective}}),Object.defineProperty(t,"GraphQLDirective",{enumerable:!0,get:function(){return o.GraphQLDirective}}),Object.defineProperty(t,"GraphQLEnumType",{enumerable:!0,get:function(){return i.GraphQLEnumType}}),Object.defineProperty(t,"GraphQLFloat",{enumerable:!0,get:function(){return a.GraphQLFloat}}),Object.defineProperty(t,"GraphQLID",{enumerable:!0,get:function(){return a.GraphQLID}}),Object.defineProperty(t,"GraphQLIncludeDirective",{enumerable:!0,get:function(){return o.GraphQLIncludeDirective}}),Object.defineProperty(t,"GraphQLInputObjectType",{enumerable:!0,get:function(){return i.GraphQLInputObjectType}}),Object.defineProperty(t,"GraphQLInt",{enumerable:!0,get:function(){return a.GraphQLInt}}),Object.defineProperty(t,"GraphQLInterfaceType",{enumerable:!0,get:function(){return i.GraphQLInterfaceType}}),Object.defineProperty(t,"GraphQLList",{enumerable:!0,get:function(){return i.GraphQLList}}),Object.defineProperty(t,"GraphQLNonNull",{enumerable:!0,get:function(){return i.GraphQLNonNull}}),Object.defineProperty(t,"GraphQLObjectType",{enumerable:!0,get:function(){return i.GraphQLObjectType}}),Object.defineProperty(t,"GraphQLScalarType",{enumerable:!0,get:function(){return i.GraphQLScalarType}}),Object.defineProperty(t,"GraphQLSchema",{enumerable:!0,get:function(){return r.GraphQLSchema}}),Object.defineProperty(t,"GraphQLSkipDirective",{enumerable:!0,get:function(){return o.GraphQLSkipDirective}}),Object.defineProperty(t,"GraphQLSpecifiedByDirective",{enumerable:!0,get:function(){return o.GraphQLSpecifiedByDirective}}),Object.defineProperty(t,"GraphQLString",{enumerable:!0,get:function(){return a.GraphQLString}}),Object.defineProperty(t,"GraphQLUnionType",{enumerable:!0,get:function(){return i.GraphQLUnionType}}),Object.defineProperty(t,"SchemaMetaFieldDef",{enumerable:!0,get:function(){return s.SchemaMetaFieldDef}}),Object.defineProperty(t,"TypeKind",{enumerable:!0,get:function(){return s.TypeKind}}),Object.defineProperty(t,"TypeMetaFieldDef",{enumerable:!0,get:function(){return s.TypeMetaFieldDef}}),Object.defineProperty(t,"TypeNameMetaFieldDef",{enumerable:!0,get:function(){return s.TypeNameMetaFieldDef}}),Object.defineProperty(t,"__Directive",{enumerable:!0,get:function(){return s.__Directive}}),Object.defineProperty(t,"__DirectiveLocation",{enumerable:!0,get:function(){return s.__DirectiveLocation}}),Object.defineProperty(t,"__EnumValue",{enumerable:!0,get:function(){return s.__EnumValue}}),Object.defineProperty(t,"__Field",{enumerable:!0,get:function(){return s.__Field}}),Object.defineProperty(t,"__InputValue",{enumerable:!0,get:function(){return s.__InputValue}}),Object.defineProperty(t,"__Schema",{enumerable:!0,get:function(){return s.__Schema}}),Object.defineProperty(t,"__Type",{enumerable:!0,get:function(){return s.__Type}}),Object.defineProperty(t,"__TypeKind",{enumerable:!0,get:function(){return s.__TypeKind}}),Object.defineProperty(t,"assertAbstractType",{enumerable:!0,get:function(){return i.assertAbstractType}}),Object.defineProperty(t,"assertCompositeType",{enumerable:!0,get:function(){return i.assertCompositeType}}),Object.defineProperty(t,"assertDirective",{enumerable:!0,get:function(){return o.assertDirective}}),Object.defineProperty(t,"assertEnumType",{enumerable:!0,get:function(){return i.assertEnumType}}),Object.defineProperty(t,"assertEnumValueName",{enumerable:!0,get:function(){return c.assertEnumValueName}}),Object.defineProperty(t,"assertInputObjectType",{enumerable:!0,get:function(){return i.assertInputObjectType}}),Object.defineProperty(t,"assertInputType",{enumerable:!0,get:function(){return i.assertInputType}}),Object.defineProperty(t,"assertInterfaceType",{enumerable:!0,get:function(){return i.assertInterfaceType}}),Object.defineProperty(t,"assertLeafType",{enumerable:!0,get:function(){return i.assertLeafType}}),Object.defineProperty(t,"assertListType",{enumerable:!0,get:function(){return i.assertListType}}),Object.defineProperty(t,"assertName",{enumerable:!0,get:function(){return c.assertName}}),Object.defineProperty(t,"assertNamedType",{enumerable:!0,get:function(){return i.assertNamedType}}),Object.defineProperty(t,"assertNonNullType",{enumerable:!0,get:function(){return i.assertNonNullType}}),Object.defineProperty(t,"assertNullableType",{enumerable:!0,get:function(){return i.assertNullableType}}),Object.defineProperty(t,"assertObjectType",{enumerable:!0,get:function(){return i.assertObjectType}}),Object.defineProperty(t,"assertOutputType",{enumerable:!0,get:function(){return i.assertOutputType}}),Object.defineProperty(t,"assertScalarType",{enumerable:!0,get:function(){return i.assertScalarType}}),Object.defineProperty(t,"assertSchema",{enumerable:!0,get:function(){return r.assertSchema}}),Object.defineProperty(t,"assertType",{enumerable:!0,get:function(){return i.assertType}}),Object.defineProperty(t,"assertUnionType",{enumerable:!0,get:function(){return i.assertUnionType}}),Object.defineProperty(t,"assertValidSchema",{enumerable:!0,get:function(){return u.assertValidSchema}}),Object.defineProperty(t,"assertWrappingType",{enumerable:!0,get:function(){return i.assertWrappingType}}),Object.defineProperty(t,"getNamedType",{enumerable:!0,get:function(){return i.getNamedType}}),Object.defineProperty(t,"getNullableType",{enumerable:!0,get:function(){return i.getNullableType}}),Object.defineProperty(t,"introspectionTypes",{enumerable:!0,get:function(){return s.introspectionTypes}}),Object.defineProperty(t,"isAbstractType",{enumerable:!0,get:function(){return i.isAbstractType}}),Object.defineProperty(t,"isCompositeType",{enumerable:!0,get:function(){return i.isCompositeType}}),Object.defineProperty(t,"isDirective",{enumerable:!0,get:function(){return o.isDirective}}),Object.defineProperty(t,"isEnumType",{enumerable:!0,get:function(){return i.isEnumType}}),Object.defineProperty(t,"isInputObjectType",{enumerable:!0,get:function(){return i.isInputObjectType}}),Object.defineProperty(t,"isInputType",{enumerable:!0,get:function(){return i.isInputType}}),Object.defineProperty(t,"isInterfaceType",{enumerable:!0,get:function(){return i.isInterfaceType}}),Object.defineProperty(t,"isIntrospectionType",{enumerable:!0,get:function(){return s.isIntrospectionType}}),Object.defineProperty(t,"isLeafType",{enumerable:!0,get:function(){return i.isLeafType}}),Object.defineProperty(t,"isListType",{enumerable:!0,get:function(){return i.isListType}}),Object.defineProperty(t,"isNamedType",{enumerable:!0,get:function(){return i.isNamedType}}),Object.defineProperty(t,"isNonNullType",{enumerable:!0,get:function(){return i.isNonNullType}}),Object.defineProperty(t,"isNullableType",{enumerable:!0,get:function(){return i.isNullableType}}),Object.defineProperty(t,"isObjectType",{enumerable:!0,get:function(){return i.isObjectType}}),Object.defineProperty(t,"isOutputType",{enumerable:!0,get:function(){return i.isOutputType}}),Object.defineProperty(t,"isRequiredArgument",{enumerable:!0,get:function(){return i.isRequiredArgument}}),Object.defineProperty(t,"isRequiredInputField",{enumerable:!0,get:function(){return i.isRequiredInputField}}),Object.defineProperty(t,"isScalarType",{enumerable:!0,get:function(){return i.isScalarType}}),Object.defineProperty(t,"isSchema",{enumerable:!0,get:function(){return r.isSchema}}),Object.defineProperty(t,"isSpecifiedDirective",{enumerable:!0,get:function(){return o.isSpecifiedDirective}}),Object.defineProperty(t,"isSpecifiedScalarType",{enumerable:!0,get:function(){return a.isSpecifiedScalarType}}),Object.defineProperty(t,"isType",{enumerable:!0,get:function(){return i.isType}}),Object.defineProperty(t,"isUnionType",{enumerable:!0,get:function(){return i.isUnionType}}),Object.defineProperty(t,"isWrappingType",{enumerable:!0,get:function(){return i.isWrappingType}}),Object.defineProperty(t,"resolveObjMapThunk",{enumerable:!0,get:function(){return i.resolveObjMapThunk}}),Object.defineProperty(t,"resolveReadonlyArrayThunk",{enumerable:!0,get:function(){return i.resolveReadonlyArrayThunk}}),Object.defineProperty(t,"specifiedDirectives",{enumerable:!0,get:function(){return o.specifiedDirectives}}),Object.defineProperty(t,"specifiedScalarTypes",{enumerable:!0,get:function(){return a.specifiedScalarTypes}}),Object.defineProperty(t,"validateSchema",{enumerable:!0,get:function(){return u.validateSchema}});var r=n(4648),i=n(3754),o=n(8685),a=n(1062),s=n(8364),u=n(9873),c=n(3506)},8364:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.introspectionTypes=t.__TypeKind=t.__Type=t.__Schema=t.__InputValue=t.__Field=t.__EnumValue=t.__DirectiveLocation=t.__Directive=t.TypeNameMetaFieldDef=t.TypeMetaFieldDef=t.TypeKind=t.SchemaMetaFieldDef=void 0,t.isIntrospectionType=function(e){return N.some((({name:t})=>e.name===t))};var r=n(9657),i=n(1321),o=n(5919),a=n(585),s=n(8096),u=n(3754),c=n(1062);const p=new u.GraphQLObjectType({name:"__Schema",description:"A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.",fields:()=>({description:{type:c.GraphQLString,resolve:e=>e.description},types:{description:"A list of all types supported by this server.",type:new u.GraphQLNonNull(new u.GraphQLList(new u.GraphQLNonNull(f))),resolve:e=>Object.values(e.getTypeMap())},queryType:{description:"The type that query operations will be rooted at.",type:new u.GraphQLNonNull(f),resolve:e=>e.getQueryType()},mutationType:{description:"If this server supports mutation, the type that mutation operations will be rooted at.",type:f,resolve:e=>e.getMutationType()},subscriptionType:{description:"If this server support subscription, the type that subscription operations will be rooted at.",type:f,resolve:e=>e.getSubscriptionType()},directives:{description:"A list of all directives supported by this server.",type:new u.GraphQLNonNull(new u.GraphQLList(new u.GraphQLNonNull(l))),resolve:e=>e.getDirectives()}})});t.__Schema=p;const l=new u.GraphQLObjectType({name:"__Directive",description:"A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.",fields:()=>({name:{type:new u.GraphQLNonNull(c.GraphQLString),resolve:e=>e.name},description:{type:c.GraphQLString,resolve:e=>e.description},isRepeatable:{type:new u.GraphQLNonNull(c.GraphQLBoolean),resolve:e=>e.isRepeatable},locations:{type:new u.GraphQLNonNull(new u.GraphQLList(new u.GraphQLNonNull(d))),resolve:e=>e.locations},args:{type:new u.GraphQLNonNull(new u.GraphQLList(new u.GraphQLNonNull(m))),args:{includeDeprecated:{type:c.GraphQLBoolean,defaultValue:!1}},resolve:(e,{includeDeprecated:t})=>t?e.args:e.args.filter((e=>null==e.deprecationReason))}})});t.__Directive=l;const d=new u.GraphQLEnumType({name:"__DirectiveLocation",description:"A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.",values:{QUERY:{value:o.DirectiveLocation.QUERY,description:"Location adjacent to a query operation."},MUTATION:{value:o.DirectiveLocation.MUTATION,description:"Location adjacent to a mutation operation."},SUBSCRIPTION:{value:o.DirectiveLocation.SUBSCRIPTION,description:"Location adjacent to a subscription operation."},FIELD:{value:o.DirectiveLocation.FIELD,description:"Location adjacent to a field."},FRAGMENT_DEFINITION:{value:o.DirectiveLocation.FRAGMENT_DEFINITION,description:"Location adjacent to a fragment definition."},FRAGMENT_SPREAD:{value:o.DirectiveLocation.FRAGMENT_SPREAD,description:"Location adjacent to a fragment spread."},INLINE_FRAGMENT:{value:o.DirectiveLocation.INLINE_FRAGMENT,description:"Location adjacent to an inline fragment."},VARIABLE_DEFINITION:{value:o.DirectiveLocation.VARIABLE_DEFINITION,description:"Location adjacent to a variable definition."},SCHEMA:{value:o.DirectiveLocation.SCHEMA,description:"Location adjacent to a schema definition."},SCALAR:{value:o.DirectiveLocation.SCALAR,description:"Location adjacent to a scalar definition."},OBJECT:{value:o.DirectiveLocation.OBJECT,description:"Location adjacent to an object type definition."},FIELD_DEFINITION:{value:o.DirectiveLocation.FIELD_DEFINITION,description:"Location adjacent to a field definition."},ARGUMENT_DEFINITION:{value:o.DirectiveLocation.ARGUMENT_DEFINITION,description:"Location adjacent to an argument definition."},INTERFACE:{value:o.DirectiveLocation.INTERFACE,description:"Location adjacent to an interface definition."},UNION:{value:o.DirectiveLocation.UNION,description:"Location adjacent to a union definition."},ENUM:{value:o.DirectiveLocation.ENUM,description:"Location adjacent to an enum definition."},ENUM_VALUE:{value:o.DirectiveLocation.ENUM_VALUE,description:"Location adjacent to an enum value definition."},INPUT_OBJECT:{value:o.DirectiveLocation.INPUT_OBJECT,description:"Location adjacent to an input object type definition."},INPUT_FIELD_DEFINITION:{value:o.DirectiveLocation.INPUT_FIELD_DEFINITION,description:"Location adjacent to an input object field definition."}}});t.__DirectiveLocation=d;const f=new u.GraphQLObjectType({name:"__Type",description:"The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.",fields:()=>({kind:{type:new u.GraphQLNonNull(b),resolve:e=>(0,u.isScalarType)(e)?T.SCALAR:(0,u.isObjectType)(e)?T.OBJECT:(0,u.isInterfaceType)(e)?T.INTERFACE:(0,u.isUnionType)(e)?T.UNION:(0,u.isEnumType)(e)?T.ENUM:(0,u.isInputObjectType)(e)?T.INPUT_OBJECT:(0,u.isListType)(e)?T.LIST:(0,u.isNonNullType)(e)?T.NON_NULL:void(0,i.invariant)(!1,`Unexpected type: "${(0,r.inspect)(e)}".`)},name:{type:c.GraphQLString,resolve:e=>"name"in e?e.name:void 0},description:{type:c.GraphQLString,resolve:e=>"description"in e?e.description:void 0},specifiedByURL:{type:c.GraphQLString,resolve:e=>"specifiedByURL"in e?e.specifiedByURL:void 0},fields:{type:new u.GraphQLList(new u.GraphQLNonNull(y)),args:{includeDeprecated:{type:c.GraphQLBoolean,defaultValue:!1}},resolve(e,{includeDeprecated:t}){if((0,u.isObjectType)(e)||(0,u.isInterfaceType)(e)){const n=Object.values(e.getFields());return t?n:n.filter((e=>null==e.deprecationReason))}}},interfaces:{type:new u.GraphQLList(new u.GraphQLNonNull(f)),resolve(e){if((0,u.isObjectType)(e)||(0,u.isInterfaceType)(e))return e.getInterfaces()}},possibleTypes:{type:new u.GraphQLList(new u.GraphQLNonNull(f)),resolve(e,t,n,{schema:r}){if((0,u.isAbstractType)(e))return r.getPossibleTypes(e)}},enumValues:{type:new u.GraphQLList(new u.GraphQLNonNull(h)),args:{includeDeprecated:{type:c.GraphQLBoolean,defaultValue:!1}},resolve(e,{includeDeprecated:t}){if((0,u.isEnumType)(e)){const n=e.getValues();return t?n:n.filter((e=>null==e.deprecationReason))}}},inputFields:{type:new u.GraphQLList(new u.GraphQLNonNull(m)),args:{includeDeprecated:{type:c.GraphQLBoolean,defaultValue:!1}},resolve(e,{includeDeprecated:t}){if((0,u.isInputObjectType)(e)){const n=Object.values(e.getFields());return t?n:n.filter((e=>null==e.deprecationReason))}}},ofType:{type:f,resolve:e=>"ofType"in e?e.ofType:void 0}})});t.__Type=f;const y=new u.GraphQLObjectType({name:"__Field",description:"Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.",fields:()=>({name:{type:new u.GraphQLNonNull(c.GraphQLString),resolve:e=>e.name},description:{type:c.GraphQLString,resolve:e=>e.description},args:{type:new u.GraphQLNonNull(new u.GraphQLList(new u.GraphQLNonNull(m))),args:{includeDeprecated:{type:c.GraphQLBoolean,defaultValue:!1}},resolve:(e,{includeDeprecated:t})=>t?e.args:e.args.filter((e=>null==e.deprecationReason))},type:{type:new u.GraphQLNonNull(f),resolve:e=>e.type},isDeprecated:{type:new u.GraphQLNonNull(c.GraphQLBoolean),resolve:e=>null!=e.deprecationReason},deprecationReason:{type:c.GraphQLString,resolve:e=>e.deprecationReason}})});t.__Field=y;const m=new u.GraphQLObjectType({name:"__InputValue",description:"Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.",fields:()=>({name:{type:new u.GraphQLNonNull(c.GraphQLString),resolve:e=>e.name},description:{type:c.GraphQLString,resolve:e=>e.description},type:{type:new u.GraphQLNonNull(f),resolve:e=>e.type},defaultValue:{type:c.GraphQLString,description:"A GraphQL-formatted string representing the default value for this input value.",resolve(e){const{type:t,defaultValue:n}=e,r=(0,s.astFromValue)(n,t);return r?(0,a.print)(r):null}},isDeprecated:{type:new u.GraphQLNonNull(c.GraphQLBoolean),resolve:e=>null!=e.deprecationReason},deprecationReason:{type:c.GraphQLString,resolve:e=>e.deprecationReason}})});t.__InputValue=m;const h=new u.GraphQLObjectType({name:"__EnumValue",description:"One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.",fields:()=>({name:{type:new u.GraphQLNonNull(c.GraphQLString),resolve:e=>e.name},description:{type:c.GraphQLString,resolve:e=>e.description},isDeprecated:{type:new u.GraphQLNonNull(c.GraphQLBoolean),resolve:e=>null!=e.deprecationReason},deprecationReason:{type:c.GraphQLString,resolve:e=>e.deprecationReason}})});var T;t.__EnumValue=h,t.TypeKind=T,function(e){e.SCALAR="SCALAR",e.OBJECT="OBJECT",e.INTERFACE="INTERFACE",e.UNION="UNION",e.ENUM="ENUM",e.INPUT_OBJECT="INPUT_OBJECT",e.LIST="LIST",e.NON_NULL="NON_NULL"}(T||(t.TypeKind=T={}));const b=new u.GraphQLEnumType({name:"__TypeKind",description:"An enum describing what kind of type a given `__Type` is.",values:{SCALAR:{value:T.SCALAR,description:"Indicates this type is a scalar."},OBJECT:{value:T.OBJECT,description:"Indicates this type is an object. `fields` and `interfaces` are valid fields."},INTERFACE:{value:T.INTERFACE,description:"Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields."},UNION:{value:T.UNION,description:"Indicates this type is a union. `possibleTypes` is a valid field."},ENUM:{value:T.ENUM,description:"Indicates this type is an enum. `enumValues` is a valid field."},INPUT_OBJECT:{value:T.INPUT_OBJECT,description:"Indicates this type is an input object. `inputFields` is a valid field."},LIST:{value:T.LIST,description:"Indicates this type is a list. `ofType` is a valid field."},NON_NULL:{value:T.NON_NULL,description:"Indicates this type is a non-null. `ofType` is a valid field."}}});t.__TypeKind=b;const v={name:"__schema",type:new u.GraphQLNonNull(p),description:"Access the current type schema of this server.",args:[],resolve:(e,t,n,{schema:r})=>r,deprecationReason:void 0,extensions:Object.create(null),astNode:void 0};t.SchemaMetaFieldDef=v;const g={name:"__type",type:f,description:"Request the type information of a single type.",args:[{name:"name",description:void 0,type:new u.GraphQLNonNull(c.GraphQLString),defaultValue:void 0,deprecationReason:void 0,extensions:Object.create(null),astNode:void 0}],resolve:(e,{name:t},n,{schema:r})=>r.getType(t),deprecationReason:void 0,extensions:Object.create(null),astNode:void 0};t.TypeMetaFieldDef=g;const E={name:"__typename",type:new u.GraphQLNonNull(c.GraphQLString),description:"The name of the current Object type at runtime.",args:[],resolve:(e,t,n,{parentType:r})=>r.name,deprecationReason:void 0,extensions:Object.create(null),astNode:void 0};t.TypeNameMetaFieldDef=E;const N=Object.freeze([p,l,d,f,y,m,h,b]);t.introspectionTypes=N},1062:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GraphQLString=t.GraphQLInt=t.GraphQLID=t.GraphQLFloat=t.GraphQLBoolean=t.GRAPHQL_MIN_INT=t.GRAPHQL_MAX_INT=void 0,t.isSpecifiedScalarType=function(e){return h.some((({name:t})=>e.name===t))},t.specifiedScalarTypes=void 0;var r=n(9657),i=n(5569),o=n(1702),a=n(7030),s=n(585),u=n(3754);const c=2147483647;t.GRAPHQL_MAX_INT=c;const p=-2147483648;t.GRAPHQL_MIN_INT=p;const l=new u.GraphQLScalarType({name:"Int",description:"The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.",serialize(e){const t=T(e);if("boolean"==typeof t)return t?1:0;let n=t;if("string"==typeof t&&""!==t&&(n=Number(t)),"number"!=typeof n||!Number.isInteger(n))throw new o.GraphQLError(`Int cannot represent non-integer value: ${(0,r.inspect)(t)}`);if(n>c||n<p)throw new o.GraphQLError("Int cannot represent non 32-bit signed integer value: "+(0,r.inspect)(t));return n},parseValue(e){if("number"!=typeof e||!Number.isInteger(e))throw new o.GraphQLError(`Int cannot represent non-integer value: ${(0,r.inspect)(e)}`);if(e>c||e<p)throw new o.GraphQLError(`Int cannot represent non 32-bit signed integer value: ${e}`);return e},parseLiteral(e){if(e.kind!==a.Kind.INT)throw new o.GraphQLError(`Int cannot represent non-integer value: ${(0,s.print)(e)}`,{nodes:e});const t=parseInt(e.value,10);if(t>c||t<p)throw new o.GraphQLError(`Int cannot represent non 32-bit signed integer value: ${e.value}`,{nodes:e});return t}});t.GraphQLInt=l;const d=new u.GraphQLScalarType({name:"Float",description:"The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).",serialize(e){const t=T(e);if("boolean"==typeof t)return t?1:0;let n=t;if("string"==typeof t&&""!==t&&(n=Number(t)),"number"!=typeof n||!Number.isFinite(n))throw new o.GraphQLError(`Float cannot represent non numeric value: ${(0,r.inspect)(t)}`);return n},parseValue(e){if("number"!=typeof e||!Number.isFinite(e))throw new o.GraphQLError(`Float cannot represent non numeric value: ${(0,r.inspect)(e)}`);return e},parseLiteral(e){if(e.kind!==a.Kind.FLOAT&&e.kind!==a.Kind.INT)throw new o.GraphQLError(`Float cannot represent non numeric value: ${(0,s.print)(e)}`,e);return parseFloat(e.value)}});t.GraphQLFloat=d;const f=new u.GraphQLScalarType({name:"String",description:"The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.",serialize(e){const t=T(e);if("string"==typeof t)return t;if("boolean"==typeof t)return t?"true":"false";if("number"==typeof t&&Number.isFinite(t))return t.toString();throw new o.GraphQLError(`String cannot represent value: ${(0,r.inspect)(e)}`)},parseValue(e){if("string"!=typeof e)throw new o.GraphQLError(`String cannot represent a non string value: ${(0,r.inspect)(e)}`);return e},parseLiteral(e){if(e.kind!==a.Kind.STRING)throw new o.GraphQLError(`String cannot represent a non string value: ${(0,s.print)(e)}`,{nodes:e});return e.value}});t.GraphQLString=f;const y=new u.GraphQLScalarType({name:"Boolean",description:"The `Boolean` scalar type represents `true` or `false`.",serialize(e){const t=T(e);if("boolean"==typeof t)return t;if(Number.isFinite(t))return 0!==t;throw new o.GraphQLError(`Boolean cannot represent a non boolean value: ${(0,r.inspect)(t)}`)},parseValue(e){if("boolean"!=typeof e)throw new o.GraphQLError(`Boolean cannot represent a non boolean value: ${(0,r.inspect)(e)}`);return e},parseLiteral(e){if(e.kind!==a.Kind.BOOLEAN)throw new o.GraphQLError(`Boolean cannot represent a non boolean value: ${(0,s.print)(e)}`,{nodes:e});return e.value}});t.GraphQLBoolean=y;const m=new u.GraphQLScalarType({name:"ID",description:'The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `"4"`) or integer (such as `4`) input value will be accepted as an ID.',serialize(e){const t=T(e);if("string"==typeof t)return t;if(Number.isInteger(t))return String(t);throw new o.GraphQLError(`ID cannot represent value: ${(0,r.inspect)(e)}`)},parseValue(e){if("string"==typeof e)return e;if("number"==typeof e&&Number.isInteger(e))return e.toString();throw new o.GraphQLError(`ID cannot represent value: ${(0,r.inspect)(e)}`)},parseLiteral(e){if(e.kind!==a.Kind.STRING&&e.kind!==a.Kind.INT)throw new o.GraphQLError("ID cannot represent a non-string and non-integer value: "+(0,s.print)(e),{nodes:e});return e.value}});t.GraphQLID=m;const h=Object.freeze([f,l,d,y,m]);function T(e){if((0,i.isObjectLike)(e)){if("function"==typeof e.valueOf){const t=e.valueOf();if(!(0,i.isObjectLike)(t))return t}if("function"==typeof e.toJSON)return e.toJSON()}return e}t.specifiedScalarTypes=h},4648:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GraphQLSchema=void 0,t.assertSchema=function(e){if(!d(e))throw new Error(`Expected ${(0,i.inspect)(e)} to be a GraphQL schema.`);return e},t.isSchema=d;var r=n(3028),i=n(9657),o=n(9527),a=n(5569),s=n(3101),u=n(6257),c=n(3754),p=n(8685),l=n(8364);function d(e){return(0,o.instanceOf)(e,f)}class f{constructor(e){var t,n;this.__validationErrors=!0===e.assumeValid?[]:void 0,(0,a.isObjectLike)(e)||(0,r.devAssert)(!1,"Must provide configuration object."),!e.types||Array.isArray(e.types)||(0,r.devAssert)(!1,`"types" must be Array if provided but got: ${(0,i.inspect)(e.types)}.`),!e.directives||Array.isArray(e.directives)||(0,r.devAssert)(!1,`"directives" must be Array if provided but got: ${(0,i.inspect)(e.directives)}.`),this.description=e.description,this.extensions=(0,s.toObjMap)(e.extensions),this.astNode=e.astNode,this.extensionASTNodes=null!==(t=e.extensionASTNodes)&&void 0!==t?t:[],this._queryType=e.query,this._mutationType=e.mutation,this._subscriptionType=e.subscription,this._directives=null!==(n=e.directives)&&void 0!==n?n:p.specifiedDirectives;const o=new Set(e.types);if(null!=e.types)for(const t of e.types)o.delete(t),y(t,o);null!=this._queryType&&y(this._queryType,o),null!=this._mutationType&&y(this._mutationType,o),null!=this._subscriptionType&&y(this._subscriptionType,o);for(const e of this._directives)if((0,p.isDirective)(e))for(const t of e.args)y(t.type,o);y(l.__Schema,o),this._typeMap=Object.create(null),this._subTypeMap=Object.create(null),this._implementationsMap=Object.create(null);for(const e of o){if(null==e)continue;const t=e.name;if(t||(0,r.devAssert)(!1,"One of the provided types for building the Schema is missing a name."),void 0!==this._typeMap[t])throw new Error(`Schema must contain uniquely named types but contains multiple types named "${t}".`);if(this._typeMap[t]=e,(0,c.isInterfaceType)(e)){for(const t of e.getInterfaces())if((0,c.isInterfaceType)(t)){let n=this._implementationsMap[t.name];void 0===n&&(n=this._implementationsMap[t.name]={objects:[],interfaces:[]}),n.interfaces.push(e)}}else if((0,c.isObjectType)(e))for(const t of e.getInterfaces())if((0,c.isInterfaceType)(t)){let n=this._implementationsMap[t.name];void 0===n&&(n=this._implementationsMap[t.name]={objects:[],interfaces:[]}),n.objects.push(e)}}}get[Symbol.toStringTag](){return"GraphQLSchema"}getQueryType(){return this._queryType}getMutationType(){return this._mutationType}getSubscriptionType(){return this._subscriptionType}getRootType(e){switch(e){case u.OperationTypeNode.QUERY:return this.getQueryType();case u.OperationTypeNode.MUTATION:return this.getMutationType();case u.OperationTypeNode.SUBSCRIPTION:return this.getSubscriptionType()}}getTypeMap(){return this._typeMap}getType(e){return this.getTypeMap()[e]}getPossibleTypes(e){return(0,c.isUnionType)(e)?e.getTypes():this.getImplementations(e).objects}getImplementations(e){const t=this._implementationsMap[e.name];return null!=t?t:{objects:[],interfaces:[]}}isSubType(e,t){let n=this._subTypeMap[e.name];if(void 0===n){if(n=Object.create(null),(0,c.isUnionType)(e))for(const t of e.getTypes())n[t.name]=!0;else{const t=this.getImplementations(e);for(const e of t.objects)n[e.name]=!0;for(const e of t.interfaces)n[e.name]=!0}this._subTypeMap[e.name]=n}return void 0!==n[t.name]}getDirectives(){return this._directives}getDirective(e){return this.getDirectives().find((t=>t.name===e))}toConfig(){return{description:this.description,query:this.getQueryType(),mutation:this.getMutationType(),subscription:this.getSubscriptionType(),types:Object.values(this.getTypeMap()),directives:this.getDirectives(),extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes,assumeValid:void 0!==this.__validationErrors}}}function y(e,t){const n=(0,c.getNamedType)(e);if(!t.has(n))if(t.add(n),(0,c.isUnionType)(n))for(const e of n.getTypes())y(e,t);else if((0,c.isObjectType)(n)||(0,c.isInterfaceType)(n)){for(const e of n.getInterfaces())y(e,t);for(const e of Object.values(n.getFields())){y(e.type,t);for(const n of e.args)y(n.type,t)}}else if((0,c.isInputObjectType)(n))for(const e of Object.values(n.getFields()))y(e.type,t);return t}t.GraphQLSchema=f},9873:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.assertValidSchema=function(e){const t=l(e);if(0!==t.length)throw new Error(t.map((e=>e.message)).join("\n\n"))},t.validateSchema=l;var r=n(9657),i=n(1702),o=n(6257),a=n(3448),s=n(3754),u=n(8685),c=n(8364),p=n(4648);function l(e){if((0,p.assertSchema)(e),e.__validationErrors)return e.__validationErrors;const t=new d(e);!function(e){const t=e.schema,n=t.getQueryType();if(n){if(!(0,s.isObjectType)(n)){var i;e.reportError(`Query root type must be Object type, it cannot be ${(0,r.inspect)(n)}.`,null!==(i=f(t,o.OperationTypeNode.QUERY))&&void 0!==i?i:n.astNode)}}else e.reportError("Query root type must be provided.",t.astNode);const a=t.getMutationType();var u;a&&!(0,s.isObjectType)(a)&&e.reportError(`Mutation root type must be Object type if provided, it cannot be ${(0,r.inspect)(a)}.`,null!==(u=f(t,o.OperationTypeNode.MUTATION))&&void 0!==u?u:a.astNode);const c=t.getSubscriptionType();var p;c&&!(0,s.isObjectType)(c)&&e.reportError(`Subscription root type must be Object type if provided, it cannot be ${(0,r.inspect)(c)}.`,null!==(p=f(t,o.OperationTypeNode.SUBSCRIPTION))&&void 0!==p?p:c.astNode)}(t),function(e){for(const n of e.schema.getDirectives())if((0,u.isDirective)(n)){y(e,n);for(const i of n.args){var t;y(e,i),(0,s.isInputType)(i.type)||e.reportError(`The type of @${n.name}(${i.name}:) must be Input Type but got: ${(0,r.inspect)(i.type)}.`,i.astNode),(0,s.isRequiredArgument)(i)&&null!=i.deprecationReason&&e.reportError(`Required argument @${n.name}(${i.name}:) cannot be deprecated.`,[I(i.astNode),null===(t=i.astNode)||void 0===t?void 0:t.type])}}else e.reportError(`Expected directive but got: ${(0,r.inspect)(n)}.`,null==n?void 0:n.astNode)}(t),function(e){const t=function(e){const t=Object.create(null),n=[],r=Object.create(null);return function i(o){if(t[o.name])return;t[o.name]=!0,r[o.name]=n.length;const a=Object.values(o.getFields());for(const t of a)if((0,s.isNonNullType)(t.type)&&(0,s.isInputObjectType)(t.type.ofType)){const o=t.type.ofType,a=r[o.name];if(n.push(t),void 0===a)i(o);else{const t=n.slice(a),r=t.map((e=>e.name)).join(".");e.reportError(`Cannot reference Input Object "${o.name}" within itself through a series of non-null fields: "${r}".`,t.map((e=>e.astNode)))}n.pop()}r[o.name]=void 0}}(e),n=e.schema.getTypeMap();for(const i of Object.values(n))(0,s.isNamedType)(i)?((0,c.isIntrospectionType)(i)||y(e,i),(0,s.isObjectType)(i)||(0,s.isInterfaceType)(i)?(m(e,i),h(e,i)):(0,s.isUnionType)(i)?v(e,i):(0,s.isEnumType)(i)?g(e,i):(0,s.isInputObjectType)(i)&&(E(e,i),t(i))):e.reportError(`Expected GraphQL named type but got: ${(0,r.inspect)(i)}.`,i.astNode)}(t);const n=t.getErrors();return e.__validationErrors=n,n}class d{constructor(e){this._errors=[],this.schema=e}reportError(e,t){const n=Array.isArray(t)?t.filter(Boolean):t;this._errors.push(new i.GraphQLError(e,{nodes:n}))}getErrors(){return this._errors}}function f(e,t){var n;return null===(n=[e.astNode,...e.extensionASTNodes].flatMap((e=>{var t;return null!==(t=null==e?void 0:e.operationTypes)&&void 0!==t?t:[]})).find((e=>e.operation===t)))||void 0===n?void 0:n.type}function y(e,t){t.name.startsWith("__")&&e.reportError(`Name "${t.name}" must not begin with "__", which is reserved by GraphQL introspection.`,t.astNode)}function m(e,t){const n=Object.values(t.getFields());0===n.length&&e.reportError(`Type ${t.name} must define one or more fields.`,[t.astNode,...t.extensionASTNodes]);for(const u of n){var i;y(e,u),(0,s.isOutputType)(u.type)||e.reportError(`The type of ${t.name}.${u.name} must be Output Type but got: ${(0,r.inspect)(u.type)}.`,null===(i=u.astNode)||void 0===i?void 0:i.type);for(const n of u.args){const i=n.name;var o,a;y(e,n),(0,s.isInputType)(n.type)||e.reportError(`The type of ${t.name}.${u.name}(${i}:) must be Input Type but got: ${(0,r.inspect)(n.type)}.`,null===(o=n.astNode)||void 0===o?void 0:o.type),(0,s.isRequiredArgument)(n)&&null!=n.deprecationReason&&e.reportError(`Required argument ${t.name}.${u.name}(${i}:) cannot be deprecated.`,[I(n.astNode),null===(a=n.astNode)||void 0===a?void 0:a.type])}}}function h(e,t){const n=Object.create(null);for(const i of t.getInterfaces())(0,s.isInterfaceType)(i)?t!==i?n[i.name]?e.reportError(`Type ${t.name} can only implement ${i.name} once.`,N(t,i)):(n[i.name]=!0,b(e,t,i),T(e,t,i)):e.reportError(`Type ${t.name} cannot implement itself because it would create a circular reference.`,N(t,i)):e.reportError(`Type ${(0,r.inspect)(t)} must only implement Interface types, it cannot implement ${(0,r.inspect)(i)}.`,N(t,i))}function T(e,t,n){const i=t.getFields();for(const l of Object.values(n.getFields())){const d=l.name,f=i[d];if(f){var o,u;(0,a.isTypeSubTypeOf)(e.schema,f.type,l.type)||e.reportError(`Interface field ${n.name}.${d} expects type ${(0,r.inspect)(l.type)} but ${t.name}.${d} is type ${(0,r.inspect)(f.type)}.`,[null===(o=l.astNode)||void 0===o?void 0:o.type,null===(u=f.astNode)||void 0===u?void 0:u.type]);for(const i of l.args){const o=i.name,s=f.args.find((e=>e.name===o));var c,p;s?(0,a.isEqualType)(i.type,s.type)||e.reportError(`Interface field argument ${n.name}.${d}(${o}:) expects type ${(0,r.inspect)(i.type)} but ${t.name}.${d}(${o}:) is type ${(0,r.inspect)(s.type)}.`,[null===(c=i.astNode)||void 0===c?void 0:c.type,null===(p=s.astNode)||void 0===p?void 0:p.type]):e.reportError(`Interface field argument ${n.name}.${d}(${o}:) expected but ${t.name}.${d} does not provide it.`,[i.astNode,f.astNode])}for(const r of f.args){const i=r.name;!l.args.find((e=>e.name===i))&&(0,s.isRequiredArgument)(r)&&e.reportError(`Object field ${t.name}.${d} includes required argument ${i} that is missing from the Interface field ${n.name}.${d}.`,[r.astNode,l.astNode])}}else e.reportError(`Interface field ${n.name}.${d} expected but ${t.name} does not provide it.`,[l.astNode,t.astNode,...t.extensionASTNodes])}}function b(e,t,n){const r=t.getInterfaces();for(const i of n.getInterfaces())r.includes(i)||e.reportError(i===t?`Type ${t.name} cannot implement ${n.name} because it would create a circular reference.`:`Type ${t.name} must implement ${i.name} because it is implemented by ${n.name}.`,[...N(n,i),...N(t,n)])}function v(e,t){const n=t.getTypes();0===n.length&&e.reportError(`Union type ${t.name} must define one or more member types.`,[t.astNode,...t.extensionASTNodes]);const i=Object.create(null);for(const o of n)i[o.name]?e.reportError(`Union type ${t.name} can only include type ${o.name} once.`,O(t,o.name)):(i[o.name]=!0,(0,s.isObjectType)(o)||e.reportError(`Union type ${t.name} can only include Object types, it cannot include ${(0,r.inspect)(o)}.`,O(t,String(o))))}function g(e,t){const n=t.getValues();0===n.length&&e.reportError(`Enum type ${t.name} must define one or more values.`,[t.astNode,...t.extensionASTNodes]);for(const t of n)y(e,t)}function E(e,t){const n=Object.values(t.getFields());0===n.length&&e.reportError(`Input Object type ${t.name} must define one or more fields.`,[t.astNode,...t.extensionASTNodes]);for(const a of n){var i,o;y(e,a),(0,s.isInputType)(a.type)||e.reportError(`The type of ${t.name}.${a.name} must be Input Type but got: ${(0,r.inspect)(a.type)}.`,null===(i=a.astNode)||void 0===i?void 0:i.type),(0,s.isRequiredInputField)(a)&&null!=a.deprecationReason&&e.reportError(`Required input field ${t.name}.${a.name} cannot be deprecated.`,[I(a.astNode),null===(o=a.astNode)||void 0===o?void 0:o.type])}}function N(e,t){const{astNode:n,extensionASTNodes:r}=e;return(null!=n?[n,...r]:r).flatMap((e=>{var t;return null!==(t=e.interfaces)&&void 0!==t?t:[]})).filter((e=>e.name.value===t.name))}function O(e,t){const{astNode:n,extensionASTNodes:r}=e;return(null!=n?[n,...r]:r).flatMap((e=>{var t;return null!==(t=e.types)&&void 0!==t?t:[]})).filter((e=>e.name.value===t))}function I(e){var t;return null==e||null===(t=e.directives)||void 0===t?void 0:t.find((e=>e.name.value===u.GraphQLDeprecatedDirective.name))}},7485:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TypeInfo=void 0,t.visitWithTypeInfo=function(e,t){return{enter(...n){const i=n[0];e.enter(i);const a=(0,o.getEnterLeaveForKind)(t,i.kind).enter;if(a){const o=a.apply(t,n);return void 0!==o&&(e.leave(i),(0,r.isNode)(o)&&e.enter(o)),o}},leave(...n){const r=n[0],i=(0,o.getEnterLeaveForKind)(t,r.kind).leave;let a;return i&&(a=i.apply(t,n)),e.leave(r),a}}};var r=n(6257),i=n(7030),o=n(9111),a=n(3754),s=n(8364),u=n(6693);class c{constructor(e,t,n){this._schema=e,this._typeStack=[],this._parentTypeStack=[],this._inputTypeStack=[],this._fieldDefStack=[],this._defaultValueStack=[],this._directive=null,this._argument=null,this._enumValue=null,this._getFieldDef=null!=n?n:p,t&&((0,a.isInputType)(t)&&this._inputTypeStack.push(t),(0,a.isCompositeType)(t)&&this._parentTypeStack.push(t),(0,a.isOutputType)(t)&&this._typeStack.push(t))}get[Symbol.toStringTag](){return"TypeInfo"}getType(){if(this._typeStack.length>0)return this._typeStack[this._typeStack.length-1]}getParentType(){if(this._parentTypeStack.length>0)return this._parentTypeStack[this._parentTypeStack.length-1]}getInputType(){if(this._inputTypeStack.length>0)return this._inputTypeStack[this._inputTypeStack.length-1]}getParentInputType(){if(this._inputTypeStack.length>1)return this._inputTypeStack[this._inputTypeStack.length-2]}getFieldDef(){if(this._fieldDefStack.length>0)return this._fieldDefStack[this._fieldDefStack.length-1]}getDefaultValue(){if(this._defaultValueStack.length>0)return this._defaultValueStack[this._defaultValueStack.length-1]}getDirective(){return this._directive}getArgument(){return this._argument}getEnumValue(){return this._enumValue}enter(e){const t=this._schema;switch(e.kind){case i.Kind.SELECTION_SET:{const e=(0,a.getNamedType)(this.getType());this._parentTypeStack.push((0,a.isCompositeType)(e)?e:void 0);break}case i.Kind.FIELD:{const n=this.getParentType();let r,i;n&&(r=this._getFieldDef(t,n,e),r&&(i=r.type)),this._fieldDefStack.push(r),this._typeStack.push((0,a.isOutputType)(i)?i:void 0);break}case i.Kind.DIRECTIVE:this._directive=t.getDirective(e.name.value);break;case i.Kind.OPERATION_DEFINITION:{const n=t.getRootType(e.operation);this._typeStack.push((0,a.isObjectType)(n)?n:void 0);break}case i.Kind.INLINE_FRAGMENT:case i.Kind.FRAGMENT_DEFINITION:{const n=e.typeCondition,r=n?(0,u.typeFromAST)(t,n):(0,a.getNamedType)(this.getType());this._typeStack.push((0,a.isOutputType)(r)?r:void 0);break}case i.Kind.VARIABLE_DEFINITION:{const n=(0,u.typeFromAST)(t,e.type);this._inputTypeStack.push((0,a.isInputType)(n)?n:void 0);break}case i.Kind.ARGUMENT:{var n;let t,r;const i=null!==(n=this.getDirective())&&void 0!==n?n:this.getFieldDef();i&&(t=i.args.find((t=>t.name===e.name.value)),t&&(r=t.type)),this._argument=t,this._defaultValueStack.push(t?t.defaultValue:void 0),this._inputTypeStack.push((0,a.isInputType)(r)?r:void 0);break}case i.Kind.LIST:{const e=(0,a.getNullableType)(this.getInputType()),t=(0,a.isListType)(e)?e.ofType:e;this._defaultValueStack.push(void 0),this._inputTypeStack.push((0,a.isInputType)(t)?t:void 0);break}case i.Kind.OBJECT_FIELD:{const t=(0,a.getNamedType)(this.getInputType());let n,r;(0,a.isInputObjectType)(t)&&(r=t.getFields()[e.name.value],r&&(n=r.type)),this._defaultValueStack.push(r?r.defaultValue:void 0),this._inputTypeStack.push((0,a.isInputType)(n)?n:void 0);break}case i.Kind.ENUM:{const t=(0,a.getNamedType)(this.getInputType());let n;(0,a.isEnumType)(t)&&(n=t.getValue(e.value)),this._enumValue=n;break}}}leave(e){switch(e.kind){case i.Kind.SELECTION_SET:this._parentTypeStack.pop();break;case i.Kind.FIELD:this._fieldDefStack.pop(),this._typeStack.pop();break;case i.Kind.DIRECTIVE:this._directive=null;break;case i.Kind.OPERATION_DEFINITION:case i.Kind.INLINE_FRAGMENT:case i.Kind.FRAGMENT_DEFINITION:this._typeStack.pop();break;case i.Kind.VARIABLE_DEFINITION:this._inputTypeStack.pop();break;case i.Kind.ARGUMENT:this._argument=null,this._defaultValueStack.pop(),this._inputTypeStack.pop();break;case i.Kind.LIST:case i.Kind.OBJECT_FIELD:this._defaultValueStack.pop(),this._inputTypeStack.pop();break;case i.Kind.ENUM:this._enumValue=null}}}function p(e,t,n){const r=n.name.value;return r===s.SchemaMetaFieldDef.name&&e.getQueryType()===t?s.SchemaMetaFieldDef:r===s.TypeMetaFieldDef.name&&e.getQueryType()===t?s.TypeMetaFieldDef:r===s.TypeNameMetaFieldDef.name&&(0,a.isCompositeType)(t)?s.TypeNameMetaFieldDef:(0,a.isObjectType)(t)||(0,a.isInterfaceType)(t)?t.getFields()[r]:void 0}t.TypeInfo=c},8426:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.assertValidName=function(e){const t=a(e);if(t)throw t;return e},t.isValidNameError=a;var r=n(3028),i=n(1702),o=n(3506);function a(e){if("string"==typeof e||(0,r.devAssert)(!1,"Expected name to be a string."),e.startsWith("__"))return new i.GraphQLError(`Name "${e}" must not begin with "__", which is reserved by GraphQL introspection.`);try{(0,o.assertName)(e)}catch(e){return e}}},8096:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.astFromValue=function e(t,n){if((0,u.isNonNullType)(n)){const r=e(t,n.ofType);return(null==r?void 0:r.kind)===s.Kind.NULL?null:r}if(null===t)return{kind:s.Kind.NULL};if(void 0===t)return null;if((0,u.isListType)(n)){const r=n.ofType;if((0,o.isIterableObject)(t)){const n=[];for(const i of t){const t=e(i,r);null!=t&&n.push(t)}return{kind:s.Kind.LIST,values:n}}return e(t,r)}if((0,u.isInputObjectType)(n)){if(!(0,a.isObjectLike)(t))return null;const r=[];for(const i of Object.values(n.getFields())){const n=e(t[i.name],i.type);n&&r.push({kind:s.Kind.OBJECT_FIELD,name:{kind:s.Kind.NAME,value:i.name},value:n})}return{kind:s.Kind.OBJECT,fields:r}}if((0,u.isLeafType)(n)){const e=n.serialize(t);if(null==e)return null;if("boolean"==typeof e)return{kind:s.Kind.BOOLEAN,value:e};if("number"==typeof e&&Number.isFinite(e)){const t=String(e);return p.test(t)?{kind:s.Kind.INT,value:t}:{kind:s.Kind.FLOAT,value:t}}if("string"==typeof e)return(0,u.isEnumType)(n)?{kind:s.Kind.ENUM,value:e}:n===c.GraphQLID&&p.test(e)?{kind:s.Kind.INT,value:e}:{kind:s.Kind.STRING,value:e};throw new TypeError(`Cannot convert value to AST: ${(0,r.inspect)(e)}.`)}(0,i.invariant)(!1,"Unexpected input type: "+(0,r.inspect)(n))};var r=n(9657),i=n(1321),o=n(4820),a=n(5569),s=n(7030),u=n(3754),c=n(1062);const p=/^-?(?:0|[1-9][0-9]*)$/},434:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.buildASTSchema=p,t.buildSchema=function(e,t){return p((0,o.parse)(e,{noLocation:null==t?void 0:t.noLocation,allowLegacyFragmentVariables:null==t?void 0:t.allowLegacyFragmentVariables}),{assumeValidSDL:null==t?void 0:t.assumeValidSDL,assumeValid:null==t?void 0:t.assumeValid})};var r=n(3028),i=n(7030),o=n(246),a=n(8685),s=n(4648),u=n(9040),c=n(1442);function p(e,t){null!=e&&e.kind===i.Kind.DOCUMENT||(0,r.devAssert)(!1,"Must provide valid Document AST."),!0!==(null==t?void 0:t.assumeValid)&&!0!==(null==t?void 0:t.assumeValidSDL)&&(0,u.assertValidSDL)(e);const n={description:void 0,types:[],directives:[],extensions:Object.create(null),extensionASTNodes:[],assumeValid:!1},o=(0,c.extendSchemaImpl)(n,e,t);if(null==o.astNode)for(const e of o.types)switch(e.name){case"Query":o.query=e;break;case"Mutation":o.mutation=e;break;case"Subscription":o.subscription=e}const p=[...o.directives,...a.specifiedDirectives.filter((e=>o.directives.every((t=>t.name!==e.name))))];return new s.GraphQLSchema({...o,directives:p})}},6613:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.buildClientSchema=function(e,t){(0,o.isObjectLike)(e)&&(0,o.isObjectLike)(e.__schema)||(0,r.devAssert)(!1,`Invalid or incomplete introspection result. Ensure that you are passing "data" property of introspection response and no "errors" was returned alongside: ${(0,i.inspect)(e)}.`);const n=e.__schema,y=(0,a.keyValMap)(n.types,(e=>e.name),(e=>function(e){if(null!=e&&null!=e.name&&null!=e.kind)switch(e.kind){case p.TypeKind.SCALAR:return r=e,new u.GraphQLScalarType({name:r.name,description:r.description,specifiedByURL:r.specifiedByURL});case p.TypeKind.OBJECT:return n=e,new u.GraphQLObjectType({name:n.name,description:n.description,interfaces:()=>O(n),fields:()=>I(n)});case p.TypeKind.INTERFACE:return t=e,new u.GraphQLInterfaceType({name:t.name,description:t.description,interfaces:()=>O(t),fields:()=>I(t)});case p.TypeKind.UNION:return function(e){if(!e.possibleTypes){const t=(0,i.inspect)(e);throw new Error(`Introspection result missing possibleTypes: ${t}.`)}return new u.GraphQLUnionType({name:e.name,description:e.description,types:()=>e.possibleTypes.map(E)})}(e);case p.TypeKind.ENUM:return function(e){if(!e.enumValues){const t=(0,i.inspect)(e);throw new Error(`Introspection result missing enumValues: ${t}.`)}return new u.GraphQLEnumType({name:e.name,description:e.description,values:(0,a.keyValMap)(e.enumValues,(e=>e.name),(e=>({description:e.description,deprecationReason:e.deprecationReason})))})}(e);case p.TypeKind.INPUT_OBJECT:return function(e){if(!e.inputFields){const t=(0,i.inspect)(e);throw new Error(`Introspection result missing inputFields: ${t}.`)}return new u.GraphQLInputObjectType({name:e.name,description:e.description,fields:()=>L(e.inputFields)})}(e)}var t,n,r;const o=(0,i.inspect)(e);throw new Error(`Invalid or incomplete introspection result. Ensure that a full introspection query is used in order to build a client schema: ${o}.`)}(e)));for(const e of[...l.specifiedScalarTypes,...p.introspectionTypes])y[e.name]&&(y[e.name]=e);const m=n.queryType?E(n.queryType):null,h=n.mutationType?E(n.mutationType):null,T=n.subscriptionType?E(n.subscriptionType):null,b=n.directives?n.directives.map((function(e){if(!e.args){const t=(0,i.inspect)(e);throw new Error(`Introspection result missing directive args: ${t}.`)}if(!e.locations){const t=(0,i.inspect)(e);throw new Error(`Introspection result missing directive locations: ${t}.`)}return new c.GraphQLDirective({name:e.name,description:e.description,isRepeatable:e.isRepeatable,locations:e.locations.slice(),args:L(e.args)})})):[];return new d.GraphQLSchema({description:n.description,query:m,mutation:h,subscription:T,types:Object.values(y),directives:b,assumeValid:null==t?void 0:t.assumeValid});function v(e){if(e.kind===p.TypeKind.LIST){const t=e.ofType;if(!t)throw new Error("Decorated type deeper than introspection query.");return new u.GraphQLList(v(t))}if(e.kind===p.TypeKind.NON_NULL){const t=e.ofType;if(!t)throw new Error("Decorated type deeper than introspection query.");const n=v(t);return new u.GraphQLNonNull((0,u.assertNullableType)(n))}return g(e)}function g(e){const t=e.name;if(!t)throw new Error(`Unknown type reference: ${(0,i.inspect)(e)}.`);const n=y[t];if(!n)throw new Error(`Invalid or incomplete schema, unknown type: ${t}. Ensure that a full introspection query is used in order to build a client schema.`);return n}function E(e){return(0,u.assertObjectType)(g(e))}function N(e){return(0,u.assertInterfaceType)(g(e))}function O(e){if(null===e.interfaces&&e.kind===p.TypeKind.INTERFACE)return[];if(!e.interfaces){const t=(0,i.inspect)(e);throw new Error(`Introspection result missing interfaces: ${t}.`)}return e.interfaces.map(N)}function I(e){if(!e.fields)throw new Error(`Introspection result missing fields: ${(0,i.inspect)(e)}.`);return(0,a.keyValMap)(e.fields,(e=>e.name),_)}function _(e){const t=v(e.type);if(!(0,u.isOutputType)(t)){const e=(0,i.inspect)(t);throw new Error(`Introspection must provide output type for fields, but received: ${e}.`)}if(!e.args){const t=(0,i.inspect)(e);throw new Error(`Introspection result missing field args: ${t}.`)}return{description:e.description,deprecationReason:e.deprecationReason,type:t,args:L(e.args)}}function L(e){return(0,a.keyValMap)(e,(e=>e.name),S)}function S(e){const t=v(e.type);if(!(0,u.isInputType)(t)){const e=(0,i.inspect)(t);throw new Error(`Introspection must provide input type for arguments, but received: ${e}.`)}const n=null!=e.defaultValue?(0,f.valueFromAST)((0,s.parseValue)(e.defaultValue),t):void 0;return{description:e.description,type:t,defaultValue:n,deprecationReason:e.deprecationReason}}};var r=n(3028),i=n(9657),o=n(5569),a=n(5785),s=n(246),u=n(3754),c=n(8685),p=n(8364),l=n(1062),d=n(4648),f=n(2302)},4090:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.coerceInputValue=function(e,t,n=f){return y(e,t,n,void 0)};var r=n(2832),i=n(9657),o=n(1321),a=n(4820),s=n(5569),u=n(6506),c=n(636),p=n(1709),l=n(1702),d=n(3754);function f(e,t,n){let r="Invalid value "+(0,i.inspect)(t);throw e.length>0&&(r+=` at "value${(0,c.printPathArray)(e)}"`),n.message=r+": "+n.message,n}function y(e,t,n,c){if((0,d.isNonNullType)(t))return null!=e?y(e,t.ofType,n,c):void n((0,u.pathToArray)(c),e,new l.GraphQLError(`Expected non-nullable type "${(0,i.inspect)(t)}" not to be null.`));if(null==e)return null;if((0,d.isListType)(t)){const r=t.ofType;return(0,a.isIterableObject)(e)?Array.from(e,((e,t)=>{const i=(0,u.addPath)(c,t,void 0);return y(e,r,n,i)})):[y(e,r,n,c)]}if((0,d.isInputObjectType)(t)){if(!(0,s.isObjectLike)(e))return void n((0,u.pathToArray)(c),e,new l.GraphQLError(`Expected type "${t.name}" to be an object.`));const o={},a=t.getFields();for(const r of Object.values(a)){const a=e[r.name];if(void 0!==a)o[r.name]=y(a,r.type,n,(0,u.addPath)(c,r.name,t.name));else if(void 0!==r.defaultValue)o[r.name]=r.defaultValue;else if((0,d.isNonNullType)(r.type)){const t=(0,i.inspect)(r.type);n((0,u.pathToArray)(c),e,new l.GraphQLError(`Field "${r.name}" of required type "${t}" was not provided.`))}}for(const i of Object.keys(e))if(!a[i]){const o=(0,p.suggestionList)(i,Object.keys(t.getFields()));n((0,u.pathToArray)(c),e,new l.GraphQLError(`Field "${i}" is not defined by type "${t.name}".`+(0,r.didYouMean)(o)))}return o}if((0,d.isLeafType)(t)){let r;try{r=t.parseValue(e)}catch(r){return void(r instanceof l.GraphQLError?n((0,u.pathToArray)(c),e,r):n((0,u.pathToArray)(c),e,new l.GraphQLError(`Expected type "${t.name}". `+r.message,{originalError:r})))}return void 0===r&&n((0,u.pathToArray)(c),e,new l.GraphQLError(`Expected type "${t.name}".`)),r}(0,o.invariant)(!1,"Unexpected input type: "+(0,i.inspect)(t))}},3129:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.concatAST=function(e){const t=[];for(const n of e)t.push(...n.definitions);return{kind:r.Kind.DOCUMENT,definitions:t}};var r=n(7030)},1442:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.extendSchema=function(e,t,n){(0,y.assertSchema)(e),null!=t&&t.kind===u.Kind.DOCUMENT||(0,r.devAssert)(!1,"Must provide valid Document AST."),!0!==(null==n?void 0:n.assumeValid)&&!0!==(null==n?void 0:n.assumeValidSDL)&&(0,m.assertValidSDLExtension)(t,e);const i=e.toConfig(),o=b(i,t,n);return i===o?e:new y.GraphQLSchema(o)},t.extendSchemaImpl=b;var r=n(3028),i=n(9657),o=n(1321),a=n(4590),s=n(3430),u=n(7030),c=n(9187),p=n(3754),l=n(8685),d=n(8364),f=n(1062),y=n(4648),m=n(9040),h=n(8113),T=n(2302);function b(e,t,n){var r,a,y,m;const h=[],b=Object.create(null),N=[];let O;const I=[];for(const e of t.definitions)if(e.kind===u.Kind.SCHEMA_DEFINITION)O=e;else if(e.kind===u.Kind.SCHEMA_EXTENSION)I.push(e);else if((0,c.isTypeDefinitionNode)(e))h.push(e);else if((0,c.isTypeExtensionNode)(e)){const t=e.name.value,n=b[t];b[t]=n?n.concat([e]):[e]}else e.kind===u.Kind.DIRECTIVE_DEFINITION&&N.push(e);if(0===Object.keys(b).length&&0===h.length&&0===N.length&&0===I.length&&null==O)return e;const _=Object.create(null);for(const t of e.types)_[t.name]=(L=t,(0,d.isIntrospectionType)(L)||(0,f.isSpecifiedScalarType)(L)?L:(0,p.isScalarType)(L)?function(e){var t;const n=e.toConfig(),r=null!==(t=b[n.name])&&void 0!==t?t:[];let i=n.specifiedByURL;for(const e of r){var o;i=null!==(o=E(e))&&void 0!==o?o:i}return new p.GraphQLScalarType({...n,specifiedByURL:i,extensionASTNodes:n.extensionASTNodes.concat(r)})}(L):(0,p.isObjectType)(L)?function(e){var t;const n=e.toConfig(),r=null!==(t=b[n.name])&&void 0!==t?t:[];return new p.GraphQLObjectType({...n,interfaces:()=>[...e.getInterfaces().map(A),...M(r)],fields:()=>({...(0,s.mapValue)(n.fields,P),...x(r)}),extensionASTNodes:n.extensionASTNodes.concat(r)})}(L):(0,p.isInterfaceType)(L)?function(e){var t;const n=e.toConfig(),r=null!==(t=b[n.name])&&void 0!==t?t:[];return new p.GraphQLInterfaceType({...n,interfaces:()=>[...e.getInterfaces().map(A),...M(r)],fields:()=>({...(0,s.mapValue)(n.fields,P),...x(r)}),extensionASTNodes:n.extensionASTNodes.concat(r)})}(L):(0,p.isUnionType)(L)?function(e){var t;const n=e.toConfig(),r=null!==(t=b[n.name])&&void 0!==t?t:[];return new p.GraphQLUnionType({...n,types:()=>[...e.getTypes().map(A),...K(r)],extensionASTNodes:n.extensionASTNodes.concat(r)})}(L):(0,p.isEnumType)(L)?function(e){var t;const n=e.toConfig(),r=null!==(t=b[e.name])&&void 0!==t?t:[];return new p.GraphQLEnumType({...n,values:{...n.values,...C(r)},extensionASTNodes:n.extensionASTNodes.concat(r)})}(L):(0,p.isInputObjectType)(L)?function(e){var t;const n=e.toConfig(),r=null!==(t=b[n.name])&&void 0!==t?t:[];return new p.GraphQLInputObjectType({...n,fields:()=>({...(0,s.mapValue)(n.fields,(e=>({...e,type:j(e.type)}))),...V(r)}),extensionASTNodes:n.extensionASTNodes.concat(r)})}(L):void(0,o.invariant)(!1,"Unexpected type: "+(0,i.inspect)(L)));var L;for(const e of h){var S;const t=e.name.value;_[t]=null!==(S=v[t])&&void 0!==S?S:Q(e)}const D={query:e.query&&A(e.query),mutation:e.mutation&&A(e.mutation),subscription:e.subscription&&A(e.subscription),...O&&w([O]),...w(I)};return{description:null===(r=O)||void 0===r||null===(a=r.description)||void 0===a?void 0:a.value,...D,types:Object.values(_),directives:[...e.directives.map((function(e){const t=e.toConfig();return new l.GraphQLDirective({...t,args:(0,s.mapValue)(t.args,R)})})),...N.map((function(e){var t;return new l.GraphQLDirective({name:e.name.value,description:null===(t=e.description)||void 0===t?void 0:t.value,locations:e.locations.map((({value:e})=>e)),isRepeatable:e.repeatable,args:G(e.arguments),astNode:e})}))],extensions:Object.create(null),astNode:null!==(y=O)&&void 0!==y?y:e.astNode,extensionASTNodes:e.extensionASTNodes.concat(I),assumeValid:null!==(m=null==n?void 0:n.assumeValid)&&void 0!==m&&m};function j(e){return(0,p.isListType)(e)?new p.GraphQLList(j(e.ofType)):(0,p.isNonNullType)(e)?new p.GraphQLNonNull(j(e.ofType)):A(e)}function A(e){return _[e.name]}function P(e){return{...e,type:j(e.type),args:e.args&&(0,s.mapValue)(e.args,R)}}function R(e){return{...e,type:j(e.type)}}function w(e){const t={};for(const r of e){var n;const e=null!==(n=r.operationTypes)&&void 0!==n?n:[];for(const n of e)t[n.operation]=k(n.type)}return t}function k(e){var t;const n=e.name.value,r=null!==(t=v[n])&&void 0!==t?t:_[n];if(void 0===r)throw new Error(`Unknown type: "${n}".`);return r}function F(e){return e.kind===u.Kind.LIST_TYPE?new p.GraphQLList(F(e.type)):e.kind===u.Kind.NON_NULL_TYPE?new p.GraphQLNonNull(F(e.type)):k(e)}function x(e){const t=Object.create(null);for(const i of e){var n;const e=null!==(n=i.fields)&&void 0!==n?n:[];for(const n of e){var r;t[n.name.value]={type:F(n.type),description:null===(r=n.description)||void 0===r?void 0:r.value,args:G(n.arguments),deprecationReason:g(n),astNode:n}}}return t}function G(e){const t=null!=e?e:[],n=Object.create(null);for(const e of t){var r;const t=F(e.type);n[e.name.value]={type:t,description:null===(r=e.description)||void 0===r?void 0:r.value,defaultValue:(0,T.valueFromAST)(e.defaultValue,t),deprecationReason:g(e),astNode:e}}return n}function V(e){const t=Object.create(null);for(const i of e){var n;const e=null!==(n=i.fields)&&void 0!==n?n:[];for(const n of e){var r;const e=F(n.type);t[n.name.value]={type:e,description:null===(r=n.description)||void 0===r?void 0:r.value,defaultValue:(0,T.valueFromAST)(n.defaultValue,e),deprecationReason:g(n),astNode:n}}}return t}function C(e){const t=Object.create(null);for(const i of e){var n;const e=null!==(n=i.values)&&void 0!==n?n:[];for(const n of e){var r;t[n.name.value]={description:null===(r=n.description)||void 0===r?void 0:r.value,deprecationReason:g(n),astNode:n}}}return t}function M(e){return e.flatMap((e=>{var t,n;return null!==(t=null===(n=e.interfaces)||void 0===n?void 0:n.map(k))&&void 0!==t?t:[]}))}function K(e){return e.flatMap((e=>{var t,n;return null!==(t=null===(n=e.types)||void 0===n?void 0:n.map(k))&&void 0!==t?t:[]}))}function Q(e){var t;const n=e.name.value,r=null!==(t=b[n])&&void 0!==t?t:[];switch(e.kind){case u.Kind.OBJECT_TYPE_DEFINITION:{var i;const t=[e,...r];return new p.GraphQLObjectType({name:n,description:null===(i=e.description)||void 0===i?void 0:i.value,interfaces:()=>M(t),fields:()=>x(t),astNode:e,extensionASTNodes:r})}case u.Kind.INTERFACE_TYPE_DEFINITION:{var o;const t=[e,...r];return new p.GraphQLInterfaceType({name:n,description:null===(o=e.description)||void 0===o?void 0:o.value,interfaces:()=>M(t),fields:()=>x(t),astNode:e,extensionASTNodes:r})}case u.Kind.ENUM_TYPE_DEFINITION:{var a;const t=[e,...r];return new p.GraphQLEnumType({name:n,description:null===(a=e.description)||void 0===a?void 0:a.value,values:C(t),astNode:e,extensionASTNodes:r})}case u.Kind.UNION_TYPE_DEFINITION:{var s;const t=[e,...r];return new p.GraphQLUnionType({name:n,description:null===(s=e.description)||void 0===s?void 0:s.value,types:()=>K(t),astNode:e,extensionASTNodes:r})}case u.Kind.SCALAR_TYPE_DEFINITION:var c;return new p.GraphQLScalarType({name:n,description:null===(c=e.description)||void 0===c?void 0:c.value,specifiedByURL:E(e),astNode:e,extensionASTNodes:r});case u.Kind.INPUT_OBJECT_TYPE_DEFINITION:{var l;const t=[e,...r];return new p.GraphQLInputObjectType({name:n,description:null===(l=e.description)||void 0===l?void 0:l.value,fields:()=>V(t),astNode:e,extensionASTNodes:r})}}}}const v=(0,a.keyMap)([...f.specifiedScalarTypes,...d.introspectionTypes],(e=>e.name));function g(e){const t=(0,h.getDirectiveValues)(l.GraphQLDeprecatedDirective,e);return null==t?void 0:t.reason}function E(e){const t=(0,h.getDirectiveValues)(l.GraphQLSpecifiedByDirective,e);return null==t?void 0:t.url}},3666:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DangerousChangeType=t.BreakingChangeType=void 0,t.findBreakingChanges=function(e,t){return f(e,t).filter((e=>e.type in r))},t.findDangerousChanges=function(e,t){return f(e,t).filter((e=>e.type in i))};var r,i,o=n(9657),a=n(1321),s=n(4590),u=n(585),c=n(3754),p=n(1062),l=n(8096),d=n(1152);function f(e,t){return[...m(e,t),...y(e,t)]}function y(e,t){const n=[],i=L(e.getDirectives(),t.getDirectives());for(const e of i.removed)n.push({type:r.DIRECTIVE_REMOVED,description:`${e.name} was removed.`});for(const[e,t]of i.persisted){const i=L(e.args,t.args);for(const t of i.added)(0,c.isRequiredArgument)(t)&&n.push({type:r.REQUIRED_DIRECTIVE_ARG_ADDED,description:`A required arg ${t.name} on directive ${e.name} was added.`});for(const t of i.removed)n.push({type:r.DIRECTIVE_ARG_REMOVED,description:`${t.name} was removed from ${e.name}.`});e.isRepeatable&&!t.isRepeatable&&n.push({type:r.DIRECTIVE_REPEATABLE_REMOVED,description:`Repeatable flag was removed from ${e.name}.`});for(const i of e.locations)t.locations.includes(i)||n.push({type:r.DIRECTIVE_LOCATION_REMOVED,description:`${i} was removed from ${e.name}.`})}return n}function m(e,t){const n=[],i=L(Object.values(e.getTypeMap()),Object.values(t.getTypeMap()));for(const e of i.removed)n.push({type:r.TYPE_REMOVED,description:(0,p.isSpecifiedScalarType)(e)?`Standard scalar ${e.name} was removed because it is not referenced anymore.`:`${e.name} was removed.`});for(const[e,t]of i.persisted)(0,c.isEnumType)(e)&&(0,c.isEnumType)(t)?n.push(...b(e,t)):(0,c.isUnionType)(e)&&(0,c.isUnionType)(t)?n.push(...T(e,t)):(0,c.isInputObjectType)(e)&&(0,c.isInputObjectType)(t)?n.push(...h(e,t)):(0,c.isObjectType)(e)&&(0,c.isObjectType)(t)||(0,c.isInterfaceType)(e)&&(0,c.isInterfaceType)(t)?n.push(...g(e,t),...v(e,t)):e.constructor!==t.constructor&&n.push({type:r.TYPE_CHANGED_KIND,description:`${e.name} changed from ${I(e)} to ${I(t)}.`});return n}function h(e,t){const n=[],o=L(Object.values(e.getFields()),Object.values(t.getFields()));for(const t of o.added)(0,c.isRequiredInputField)(t)?n.push({type:r.REQUIRED_INPUT_FIELD_ADDED,description:`A required field ${t.name} on input type ${e.name} was added.`}):n.push({type:i.OPTIONAL_INPUT_FIELD_ADDED,description:`An optional field ${t.name} on input type ${e.name} was added.`});for(const t of o.removed)n.push({type:r.FIELD_REMOVED,description:`${e.name}.${t.name} was removed.`});for(const[t,i]of o.persisted)O(t.type,i.type)||n.push({type:r.FIELD_CHANGED_KIND,description:`${e.name}.${t.name} changed type from ${String(t.type)} to ${String(i.type)}.`});return n}function T(e,t){const n=[],o=L(e.getTypes(),t.getTypes());for(const t of o.added)n.push({type:i.TYPE_ADDED_TO_UNION,description:`${t.name} was added to union type ${e.name}.`});for(const t of o.removed)n.push({type:r.TYPE_REMOVED_FROM_UNION,description:`${t.name} was removed from union type ${e.name}.`});return n}function b(e,t){const n=[],o=L(e.getValues(),t.getValues());for(const t of o.added)n.push({type:i.VALUE_ADDED_TO_ENUM,description:`${t.name} was added to enum type ${e.name}.`});for(const t of o.removed)n.push({type:r.VALUE_REMOVED_FROM_ENUM,description:`${t.name} was removed from enum type ${e.name}.`});return n}function v(e,t){const n=[],o=L(e.getInterfaces(),t.getInterfaces());for(const t of o.added)n.push({type:i.IMPLEMENTED_INTERFACE_ADDED,description:`${t.name} added to interfaces implemented by ${e.name}.`});for(const t of o.removed)n.push({type:r.IMPLEMENTED_INTERFACE_REMOVED,description:`${e.name} no longer implements interface ${t.name}.`});return n}function g(e,t){const n=[],i=L(Object.values(e.getFields()),Object.values(t.getFields()));for(const t of i.removed)n.push({type:r.FIELD_REMOVED,description:`${e.name}.${t.name} was removed.`});for(const[t,o]of i.persisted)n.push(...E(e,t,o)),N(t.type,o.type)||n.push({type:r.FIELD_CHANGED_KIND,description:`${e.name}.${t.name} changed type from ${String(t.type)} to ${String(o.type)}.`});return n}function E(e,t,n){const o=[],a=L(t.args,n.args);for(const n of a.removed)o.push({type:r.ARG_REMOVED,description:`${e.name}.${t.name} arg ${n.name} was removed.`});for(const[n,s]of a.persisted)if(O(n.type,s.type)){if(void 0!==n.defaultValue)if(void 0===s.defaultValue)o.push({type:i.ARG_DEFAULT_VALUE_CHANGE,description:`${e.name}.${t.name} arg ${n.name} defaultValue was removed.`});else{const r=_(n.defaultValue,n.type),a=_(s.defaultValue,s.type);r!==a&&o.push({type:i.ARG_DEFAULT_VALUE_CHANGE,description:`${e.name}.${t.name} arg ${n.name} has changed defaultValue from ${r} to ${a}.`})}}else o.push({type:r.ARG_CHANGED_KIND,description:`${e.name}.${t.name} arg ${n.name} has changed type from ${String(n.type)} to ${String(s.type)}.`});for(const n of a.added)(0,c.isRequiredArgument)(n)?o.push({type:r.REQUIRED_ARG_ADDED,description:`A required arg ${n.name} on ${e.name}.${t.name} was added.`}):o.push({type:i.OPTIONAL_ARG_ADDED,description:`An optional arg ${n.name} on ${e.name}.${t.name} was added.`});return o}function N(e,t){return(0,c.isListType)(e)?(0,c.isListType)(t)&&N(e.ofType,t.ofType)||(0,c.isNonNullType)(t)&&N(e,t.ofType):(0,c.isNonNullType)(e)?(0,c.isNonNullType)(t)&&N(e.ofType,t.ofType):(0,c.isNamedType)(t)&&e.name===t.name||(0,c.isNonNullType)(t)&&N(e,t.ofType)}function O(e,t){return(0,c.isListType)(e)?(0,c.isListType)(t)&&O(e.ofType,t.ofType):(0,c.isNonNullType)(e)?(0,c.isNonNullType)(t)&&O(e.ofType,t.ofType)||!(0,c.isNonNullType)(t)&&O(e.ofType,t):(0,c.isNamedType)(t)&&e.name===t.name}function I(e){return(0,c.isScalarType)(e)?"a Scalar type":(0,c.isObjectType)(e)?"an Object type":(0,c.isInterfaceType)(e)?"an Interface type":(0,c.isUnionType)(e)?"a Union type":(0,c.isEnumType)(e)?"an Enum type":(0,c.isInputObjectType)(e)?"an Input type":void(0,a.invariant)(!1,"Unexpected type: "+(0,o.inspect)(e))}function _(e,t){const n=(0,l.astFromValue)(e,t);return null!=n||(0,a.invariant)(!1),(0,u.print)((0,d.sortValueNode)(n))}function L(e,t){const n=[],r=[],i=[],o=(0,s.keyMap)(e,(({name:e})=>e)),a=(0,s.keyMap)(t,(({name:e})=>e));for(const t of e){const e=a[t.name];void 0===e?r.push(t):i.push([t,e])}for(const e of t)void 0===o[e.name]&&n.push(e);return{added:n,persisted:i,removed:r}}t.BreakingChangeType=r,function(e){e.TYPE_REMOVED="TYPE_REMOVED",e.TYPE_CHANGED_KIND="TYPE_CHANGED_KIND",e.TYPE_REMOVED_FROM_UNION="TYPE_REMOVED_FROM_UNION",e.VALUE_REMOVED_FROM_ENUM="VALUE_REMOVED_FROM_ENUM",e.REQUIRED_INPUT_FIELD_ADDED="REQUIRED_INPUT_FIELD_ADDED",e.IMPLEMENTED_INTERFACE_REMOVED="IMPLEMENTED_INTERFACE_REMOVED",e.FIELD_REMOVED="FIELD_REMOVED",e.FIELD_CHANGED_KIND="FIELD_CHANGED_KIND",e.REQUIRED_ARG_ADDED="REQUIRED_ARG_ADDED",e.ARG_REMOVED="ARG_REMOVED",e.ARG_CHANGED_KIND="ARG_CHANGED_KIND",e.DIRECTIVE_REMOVED="DIRECTIVE_REMOVED",e.DIRECTIVE_ARG_REMOVED="DIRECTIVE_ARG_REMOVED",e.REQUIRED_DIRECTIVE_ARG_ADDED="REQUIRED_DIRECTIVE_ARG_ADDED",e.DIRECTIVE_REPEATABLE_REMOVED="DIRECTIVE_REPEATABLE_REMOVED",e.DIRECTIVE_LOCATION_REMOVED="DIRECTIVE_LOCATION_REMOVED"}(r||(t.BreakingChangeType=r={})),t.DangerousChangeType=i,function(e){e.VALUE_ADDED_TO_ENUM="VALUE_ADDED_TO_ENUM",e.TYPE_ADDED_TO_UNION="TYPE_ADDED_TO_UNION",e.OPTIONAL_INPUT_FIELD_ADDED="OPTIONAL_INPUT_FIELD_ADDED",e.OPTIONAL_ARG_ADDED="OPTIONAL_ARG_ADDED",e.IMPLEMENTED_INTERFACE_ADDED="IMPLEMENTED_INTERFACE_ADDED",e.ARG_DEFAULT_VALUE_CHANGE="ARG_DEFAULT_VALUE_CHANGE"}(i||(t.DangerousChangeType=i={}))},6032:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getIntrospectionQuery=function(e){const t={descriptions:!0,specifiedByUrl:!1,directiveIsRepeatable:!1,schemaDescription:!1,inputValueDeprecation:!1,...e},n=t.descriptions?"description":"",r=t.specifiedByUrl?"specifiedByURL":"",i=t.directiveIsRepeatable?"isRepeatable":"";function o(e){return t.inputValueDeprecation?e:""}return`\n    query IntrospectionQuery {\n      __schema {\n        ${t.schemaDescription?n:""}\n        queryType { name }\n        mutationType { name }\n        subscriptionType { name }\n        types {\n          ...FullType\n        }\n        directives {\n          name\n          ${n}\n          ${i}\n          locations\n          args${o("(includeDeprecated: true)")} {\n            ...InputValue\n          }\n        }\n      }\n    }\n\n    fragment FullType on __Type {\n      kind\n      name\n      ${n}\n      ${r}\n      fields(includeDeprecated: true) {\n        name\n        ${n}\n        args${o("(includeDeprecated: true)")} {\n          ...InputValue\n        }\n        type {\n          ...TypeRef\n        }\n        isDeprecated\n        deprecationReason\n      }\n      inputFields${o("(includeDeprecated: true)")} {\n        ...InputValue\n      }\n      interfaces {\n        ...TypeRef\n      }\n      enumValues(includeDeprecated: true) {\n        name\n        ${n}\n        isDeprecated\n        deprecationReason\n      }\n      possibleTypes {\n        ...TypeRef\n      }\n    }\n\n    fragment InputValue on __InputValue {\n      name\n      ${n}\n      type { ...TypeRef }\n      defaultValue\n      ${o("isDeprecated")}\n      ${o("deprecationReason")}\n    }\n\n    fragment TypeRef on __Type {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n                ofType {\n                  kind\n                  name\n                  ofType {\n                    kind\n                    name\n                    ofType {\n                      kind\n                      name\n                      ofType {\n                        kind\n                        name\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  `}},3810:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getOperationAST=function(e,t){let n=null;for(const o of e.definitions){var i;if(o.kind===r.Kind.OPERATION_DEFINITION)if(null==t){if(n)return null;n=o}else if((null===(i=o.name)||void 0===i?void 0:i.value)===t)return o}return n};var r=n(7030)},4676:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getOperationRootType=function(e,t){if("query"===t.operation){const n=e.getQueryType();if(!n)throw new r.GraphQLError("Schema does not define the required query root type.",{nodes:t});return n}if("mutation"===t.operation){const n=e.getMutationType();if(!n)throw new r.GraphQLError("Schema is not configured for mutations.",{nodes:t});return n}if("subscription"===t.operation){const n=e.getSubscriptionType();if(!n)throw new r.GraphQLError("Schema is not configured for subscriptions.",{nodes:t});return n}throw new r.GraphQLError("Can only have query, mutation and subscription operations.",{nodes:t})};var r=n(1702)},4889:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BreakingChangeType",{enumerable:!0,get:function(){return O.BreakingChangeType}}),Object.defineProperty(t,"DangerousChangeType",{enumerable:!0,get:function(){return O.DangerousChangeType}}),Object.defineProperty(t,"TypeInfo",{enumerable:!0,get:function(){return h.TypeInfo}}),Object.defineProperty(t,"assertValidName",{enumerable:!0,get:function(){return N.assertValidName}}),Object.defineProperty(t,"astFromValue",{enumerable:!0,get:function(){return m.astFromValue}}),Object.defineProperty(t,"buildASTSchema",{enumerable:!0,get:function(){return u.buildASTSchema}}),Object.defineProperty(t,"buildClientSchema",{enumerable:!0,get:function(){return s.buildClientSchema}}),Object.defineProperty(t,"buildSchema",{enumerable:!0,get:function(){return u.buildSchema}}),Object.defineProperty(t,"coerceInputValue",{enumerable:!0,get:function(){return T.coerceInputValue}}),Object.defineProperty(t,"concatAST",{enumerable:!0,get:function(){return b.concatAST}}),Object.defineProperty(t,"doTypesOverlap",{enumerable:!0,get:function(){return E.doTypesOverlap}}),Object.defineProperty(t,"extendSchema",{enumerable:!0,get:function(){return c.extendSchema}}),Object.defineProperty(t,"findBreakingChanges",{enumerable:!0,get:function(){return O.findBreakingChanges}}),Object.defineProperty(t,"findDangerousChanges",{enumerable:!0,get:function(){return O.findDangerousChanges}}),Object.defineProperty(t,"getIntrospectionQuery",{enumerable:!0,get:function(){return r.getIntrospectionQuery}}),Object.defineProperty(t,"getOperationAST",{enumerable:!0,get:function(){return i.getOperationAST}}),Object.defineProperty(t,"getOperationRootType",{enumerable:!0,get:function(){return o.getOperationRootType}}),Object.defineProperty(t,"introspectionFromSchema",{enumerable:!0,get:function(){return a.introspectionFromSchema}}),Object.defineProperty(t,"isEqualType",{enumerable:!0,get:function(){return E.isEqualType}}),Object.defineProperty(t,"isTypeSubTypeOf",{enumerable:!0,get:function(){return E.isTypeSubTypeOf}}),Object.defineProperty(t,"isValidNameError",{enumerable:!0,get:function(){return N.isValidNameError}}),Object.defineProperty(t,"lexicographicSortSchema",{enumerable:!0,get:function(){return p.lexicographicSortSchema}}),Object.defineProperty(t,"printIntrospectionSchema",{enumerable:!0,get:function(){return l.printIntrospectionSchema}}),Object.defineProperty(t,"printSchema",{enumerable:!0,get:function(){return l.printSchema}}),Object.defineProperty(t,"printType",{enumerable:!0,get:function(){return l.printType}}),Object.defineProperty(t,"separateOperations",{enumerable:!0,get:function(){return v.separateOperations}}),Object.defineProperty(t,"stripIgnoredCharacters",{enumerable:!0,get:function(){return g.stripIgnoredCharacters}}),Object.defineProperty(t,"typeFromAST",{enumerable:!0,get:function(){return d.typeFromAST}}),Object.defineProperty(t,"valueFromAST",{enumerable:!0,get:function(){return f.valueFromAST}}),Object.defineProperty(t,"valueFromASTUntyped",{enumerable:!0,get:function(){return y.valueFromASTUntyped}}),Object.defineProperty(t,"visitWithTypeInfo",{enumerable:!0,get:function(){return h.visitWithTypeInfo}});var r=n(6032),i=n(3810),o=n(4676),a=n(5197),s=n(6613),u=n(434),c=n(1442),p=n(7460),l=n(2254),d=n(6693),f=n(2302),y=n(8805),m=n(8096),h=n(7485),T=n(4090),b=n(3129),v=n(1070),g=n(8401),E=n(3448),N=n(8426),O=n(3666)},5197:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.introspectionFromSchema=function(e,t){const n={specifiedByUrl:!0,directiveIsRepeatable:!0,schemaDescription:!0,inputValueDeprecation:!0,...t},s=(0,i.parse)((0,a.getIntrospectionQuery)(n)),u=(0,o.executeSync)({schema:e,document:s});return!u.errors&&u.data||(0,r.invariant)(!1),u.data};var r=n(1321),i=n(246),o=n(6892),a=n(6032)},7460:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.lexicographicSortSchema=function(e){const t=e.toConfig(),n=(0,o.keyValMap)(d(t.types),(e=>e.name),(function(e){if((0,s.isScalarType)(e)||(0,c.isIntrospectionType)(e))return e;if((0,s.isObjectType)(e)){const t=e.toConfig();return new s.GraphQLObjectType({...t,interfaces:()=>b(t.interfaces),fields:()=>T(t.fields)})}if((0,s.isInterfaceType)(e)){const t=e.toConfig();return new s.GraphQLInterfaceType({...t,interfaces:()=>b(t.interfaces),fields:()=>T(t.fields)})}if((0,s.isUnionType)(e)){const t=e.toConfig();return new s.GraphQLUnionType({...t,types:()=>b(t.types)})}if((0,s.isEnumType)(e)){const t=e.toConfig();return new s.GraphQLEnumType({...t,values:l(t.values,(e=>e))})}if((0,s.isInputObjectType)(e)){const t=e.toConfig();return new s.GraphQLInputObjectType({...t,fields:()=>l(t.fields,(e=>({...e,type:a(e.type)})))})}(0,i.invariant)(!1,"Unexpected type: "+(0,r.inspect)(e))}));return new p.GraphQLSchema({...t,types:Object.values(n),directives:d(t.directives).map((function(e){const t=e.toConfig();return new u.GraphQLDirective({...t,locations:f(t.locations,(e=>e)),args:h(t.args)})})),query:m(t.query),mutation:m(t.mutation),subscription:m(t.subscription)});function a(e){return(0,s.isListType)(e)?new s.GraphQLList(a(e.ofType)):(0,s.isNonNullType)(e)?new s.GraphQLNonNull(a(e.ofType)):y(e)}function y(e){return n[e.name]}function m(e){return e&&y(e)}function h(e){return l(e,(e=>({...e,type:a(e.type)})))}function T(e){return l(e,(e=>({...e,type:a(e.type),args:e.args&&h(e.args)})))}function b(e){return d(e).map(y)}};var r=n(9657),i=n(1321),o=n(5785),a=n(5745),s=n(3754),u=n(8685),c=n(8364),p=n(4648);function l(e,t){const n=Object.create(null);for(const r of Object.keys(e).sort(a.naturalCompare))n[r]=t(e[r]);return n}function d(e){return f(e,(e=>e.name))}function f(e,t){return e.slice().sort(((e,n)=>{const r=t(e),i=t(n);return(0,a.naturalCompare)(r,i)}))}},2254:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.printIntrospectionSchema=function(e){return y(e,c.isSpecifiedDirective,p.isIntrospectionType)},t.printSchema=function(e){return y(e,(e=>!(0,c.isSpecifiedDirective)(e)),f)},t.printType=h;var r=n(9657),i=n(1321),o=n(9165),a=n(7030),s=n(585),u=n(3754),c=n(8685),p=n(8364),l=n(1062),d=n(8096);function f(e){return!(0,l.isSpecifiedScalarType)(e)&&!(0,p.isIntrospectionType)(e)}function y(e,t,n){const r=e.getDirectives().filter(t),i=Object.values(e.getTypeMap()).filter(n);return[m(e),...r.map((e=>function(e){return O(e)+"directive @"+e.name+g(e.args)+(e.isRepeatable?" repeatable":"")+" on "+e.locations.join(" | ")}(e))),...i.map((e=>h(e)))].filter(Boolean).join("\n\n")}function m(e){if(null==e.description&&function(e){const t=e.getQueryType();if(t&&"Query"!==t.name)return!1;const n=e.getMutationType();if(n&&"Mutation"!==n.name)return!1;const r=e.getSubscriptionType();return!r||"Subscription"===r.name}(e))return;const t=[],n=e.getQueryType();n&&t.push(`  query: ${n.name}`);const r=e.getMutationType();r&&t.push(`  mutation: ${r.name}`);const i=e.getSubscriptionType();return i&&t.push(`  subscription: ${i.name}`),O(e)+`schema {\n${t.join("\n")}\n}`}function h(e){return(0,u.isScalarType)(e)?function(e){return O(e)+`scalar ${e.name}`+(null==(t=e).specifiedByURL?"":` @specifiedBy(url: ${(0,s.print)({kind:a.Kind.STRING,value:t.specifiedByURL})})`);var t}(e):(0,u.isObjectType)(e)?function(e){return O(e)+`type ${e.name}`+T(e)+b(e)}(e):(0,u.isInterfaceType)(e)?function(e){return O(e)+`interface ${e.name}`+T(e)+b(e)}(e):(0,u.isUnionType)(e)?function(e){const t=e.getTypes(),n=t.length?" = "+t.join(" | "):"";return O(e)+"union "+e.name+n}(e):(0,u.isEnumType)(e)?function(e){const t=e.getValues().map(((e,t)=>O(e,"  ",!t)+"  "+e.name+N(e.deprecationReason)));return O(e)+`enum ${e.name}`+v(t)}(e):(0,u.isInputObjectType)(e)?function(e){const t=Object.values(e.getFields()).map(((e,t)=>O(e,"  ",!t)+"  "+E(e)));return O(e)+`input ${e.name}`+v(t)}(e):void(0,i.invariant)(!1,"Unexpected type: "+(0,r.inspect)(e))}function T(e){const t=e.getInterfaces();return t.length?" implements "+t.map((e=>e.name)).join(" & "):""}function b(e){return v(Object.values(e.getFields()).map(((e,t)=>O(e,"  ",!t)+"  "+e.name+g(e.args,"  ")+": "+String(e.type)+N(e.deprecationReason))))}function v(e){return 0!==e.length?" {\n"+e.join("\n")+"\n}":""}function g(e,t=""){return 0===e.length?"":e.every((e=>!e.description))?"("+e.map(E).join(", ")+")":"(\n"+e.map(((e,n)=>O(e,"  "+t,!n)+"  "+t+E(e))).join("\n")+"\n"+t+")"}function E(e){const t=(0,d.astFromValue)(e.defaultValue,e.type);let n=e.name+": "+String(e.type);return t&&(n+=` = ${(0,s.print)(t)}`),n+N(e.deprecationReason)}function N(e){return null==e?"":e!==c.DEFAULT_DEPRECATION_REASON?` @deprecated(reason: ${(0,s.print)({kind:a.Kind.STRING,value:e})})`:" @deprecated"}function O(e,t="",n=!0){const{description:r}=e;return null==r?"":(t&&!n?"\n"+t:t)+(0,s.print)({kind:a.Kind.STRING,value:r,block:(0,o.isPrintableAsBlockString)(r)}).replace(/\n/g,"\n"+t)+"\n"}},1070:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.separateOperations=function(e){const t=[],n=Object.create(null);for(const i of e.definitions)switch(i.kind){case r.Kind.OPERATION_DEFINITION:t.push(i);break;case r.Kind.FRAGMENT_DEFINITION:n[i.name.value]=a(i.selectionSet)}const i=Object.create(null);for(const s of t){const t=new Set;for(const e of a(s.selectionSet))o(t,n,e);i[s.name?s.name.value:""]={kind:r.Kind.DOCUMENT,definitions:e.definitions.filter((e=>e===s||e.kind===r.Kind.FRAGMENT_DEFINITION&&t.has(e.name.value)))}}return i};var r=n(7030),i=n(9111);function o(e,t,n){if(!e.has(n)){e.add(n);const r=t[n];if(void 0!==r)for(const n of r)o(e,t,n)}}function a(e){const t=[];return(0,i.visit)(e,{FragmentSpread(e){t.push(e.name.value)}}),t}},1152:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.sortValueNode=function e(t){switch(t.kind){case i.Kind.OBJECT:return{...t,fields:(n=t.fields,n.map((t=>({...t,value:e(t.value)}))).sort(((e,t)=>(0,r.naturalCompare)(e.name.value,t.name.value))))};case i.Kind.LIST:return{...t,values:t.values.map(e)};case i.Kind.INT:case i.Kind.FLOAT:case i.Kind.STRING:case i.Kind.BOOLEAN:case i.Kind.NULL:case i.Kind.ENUM:case i.Kind.VARIABLE:return t}var n};var r=n(5745),i=n(7030)},8401:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.stripIgnoredCharacters=function(e){const t=(0,o.isSource)(e)?e:new o.Source(e),n=t.body,s=new i.Lexer(t);let u="",c=!1;for(;s.advance().kind!==a.TokenKind.EOF;){const e=s.token,t=e.kind,o=!(0,i.isPunctuatorTokenKind)(e.kind);c&&(o||e.kind===a.TokenKind.SPREAD)&&(u+=" ");const p=n.slice(e.start,e.end);t===a.TokenKind.BLOCK_STRING?u+=(0,r.printBlockString)(e.value,{minimize:!0}):u+=p,c=o}return u};var r=n(9165),i=n(6083),o=n(6876),a=n(3038)},3448:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.doTypesOverlap=function(e,t,n){return t===n||((0,r.isAbstractType)(t)?(0,r.isAbstractType)(n)?e.getPossibleTypes(t).some((t=>e.isSubType(n,t))):e.isSubType(t,n):!!(0,r.isAbstractType)(n)&&e.isSubType(n,t))},t.isEqualType=function e(t,n){return t===n||((0,r.isNonNullType)(t)&&(0,r.isNonNullType)(n)||!(!(0,r.isListType)(t)||!(0,r.isListType)(n)))&&e(t.ofType,n.ofType)},t.isTypeSubTypeOf=function e(t,n,i){return n===i||((0,r.isNonNullType)(i)?!!(0,r.isNonNullType)(n)&&e(t,n.ofType,i.ofType):(0,r.isNonNullType)(n)?e(t,n.ofType,i):(0,r.isListType)(i)?!!(0,r.isListType)(n)&&e(t,n.ofType,i.ofType):!(0,r.isListType)(n)&&((0,r.isAbstractType)(i)&&((0,r.isInterfaceType)(n)||(0,r.isObjectType)(n))&&t.isSubType(i,n)))};var r=n(3754)},6693:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.typeFromAST=function e(t,n){switch(n.kind){case r.Kind.LIST_TYPE:{const r=e(t,n.type);return r&&new i.GraphQLList(r)}case r.Kind.NON_NULL_TYPE:{const r=e(t,n.type);return r&&new i.GraphQLNonNull(r)}case r.Kind.NAMED_TYPE:return t.getType(n.name.value)}};var r=n(7030),i=n(3754)},2302:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.valueFromAST=function e(t,n,c){if(t){if(t.kind===a.Kind.VARIABLE){const e=t.name.value;if(null==c||void 0===c[e])return;const r=c[e];if(null===r&&(0,s.isNonNullType)(n))return;return r}if((0,s.isNonNullType)(n)){if(t.kind===a.Kind.NULL)return;return e(t,n.ofType,c)}if(t.kind===a.Kind.NULL)return null;if((0,s.isListType)(n)){const r=n.ofType;if(t.kind===a.Kind.LIST){const n=[];for(const i of t.values)if(u(i,c)){if((0,s.isNonNullType)(r))return;n.push(null)}else{const t=e(i,r,c);if(void 0===t)return;n.push(t)}return n}const i=e(t,r,c);if(void 0===i)return;return[i]}if((0,s.isInputObjectType)(n)){if(t.kind!==a.Kind.OBJECT)return;const r=Object.create(null),i=(0,o.keyMap)(t.fields,(e=>e.name.value));for(const t of Object.values(n.getFields())){const n=i[t.name];if(!n||u(n.value,c)){if(void 0!==t.defaultValue)r[t.name]=t.defaultValue;else if((0,s.isNonNullType)(t.type))return;continue}const o=e(n.value,t.type,c);if(void 0===o)return;r[t.name]=o}return r}if((0,s.isLeafType)(n)){let e;try{e=n.parseLiteral(t,c)}catch(e){return}if(void 0===e)return;return e}(0,i.invariant)(!1,"Unexpected input type: "+(0,r.inspect)(n))}};var r=n(9657),i=n(1321),o=n(4590),a=n(7030),s=n(3754);function u(e,t){return e.kind===a.Kind.VARIABLE&&(null==t||void 0===t[e.name.value])}},8805:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.valueFromASTUntyped=function e(t,n){switch(t.kind){case i.Kind.NULL:return null;case i.Kind.INT:return parseInt(t.value,10);case i.Kind.FLOAT:return parseFloat(t.value);case i.Kind.STRING:case i.Kind.ENUM:case i.Kind.BOOLEAN:return t.value;case i.Kind.LIST:return t.values.map((t=>e(t,n)));case i.Kind.OBJECT:return(0,r.keyValMap)(t.fields,(e=>e.name.value),(t=>e(t.value,n)));case i.Kind.VARIABLE:return null==n?void 0:n[t.name.value]}};var r=n(5785),i=n(7030)},4782:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValidationContext=t.SDLValidationContext=t.ASTValidationContext=void 0;var r=n(7030),i=n(9111),o=n(7485);class a{constructor(e,t){this._ast=e,this._fragments=void 0,this._fragmentSpreads=new Map,this._recursivelyReferencedFragments=new Map,this._onError=t}get[Symbol.toStringTag](){return"ASTValidationContext"}reportError(e){this._onError(e)}getDocument(){return this._ast}getFragment(e){let t;if(this._fragments)t=this._fragments;else{t=Object.create(null);for(const e of this.getDocument().definitions)e.kind===r.Kind.FRAGMENT_DEFINITION&&(t[e.name.value]=e);this._fragments=t}return t[e]}getFragmentSpreads(e){let t=this._fragmentSpreads.get(e);if(!t){t=[];const n=[e];let i;for(;i=n.pop();)for(const e of i.selections)e.kind===r.Kind.FRAGMENT_SPREAD?t.push(e):e.selectionSet&&n.push(e.selectionSet);this._fragmentSpreads.set(e,t)}return t}getRecursivelyReferencedFragments(e){let t=this._recursivelyReferencedFragments.get(e);if(!t){t=[];const n=Object.create(null),r=[e.selectionSet];let i;for(;i=r.pop();)for(const e of this.getFragmentSpreads(i)){const i=e.name.value;if(!0!==n[i]){n[i]=!0;const e=this.getFragment(i);e&&(t.push(e),r.push(e.selectionSet))}}this._recursivelyReferencedFragments.set(e,t)}return t}}t.ASTValidationContext=a;class s extends a{constructor(e,t,n){super(e,n),this._schema=t}get[Symbol.toStringTag](){return"SDLValidationContext"}getSchema(){return this._schema}}t.SDLValidationContext=s;class u extends a{constructor(e,t,n,r){super(t,r),this._schema=e,this._typeInfo=n,this._variableUsages=new Map,this._recursiveVariableUsages=new Map}get[Symbol.toStringTag](){return"ValidationContext"}getSchema(){return this._schema}getVariableUsages(e){let t=this._variableUsages.get(e);if(!t){const n=[],r=new o.TypeInfo(this._schema);(0,i.visit)(e,(0,o.visitWithTypeInfo)(r,{VariableDefinition:()=>!1,Variable(e){n.push({node:e,type:r.getInputType(),defaultValue:r.getDefaultValue()})}})),t=n,this._variableUsages.set(e,t)}return t}getRecursiveVariableUsages(e){let t=this._recursiveVariableUsages.get(e);if(!t){t=this.getVariableUsages(e);for(const n of this.getRecursivelyReferencedFragments(e))t=t.concat(this.getVariableUsages(n));this._recursiveVariableUsages.set(e,t)}return t}getType(){return this._typeInfo.getType()}getParentType(){return this._typeInfo.getParentType()}getInputType(){return this._typeInfo.getInputType()}getParentInputType(){return this._typeInfo.getParentInputType()}getFieldDef(){return this._typeInfo.getFieldDef()}getDirective(){return this._typeInfo.getDirective()}getArgument(){return this._typeInfo.getArgument()}getEnumValue(){return this._typeInfo.getEnumValue()}}t.ValidationContext=u},4360:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ExecutableDefinitionsRule",{enumerable:!0,get:function(){return a.ExecutableDefinitionsRule}}),Object.defineProperty(t,"FieldsOnCorrectTypeRule",{enumerable:!0,get:function(){return s.FieldsOnCorrectTypeRule}}),Object.defineProperty(t,"FragmentsOnCompositeTypesRule",{enumerable:!0,get:function(){return u.FragmentsOnCompositeTypesRule}}),Object.defineProperty(t,"KnownArgumentNamesRule",{enumerable:!0,get:function(){return c.KnownArgumentNamesRule}}),Object.defineProperty(t,"KnownDirectivesRule",{enumerable:!0,get:function(){return p.KnownDirectivesRule}}),Object.defineProperty(t,"KnownFragmentNamesRule",{enumerable:!0,get:function(){return l.KnownFragmentNamesRule}}),Object.defineProperty(t,"KnownTypeNamesRule",{enumerable:!0,get:function(){return d.KnownTypeNamesRule}}),Object.defineProperty(t,"LoneAnonymousOperationRule",{enumerable:!0,get:function(){return f.LoneAnonymousOperationRule}}),Object.defineProperty(t,"LoneSchemaDefinitionRule",{enumerable:!0,get:function(){return R.LoneSchemaDefinitionRule}}),Object.defineProperty(t,"NoDeprecatedCustomRule",{enumerable:!0,get:function(){return M.NoDeprecatedCustomRule}}),Object.defineProperty(t,"NoFragmentCyclesRule",{enumerable:!0,get:function(){return y.NoFragmentCyclesRule}}),Object.defineProperty(t,"NoSchemaIntrospectionCustomRule",{enumerable:!0,get:function(){return K.NoSchemaIntrospectionCustomRule}}),Object.defineProperty(t,"NoUndefinedVariablesRule",{enumerable:!0,get:function(){return m.NoUndefinedVariablesRule}}),Object.defineProperty(t,"NoUnusedFragmentsRule",{enumerable:!0,get:function(){return h.NoUnusedFragmentsRule}}),Object.defineProperty(t,"NoUnusedVariablesRule",{enumerable:!0,get:function(){return T.NoUnusedVariablesRule}}),Object.defineProperty(t,"OverlappingFieldsCanBeMergedRule",{enumerable:!0,get:function(){return b.OverlappingFieldsCanBeMergedRule}}),Object.defineProperty(t,"PossibleFragmentSpreadsRule",{enumerable:!0,get:function(){return v.PossibleFragmentSpreadsRule}}),Object.defineProperty(t,"PossibleTypeExtensionsRule",{enumerable:!0,get:function(){return C.PossibleTypeExtensionsRule}}),Object.defineProperty(t,"ProvidedRequiredArgumentsRule",{enumerable:!0,get:function(){return g.ProvidedRequiredArgumentsRule}}),Object.defineProperty(t,"ScalarLeafsRule",{enumerable:!0,get:function(){return E.ScalarLeafsRule}}),Object.defineProperty(t,"SingleFieldSubscriptionsRule",{enumerable:!0,get:function(){return N.SingleFieldSubscriptionsRule}}),Object.defineProperty(t,"UniqueArgumentDefinitionNamesRule",{enumerable:!0,get:function(){return G.UniqueArgumentDefinitionNamesRule}}),Object.defineProperty(t,"UniqueArgumentNamesRule",{enumerable:!0,get:function(){return O.UniqueArgumentNamesRule}}),Object.defineProperty(t,"UniqueDirectiveNamesRule",{enumerable:!0,get:function(){return V.UniqueDirectiveNamesRule}}),Object.defineProperty(t,"UniqueDirectivesPerLocationRule",{enumerable:!0,get:function(){return I.UniqueDirectivesPerLocationRule}}),Object.defineProperty(t,"UniqueEnumValueNamesRule",{enumerable:!0,get:function(){return F.UniqueEnumValueNamesRule}}),Object.defineProperty(t,"UniqueFieldDefinitionNamesRule",{enumerable:!0,get:function(){return x.UniqueFieldDefinitionNamesRule}}),Object.defineProperty(t,"UniqueFragmentNamesRule",{enumerable:!0,get:function(){return _.UniqueFragmentNamesRule}}),Object.defineProperty(t,"UniqueInputFieldNamesRule",{enumerable:!0,get:function(){return L.UniqueInputFieldNamesRule}}),Object.defineProperty(t,"UniqueOperationNamesRule",{enumerable:!0,get:function(){return S.UniqueOperationNamesRule}}),Object.defineProperty(t,"UniqueOperationTypesRule",{enumerable:!0,get:function(){return w.UniqueOperationTypesRule}}),Object.defineProperty(t,"UniqueTypeNamesRule",{enumerable:!0,get:function(){return k.UniqueTypeNamesRule}}),Object.defineProperty(t,"UniqueVariableNamesRule",{enumerable:!0,get:function(){return D.UniqueVariableNamesRule}}),Object.defineProperty(t,"ValidationContext",{enumerable:!0,get:function(){return i.ValidationContext}}),Object.defineProperty(t,"ValuesOfCorrectTypeRule",{enumerable:!0,get:function(){return j.ValuesOfCorrectTypeRule}}),Object.defineProperty(t,"VariablesAreInputTypesRule",{enumerable:!0,get:function(){return A.VariablesAreInputTypesRule}}),Object.defineProperty(t,"VariablesInAllowedPositionRule",{enumerable:!0,get:function(){return P.VariablesInAllowedPositionRule}}),Object.defineProperty(t,"specifiedRules",{enumerable:!0,get:function(){return o.specifiedRules}}),Object.defineProperty(t,"validate",{enumerable:!0,get:function(){return r.validate}});var r=n(9040),i=n(4782),o=n(7283),a=n(2988),s=n(9284),u=n(6514),c=n(7372),p=n(7999),l=n(9093),d=n(5117),f=n(9130),y=n(944),m=n(1350),h=n(6072),T=n(4472),b=n(2457),v=n(6839),g=n(1672),E=n(1843),N=n(3618),O=n(5566),I=n(9529),_=n(7091),L=n(7027),S=n(5988),D=n(3191),j=n(7909),A=n(964),P=n(4281),R=n(502),w=n(105),k=n(3171),F=n(1813),x=n(3084),G=n(1066),V=n(5972),C=n(817),M=n(4555),K=n(5588)},2988:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExecutableDefinitionsRule=function(e){return{Document(t){for(const n of t.definitions)if(!(0,o.isExecutableDefinitionNode)(n)){const t=n.kind===i.Kind.SCHEMA_DEFINITION||n.kind===i.Kind.SCHEMA_EXTENSION?"schema":'"'+n.name.value+'"';e.reportError(new r.GraphQLError(`The ${t} definition is not executable.`,{nodes:n}))}return!1}}};var r=n(1702),i=n(7030),o=n(9187)},9284:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FieldsOnCorrectTypeRule=function(e){return{Field(t){const n=e.getParentType();if(n&&!e.getFieldDef()){const u=e.getSchema(),c=t.name.value;let p=(0,r.didYouMean)("to use an inline fragment on",function(e,t,n){if(!(0,s.isAbstractType)(t))return[];const r=new Set,o=Object.create(null);for(const i of e.getPossibleTypes(t))if(i.getFields()[n]){r.add(i),o[i.name]=1;for(const e of i.getInterfaces()){var a;e.getFields()[n]&&(r.add(e),o[e.name]=(null!==(a=o[e.name])&&void 0!==a?a:0)+1)}}return[...r].sort(((t,n)=>{const r=o[n.name]-o[t.name];return 0!==r?r:(0,s.isInterfaceType)(t)&&e.isSubType(t,n)?-1:(0,s.isInterfaceType)(n)&&e.isSubType(n,t)?1:(0,i.naturalCompare)(t.name,n.name)})).map((e=>e.name))}(u,n,c));""===p&&(p=(0,r.didYouMean)(function(e,t){if((0,s.isObjectType)(e)||(0,s.isInterfaceType)(e)){const n=Object.keys(e.getFields());return(0,o.suggestionList)(t,n)}return[]}(n,c))),e.reportError(new a.GraphQLError(`Cannot query field "${c}" on type "${n.name}".`+p,{nodes:t}))}}}};var r=n(2832),i=n(5745),o=n(1709),a=n(1702),s=n(3754)},6514:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FragmentsOnCompositeTypesRule=function(e){return{InlineFragment(t){const n=t.typeCondition;if(n){const t=(0,a.typeFromAST)(e.getSchema(),n);if(t&&!(0,o.isCompositeType)(t)){const t=(0,i.print)(n);e.reportError(new r.GraphQLError(`Fragment cannot condition on non composite type "${t}".`,{nodes:n}))}}},FragmentDefinition(t){const n=(0,a.typeFromAST)(e.getSchema(),t.typeCondition);if(n&&!(0,o.isCompositeType)(n)){const n=(0,i.print)(t.typeCondition);e.reportError(new r.GraphQLError(`Fragment "${t.name.value}" cannot condition on non composite type "${n}".`,{nodes:t.typeCondition}))}}}};var r=n(1702),i=n(585),o=n(3754),a=n(6693)},7372:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.KnownArgumentNamesOnDirectivesRule=u,t.KnownArgumentNamesRule=function(e){return{...u(e),Argument(t){const n=e.getArgument(),a=e.getFieldDef(),s=e.getParentType();if(!n&&a&&s){const n=t.name.value,u=a.args.map((e=>e.name)),c=(0,i.suggestionList)(n,u);e.reportError(new o.GraphQLError(`Unknown argument "${n}" on field "${s.name}.${a.name}".`+(0,r.didYouMean)(c),{nodes:t}))}}}};var r=n(2832),i=n(1709),o=n(1702),a=n(7030),s=n(8685);function u(e){const t=Object.create(null),n=e.getSchema(),u=n?n.getDirectives():s.specifiedDirectives;for(const e of u)t[e.name]=e.args.map((e=>e.name));const c=e.getDocument().definitions;for(const e of c)if(e.kind===a.Kind.DIRECTIVE_DEFINITION){var p;const n=null!==(p=e.arguments)&&void 0!==p?p:[];t[e.name.value]=n.map((e=>e.name.value))}return{Directive(n){const a=n.name.value,s=t[a];if(n.arguments&&s)for(const t of n.arguments){const n=t.name.value;if(!s.includes(n)){const u=(0,i.suggestionList)(n,s);e.reportError(new o.GraphQLError(`Unknown argument "${n}" on directive "@${a}".`+(0,r.didYouMean)(u),{nodes:t}))}}return!1}}}},7999:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.KnownDirectivesRule=function(e){const t=Object.create(null),n=e.getSchema(),p=n?n.getDirectives():c.specifiedDirectives;for(const e of p)t[e.name]=e.locations;const l=e.getDocument().definitions;for(const e of l)e.kind===u.Kind.DIRECTIVE_DEFINITION&&(t[e.name.value]=e.locations.map((e=>e.value)));return{Directive(n,c,p,l,d){const f=n.name.value,y=t[f];if(!y)return void e.reportError(new o.GraphQLError(`Unknown directive "@${f}".`,{nodes:n}));const m=function(e){const t=e[e.length-1];switch("kind"in t||(0,i.invariant)(!1),t.kind){case u.Kind.OPERATION_DEFINITION:return function(e){switch(e){case a.OperationTypeNode.QUERY:return s.DirectiveLocation.QUERY;case a.OperationTypeNode.MUTATION:return s.DirectiveLocation.MUTATION;case a.OperationTypeNode.SUBSCRIPTION:return s.DirectiveLocation.SUBSCRIPTION}}(t.operation);case u.Kind.FIELD:return s.DirectiveLocation.FIELD;case u.Kind.FRAGMENT_SPREAD:return s.DirectiveLocation.FRAGMENT_SPREAD;case u.Kind.INLINE_FRAGMENT:return s.DirectiveLocation.INLINE_FRAGMENT;case u.Kind.FRAGMENT_DEFINITION:return s.DirectiveLocation.FRAGMENT_DEFINITION;case u.Kind.VARIABLE_DEFINITION:return s.DirectiveLocation.VARIABLE_DEFINITION;case u.Kind.SCHEMA_DEFINITION:case u.Kind.SCHEMA_EXTENSION:return s.DirectiveLocation.SCHEMA;case u.Kind.SCALAR_TYPE_DEFINITION:case u.Kind.SCALAR_TYPE_EXTENSION:return s.DirectiveLocation.SCALAR;case u.Kind.OBJECT_TYPE_DEFINITION:case u.Kind.OBJECT_TYPE_EXTENSION:return s.DirectiveLocation.OBJECT;case u.Kind.FIELD_DEFINITION:return s.DirectiveLocation.FIELD_DEFINITION;case u.Kind.INTERFACE_TYPE_DEFINITION:case u.Kind.INTERFACE_TYPE_EXTENSION:return s.DirectiveLocation.INTERFACE;case u.Kind.UNION_TYPE_DEFINITION:case u.Kind.UNION_TYPE_EXTENSION:return s.DirectiveLocation.UNION;case u.Kind.ENUM_TYPE_DEFINITION:case u.Kind.ENUM_TYPE_EXTENSION:return s.DirectiveLocation.ENUM;case u.Kind.ENUM_VALUE_DEFINITION:return s.DirectiveLocation.ENUM_VALUE;case u.Kind.INPUT_OBJECT_TYPE_DEFINITION:case u.Kind.INPUT_OBJECT_TYPE_EXTENSION:return s.DirectiveLocation.INPUT_OBJECT;case u.Kind.INPUT_VALUE_DEFINITION:{const t=e[e.length-3];return"kind"in t||(0,i.invariant)(!1),t.kind===u.Kind.INPUT_OBJECT_TYPE_DEFINITION?s.DirectiveLocation.INPUT_FIELD_DEFINITION:s.DirectiveLocation.ARGUMENT_DEFINITION}default:(0,i.invariant)(!1,"Unexpected kind: "+(0,r.inspect)(t.kind))}}(d);m&&!y.includes(m)&&e.reportError(new o.GraphQLError(`Directive "@${f}" may not be used on ${m}.`,{nodes:n}))}}};var r=n(9657),i=n(1321),o=n(1702),a=n(6257),s=n(5919),u=n(7030),c=n(8685)},9093:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.KnownFragmentNamesRule=function(e){return{FragmentSpread(t){const n=t.name.value;e.getFragment(n)||e.reportError(new r.GraphQLError(`Unknown fragment "${n}".`,{nodes:t.name}))}}};var r=n(1702)},5117:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.KnownTypeNamesRule=function(e){const t=e.getSchema(),n=t?t.getTypeMap():Object.create(null),s=Object.create(null);for(const t of e.getDocument().definitions)(0,a.isTypeDefinitionNode)(t)&&(s[t.name.value]=!0);const c=[...Object.keys(n),...Object.keys(s)];return{NamedType(t,p,l,d,f){const y=t.name.value;if(!n[y]&&!s[y]){var m;const n=null!==(m=f[2])&&void 0!==m?m:l,s=null!=n&&"kind"in(h=n)&&((0,a.isTypeSystemDefinitionNode)(h)||(0,a.isTypeSystemExtensionNode)(h));if(s&&u.includes(y))return;const p=(0,i.suggestionList)(y,s?u.concat(c):c);e.reportError(new o.GraphQLError(`Unknown type "${y}".`+(0,r.didYouMean)(p),{nodes:t}))}var h}}};var r=n(2832),i=n(1709),o=n(1702),a=n(9187),s=n(8364);const u=[...n(1062).specifiedScalarTypes,...s.introspectionTypes].map((e=>e.name))},9130:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.LoneAnonymousOperationRule=function(e){let t=0;return{Document(e){t=e.definitions.filter((e=>e.kind===i.Kind.OPERATION_DEFINITION)).length},OperationDefinition(n){!n.name&&t>1&&e.reportError(new r.GraphQLError("This anonymous operation must be the only defined operation.",{nodes:n}))}}};var r=n(1702),i=n(7030)},502:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.LoneSchemaDefinitionRule=function(e){var t,n,i;const o=e.getSchema(),a=null!==(t=null!==(n=null!==(i=null==o?void 0:o.astNode)&&void 0!==i?i:null==o?void 0:o.getQueryType())&&void 0!==n?n:null==o?void 0:o.getMutationType())&&void 0!==t?t:null==o?void 0:o.getSubscriptionType();let s=0;return{SchemaDefinition(t){a?e.reportError(new r.GraphQLError("Cannot define a new schema within a schema extension.",{nodes:t})):(s>0&&e.reportError(new r.GraphQLError("Must provide only one schema definition.",{nodes:t})),++s)}}};var r=n(1702)},944:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoFragmentCyclesRule=function(e){const t=Object.create(null),n=[],i=Object.create(null);return{OperationDefinition:()=>!1,FragmentDefinition:e=>(o(e),!1)};function o(a){if(t[a.name.value])return;const s=a.name.value;t[s]=!0;const u=e.getFragmentSpreads(a.selectionSet);if(0!==u.length){i[s]=n.length;for(const t of u){const a=t.name.value,s=i[a];if(n.push(t),void 0===s){const t=e.getFragment(a);t&&o(t)}else{const t=n.slice(s),i=t.slice(0,-1).map((e=>'"'+e.name.value+'"')).join(", ");e.reportError(new r.GraphQLError(`Cannot spread fragment "${a}" within itself`+(""!==i?` via ${i}.`:"."),{nodes:t}))}n.pop()}i[s]=void 0}}};var r=n(1702)},1350:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoUndefinedVariablesRule=function(e){let t=Object.create(null);return{OperationDefinition:{enter(){t=Object.create(null)},leave(n){const i=e.getRecursiveVariableUsages(n);for(const{node:o}of i){const i=o.name.value;!0!==t[i]&&e.reportError(new r.GraphQLError(n.name?`Variable "$${i}" is not defined by operation "${n.name.value}".`:`Variable "$${i}" is not defined.`,{nodes:[o,n]}))}}},VariableDefinition(e){t[e.variable.name.value]=!0}}};var r=n(1702)},6072:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoUnusedFragmentsRule=function(e){const t=[],n=[];return{OperationDefinition:e=>(t.push(e),!1),FragmentDefinition:e=>(n.push(e),!1),Document:{leave(){const i=Object.create(null);for(const n of t)for(const t of e.getRecursivelyReferencedFragments(n))i[t.name.value]=!0;for(const t of n){const n=t.name.value;!0!==i[n]&&e.reportError(new r.GraphQLError(`Fragment "${n}" is never used.`,{nodes:t}))}}}}};var r=n(1702)},4472:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoUnusedVariablesRule=function(e){let t=[];return{OperationDefinition:{enter(){t=[]},leave(n){const i=Object.create(null),o=e.getRecursiveVariableUsages(n);for(const{node:e}of o)i[e.name.value]=!0;for(const o of t){const t=o.variable.name.value;!0!==i[t]&&e.reportError(new r.GraphQLError(n.name?`Variable "$${t}" is never used in operation "${n.name.value}".`:`Variable "$${t}" is never used.`,{nodes:o}))}}},VariableDefinition(e){t.push(e)}}};var r=n(1702)},2457:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.OverlappingFieldsCanBeMergedRule=function(e){const t=new g,n=new Map;return{SelectionSet(r){const o=function(e,t,n,r,i){const o=[],[a,s]=T(e,t,r,i);if(function(e,t,n,r,i){for(const[o,a]of Object.entries(i))if(a.length>1)for(let i=0;i<a.length;i++)for(let s=i+1;s<a.length;s++){const u=y(e,n,r,!1,o,a[i],a[s]);u&&t.push(u)}}(e,o,t,n,a),0!==s.length)for(let r=0;r<s.length;r++){l(e,o,t,n,!1,a,s[r]);for(let i=r+1;i<s.length;i++)d(e,o,t,n,!1,s[r],s[i])}return o}(e,n,t,e.getParentType(),r);for(const[[t,n],r,a]of o){const o=p(n);e.reportError(new i.GraphQLError(`Fields "${t}" conflict because ${o}. Use different aliases on the fields to fetch both if this was intentional.`,{nodes:r.concat(a)}))}}}};var r=n(9657),i=n(1702),o=n(7030),a=n(585),s=n(3754),u=n(1152),c=n(6693);function p(e){return Array.isArray(e)?e.map((([e,t])=>`subfields "${e}" conflict because `+p(t))).join(" and "):e}function l(e,t,n,r,i,o,a){const s=e.getFragment(a);if(!s)return;const[u,c]=b(e,n,s);if(o!==u){f(e,t,n,r,i,o,u);for(const s of c)r.has(s,a,i)||(r.add(s,a,i),l(e,t,n,r,i,o,s))}}function d(e,t,n,r,i,o,a){if(o===a)return;if(r.has(o,a,i))return;r.add(o,a,i);const s=e.getFragment(o),u=e.getFragment(a);if(!s||!u)return;const[c,p]=b(e,n,s),[l,y]=b(e,n,u);f(e,t,n,r,i,c,l);for(const a of y)d(e,t,n,r,i,o,a);for(const o of p)d(e,t,n,r,i,o,a)}function f(e,t,n,r,i,o,a){for(const[s,u]of Object.entries(o)){const o=a[s];if(o)for(const a of u)for(const u of o){const o=y(e,n,r,i,s,a,u);o&&t.push(o)}}}function y(e,t,n,i,o,a,u){const[c,p,y]=a,[b,v,g]=u,E=i||c!==b&&(0,s.isObjectType)(c)&&(0,s.isObjectType)(b);if(!E){const e=p.name.value,t=v.name.value;if(e!==t)return[[o,`"${e}" and "${t}" are different fields`],[p],[v]];if(!function(e,t){const n=e.arguments,r=t.arguments;if(void 0===n||0===n.length)return void 0===r||0===r.length;if(void 0===r||0===r.length)return!1;if(n.length!==r.length)return!1;const i=new Map(r.map((({name:e,value:t})=>[e.value,t])));return n.every((e=>{const t=e.value,n=i.get(e.name.value);return void 0!==n&&m(t)===m(n)}))}(p,v))return[[o,"they have differing arguments"],[p],[v]]}const N=null==y?void 0:y.type,O=null==g?void 0:g.type;if(N&&O&&h(N,O))return[[o,`they return conflicting types "${(0,r.inspect)(N)}" and "${(0,r.inspect)(O)}"`],[p],[v]];const I=p.selectionSet,_=v.selectionSet;if(I&&_){const r=function(e,t,n,r,i,o,a,s){const u=[],[c,p]=T(e,t,i,o),[y,m]=T(e,t,a,s);f(e,u,t,n,r,c,y);for(const i of m)l(e,u,t,n,r,c,i);for(const i of p)l(e,u,t,n,r,y,i);for(const i of p)for(const o of m)d(e,u,t,n,r,i,o);return u}(e,t,n,E,(0,s.getNamedType)(N),I,(0,s.getNamedType)(O),_);return function(e,t,n,r){if(e.length>0)return[[t,e.map((([e])=>e))],[n,...e.map((([,e])=>e)).flat()],[r,...e.map((([,,e])=>e)).flat()]]}(r,o,p,v)}}function m(e){return(0,a.print)((0,u.sortValueNode)(e))}function h(e,t){return(0,s.isListType)(e)?!(0,s.isListType)(t)||h(e.ofType,t.ofType):!!(0,s.isListType)(t)||((0,s.isNonNullType)(e)?!(0,s.isNonNullType)(t)||h(e.ofType,t.ofType):!!(0,s.isNonNullType)(t)||!(!(0,s.isLeafType)(e)&&!(0,s.isLeafType)(t))&&e!==t)}function T(e,t,n,r){const i=t.get(r);if(i)return i;const o=Object.create(null),a=Object.create(null);v(e,n,r,o,a);const s=[o,Object.keys(a)];return t.set(r,s),s}function b(e,t,n){const r=t.get(n.selectionSet);if(r)return r;const i=(0,c.typeFromAST)(e.getSchema(),n.typeCondition);return T(e,t,i,n.selectionSet)}function v(e,t,n,r,i){for(const a of n.selections)switch(a.kind){case o.Kind.FIELD:{const e=a.name.value;let n;((0,s.isObjectType)(t)||(0,s.isInterfaceType)(t))&&(n=t.getFields()[e]);const i=a.alias?a.alias.value:e;r[i]||(r[i]=[]),r[i].push([t,a,n]);break}case o.Kind.FRAGMENT_SPREAD:i[a.name.value]=!0;break;case o.Kind.INLINE_FRAGMENT:{const n=a.typeCondition,o=n?(0,c.typeFromAST)(e.getSchema(),n):t;v(e,o,a.selectionSet,r,i);break}}}class g{constructor(){this._data=new Map}has(e,t,n){var r;const[i,o]=e<t?[e,t]:[t,e],a=null===(r=this._data.get(i))||void 0===r?void 0:r.get(o);return void 0!==a&&(!!n||n===a)}add(e,t,n){const[r,i]=e<t?[e,t]:[t,e],o=this._data.get(r);void 0===o?this._data.set(r,new Map([[i,n]])):o.set(i,n)}}},6839:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PossibleFragmentSpreadsRule=function(e){return{InlineFragment(t){const n=e.getType(),s=e.getParentType();if((0,o.isCompositeType)(n)&&(0,o.isCompositeType)(s)&&!(0,a.doTypesOverlap)(e.getSchema(),n,s)){const o=(0,r.inspect)(s),a=(0,r.inspect)(n);e.reportError(new i.GraphQLError(`Fragment cannot be spread here as objects of type "${o}" can never be of type "${a}".`,{nodes:t}))}},FragmentSpread(t){const n=t.name.value,u=function(e,t){const n=e.getFragment(t);if(n){const t=(0,s.typeFromAST)(e.getSchema(),n.typeCondition);if((0,o.isCompositeType)(t))return t}}(e,n),c=e.getParentType();if(u&&c&&!(0,a.doTypesOverlap)(e.getSchema(),u,c)){const o=(0,r.inspect)(c),a=(0,r.inspect)(u);e.reportError(new i.GraphQLError(`Fragment "${n}" cannot be spread here as objects of type "${o}" can never be of type "${a}".`,{nodes:t}))}}}};var r=n(9657),i=n(1702),o=n(3754),a=n(3448),s=n(6693)},817:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PossibleTypeExtensionsRule=function(e){const t=e.getSchema(),n=Object.create(null);for(const t of e.getDocument().definitions)(0,c.isTypeDefinitionNode)(t)&&(n[t.name.value]=t);return{ScalarTypeExtension:d,ObjectTypeExtension:d,InterfaceTypeExtension:d,UnionTypeExtension:d,EnumTypeExtension:d,InputObjectTypeExtension:d};function d(c){const d=c.name.value,f=n[d],y=null==t?void 0:t.getType(d);let m;if(f?m=l[f.kind]:y&&(h=y,m=(0,p.isScalarType)(h)?u.Kind.SCALAR_TYPE_EXTENSION:(0,p.isObjectType)(h)?u.Kind.OBJECT_TYPE_EXTENSION:(0,p.isInterfaceType)(h)?u.Kind.INTERFACE_TYPE_EXTENSION:(0,p.isUnionType)(h)?u.Kind.UNION_TYPE_EXTENSION:(0,p.isEnumType)(h)?u.Kind.ENUM_TYPE_EXTENSION:(0,p.isInputObjectType)(h)?u.Kind.INPUT_OBJECT_TYPE_EXTENSION:void(0,o.invariant)(!1,"Unexpected type: "+(0,i.inspect)(h))),m){if(m!==c.kind){const t=function(e){switch(e){case u.Kind.SCALAR_TYPE_EXTENSION:return"scalar";case u.Kind.OBJECT_TYPE_EXTENSION:return"object";case u.Kind.INTERFACE_TYPE_EXTENSION:return"interface";case u.Kind.UNION_TYPE_EXTENSION:return"union";case u.Kind.ENUM_TYPE_EXTENSION:return"enum";case u.Kind.INPUT_OBJECT_TYPE_EXTENSION:return"input object";default:(0,o.invariant)(!1,"Unexpected kind: "+(0,i.inspect)(e))}}(c.kind);e.reportError(new s.GraphQLError(`Cannot extend non-${t} type "${d}".`,{nodes:f?[f,c]:c}))}}else{const i=Object.keys({...n,...null==t?void 0:t.getTypeMap()}),o=(0,a.suggestionList)(d,i);e.reportError(new s.GraphQLError(`Cannot extend type "${d}" because it is not defined.`+(0,r.didYouMean)(o),{nodes:c.name}))}var h}};var r=n(2832),i=n(9657),o=n(1321),a=n(1709),s=n(1702),u=n(7030),c=n(9187),p=n(3754);const l={[u.Kind.SCALAR_TYPE_DEFINITION]:u.Kind.SCALAR_TYPE_EXTENSION,[u.Kind.OBJECT_TYPE_DEFINITION]:u.Kind.OBJECT_TYPE_EXTENSION,[u.Kind.INTERFACE_TYPE_DEFINITION]:u.Kind.INTERFACE_TYPE_EXTENSION,[u.Kind.UNION_TYPE_DEFINITION]:u.Kind.UNION_TYPE_EXTENSION,[u.Kind.ENUM_TYPE_DEFINITION]:u.Kind.ENUM_TYPE_EXTENSION,[u.Kind.INPUT_OBJECT_TYPE_DEFINITION]:u.Kind.INPUT_OBJECT_TYPE_EXTENSION}},1672:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProvidedRequiredArgumentsOnDirectivesRule=p,t.ProvidedRequiredArgumentsRule=function(e){return{...p(e),Field:{leave(t){var n;const i=e.getFieldDef();if(!i)return!1;const a=new Set(null===(n=t.arguments)||void 0===n?void 0:n.map((e=>e.name.value)));for(const n of i.args)if(!a.has(n.name)&&(0,u.isRequiredArgument)(n)){const a=(0,r.inspect)(n.type);e.reportError(new o.GraphQLError(`Field "${i.name}" argument "${n.name}" of type "${a}" is required, but it was not provided.`,{nodes:t}))}}}}};var r=n(9657),i=n(4590),o=n(1702),a=n(7030),s=n(585),u=n(3754),c=n(8685);function p(e){var t;const n=Object.create(null),p=e.getSchema(),d=null!==(t=null==p?void 0:p.getDirectives())&&void 0!==t?t:c.specifiedDirectives;for(const e of d)n[e.name]=(0,i.keyMap)(e.args.filter(u.isRequiredArgument),(e=>e.name));const f=e.getDocument().definitions;for(const e of f)if(e.kind===a.Kind.DIRECTIVE_DEFINITION){var y;const t=null!==(y=e.arguments)&&void 0!==y?y:[];n[e.name.value]=(0,i.keyMap)(t.filter(l),(e=>e.name.value))}return{Directive:{leave(t){const i=t.name.value,a=n[i];if(a){var c;const n=null!==(c=t.arguments)&&void 0!==c?c:[],p=new Set(n.map((e=>e.name.value)));for(const[n,c]of Object.entries(a))if(!p.has(n)){const a=(0,u.isType)(c.type)?(0,r.inspect)(c.type):(0,s.print)(c.type);e.reportError(new o.GraphQLError(`Directive "@${i}" argument "${n}" of type "${a}" is required, but it was not provided.`,{nodes:t}))}}}}}}function l(e){return e.type.kind===a.Kind.NON_NULL_TYPE&&null==e.defaultValue}},1843:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ScalarLeafsRule=function(e){return{Field(t){const n=e.getType(),a=t.selectionSet;if(n)if((0,o.isLeafType)((0,o.getNamedType)(n))){if(a){const o=t.name.value,s=(0,r.inspect)(n);e.reportError(new i.GraphQLError(`Field "${o}" must not have a selection since type "${s}" has no subfields.`,{nodes:a}))}}else if(!a){const o=t.name.value,a=(0,r.inspect)(n);e.reportError(new i.GraphQLError(`Field "${o}" of type "${a}" must have a selection of subfields. Did you mean "${o} { ... }"?`,{nodes:t}))}}}};var r=n(9657),i=n(1702),o=n(3754)},3618:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SingleFieldSubscriptionsRule=function(e){return{OperationDefinition(t){if("subscription"===t.operation){const n=e.getSchema(),a=n.getSubscriptionType();if(a){const s=t.name?t.name.value:null,u=Object.create(null),c=e.getDocument(),p=Object.create(null);for(const e of c.definitions)e.kind===i.Kind.FRAGMENT_DEFINITION&&(p[e.name.value]=e);const l=(0,o.collectFields)(n,p,u,a,t.selectionSet);if(l.size>1){const t=[...l.values()].slice(1).flat();e.reportError(new r.GraphQLError(null!=s?`Subscription "${s}" must select only one top level field.`:"Anonymous Subscription must select only one top level field.",{nodes:t}))}for(const t of l.values())t[0].name.value.startsWith("__")&&e.reportError(new r.GraphQLError(null!=s?`Subscription "${s}" must not select an introspection top level field.`:"Anonymous Subscription must not select an introspection top level field.",{nodes:t}))}}}}};var r=n(1702),i=n(7030),o=n(1516)},1066:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueArgumentDefinitionNamesRule=function(e){return{DirectiveDefinition(e){var t;const r=null!==(t=e.arguments)&&void 0!==t?t:[];return n(`@${e.name.value}`,r)},InterfaceTypeDefinition:t,InterfaceTypeExtension:t,ObjectTypeDefinition:t,ObjectTypeExtension:t};function t(e){var t;const r=e.name.value,i=null!==(t=e.fields)&&void 0!==t?t:[];for(const e of i){var o;n(`${r}.${e.name.value}`,null!==(o=e.arguments)&&void 0!==o?o:[])}return!1}function n(t,n){const o=(0,r.groupBy)(n,(e=>e.name.value));for(const[n,r]of o)r.length>1&&e.reportError(new i.GraphQLError(`Argument "${t}(${n}:)" can only be defined once.`,{nodes:r.map((e=>e.name))}));return!1}};var r=n(4947),i=n(1702)},5566:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueArgumentNamesRule=function(e){return{Field:t,Directive:t};function t(t){var n;const o=null!==(n=t.arguments)&&void 0!==n?n:[],a=(0,r.groupBy)(o,(e=>e.name.value));for(const[t,n]of a)n.length>1&&e.reportError(new i.GraphQLError(`There can be only one argument named "${t}".`,{nodes:n.map((e=>e.name))}))}};var r=n(4947),i=n(1702)},5972:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueDirectiveNamesRule=function(e){const t=Object.create(null),n=e.getSchema();return{DirectiveDefinition(i){const o=i.name.value;if(null==n||!n.getDirective(o))return t[o]?e.reportError(new r.GraphQLError(`There can be only one directive named "@${o}".`,{nodes:[t[o],i.name]})):t[o]=i.name,!1;e.reportError(new r.GraphQLError(`Directive "@${o}" already exists in the schema. It cannot be redefined.`,{nodes:i.name}))}}};var r=n(1702)},9529:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueDirectivesPerLocationRule=function(e){const t=Object.create(null),n=e.getSchema(),s=n?n.getDirectives():a.specifiedDirectives;for(const e of s)t[e.name]=!e.isRepeatable;const u=e.getDocument().definitions;for(const e of u)e.kind===i.Kind.DIRECTIVE_DEFINITION&&(t[e.name.value]=!e.repeatable);const c=Object.create(null),p=Object.create(null);return{enter(n){if(!("directives"in n)||!n.directives)return;let a;if(n.kind===i.Kind.SCHEMA_DEFINITION||n.kind===i.Kind.SCHEMA_EXTENSION)a=c;else if((0,o.isTypeDefinitionNode)(n)||(0,o.isTypeExtensionNode)(n)){const e=n.name.value;a=p[e],void 0===a&&(p[e]=a=Object.create(null))}else a=Object.create(null);for(const i of n.directives){const n=i.name.value;t[n]&&(a[n]?e.reportError(new r.GraphQLError(`The directive "@${n}" can only be used once at this location.`,{nodes:[a[n],i]})):a[n]=i)}}}};var r=n(1702),i=n(7030),o=n(9187),a=n(8685)},1813:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueEnumValueNamesRule=function(e){const t=e.getSchema(),n=t?t.getTypeMap():Object.create(null),o=Object.create(null);return{EnumTypeDefinition:a,EnumTypeExtension:a};function a(t){var a;const s=t.name.value;o[s]||(o[s]=Object.create(null));const u=null!==(a=t.values)&&void 0!==a?a:[],c=o[s];for(const t of u){const o=t.name.value,a=n[s];(0,i.isEnumType)(a)&&a.getValue(o)?e.reportError(new r.GraphQLError(`Enum value "${s}.${o}" already exists in the schema. It cannot also be defined in this type extension.`,{nodes:t.name})):c[o]?e.reportError(new r.GraphQLError(`Enum value "${s}.${o}" can only be defined once.`,{nodes:[c[o],t.name]})):c[o]=t.name}return!1}};var r=n(1702),i=n(3754)},3084:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueFieldDefinitionNamesRule=function(e){const t=e.getSchema(),n=t?t.getTypeMap():Object.create(null),i=Object.create(null);return{InputObjectTypeDefinition:a,InputObjectTypeExtension:a,InterfaceTypeDefinition:a,InterfaceTypeExtension:a,ObjectTypeDefinition:a,ObjectTypeExtension:a};function a(t){var a;const s=t.name.value;i[s]||(i[s]=Object.create(null));const u=null!==(a=t.fields)&&void 0!==a?a:[],c=i[s];for(const t of u){const i=t.name.value;o(n[s],i)?e.reportError(new r.GraphQLError(`Field "${s}.${i}" already exists in the schema. It cannot also be defined in this type extension.`,{nodes:t.name})):c[i]?e.reportError(new r.GraphQLError(`Field "${s}.${i}" can only be defined once.`,{nodes:[c[i],t.name]})):c[i]=t.name}return!1}};var r=n(1702),i=n(3754);function o(e,t){return!!((0,i.isObjectType)(e)||(0,i.isInterfaceType)(e)||(0,i.isInputObjectType)(e))&&null!=e.getFields()[t]}},7091:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueFragmentNamesRule=function(e){const t=Object.create(null);return{OperationDefinition:()=>!1,FragmentDefinition(n){const i=n.name.value;return t[i]?e.reportError(new r.GraphQLError(`There can be only one fragment named "${i}".`,{nodes:[t[i],n.name]})):t[i]=n.name,!1}}};var r=n(1702)},7027:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueInputFieldNamesRule=function(e){const t=[];let n=Object.create(null);return{ObjectValue:{enter(){t.push(n),n=Object.create(null)},leave(){const e=t.pop();e||(0,r.invariant)(!1),n=e}},ObjectField(t){const r=t.name.value;n[r]?e.reportError(new i.GraphQLError(`There can be only one input field named "${r}".`,{nodes:[n[r],t.name]})):n[r]=t.name}}};var r=n(1321),i=n(1702)},5988:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueOperationNamesRule=function(e){const t=Object.create(null);return{OperationDefinition(n){const i=n.name;return i&&(t[i.value]?e.reportError(new r.GraphQLError(`There can be only one operation named "${i.value}".`,{nodes:[t[i.value],i]})):t[i.value]=i),!1},FragmentDefinition:()=>!1}};var r=n(1702)},105:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueOperationTypesRule=function(e){const t=e.getSchema(),n=Object.create(null),i=t?{query:t.getQueryType(),mutation:t.getMutationType(),subscription:t.getSubscriptionType()}:{};return{SchemaDefinition:o,SchemaExtension:o};function o(t){var o;const a=null!==(o=t.operationTypes)&&void 0!==o?o:[];for(const t of a){const o=t.operation,a=n[o];i[o]?e.reportError(new r.GraphQLError(`Type for ${o} already defined in the schema. It cannot be redefined.`,{nodes:t})):a?e.reportError(new r.GraphQLError(`There can be only one ${o} type in schema.`,{nodes:[a,t]})):n[o]=t}return!1}};var r=n(1702)},3171:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueTypeNamesRule=function(e){const t=Object.create(null),n=e.getSchema();return{ScalarTypeDefinition:i,ObjectTypeDefinition:i,InterfaceTypeDefinition:i,UnionTypeDefinition:i,EnumTypeDefinition:i,InputObjectTypeDefinition:i};function i(i){const o=i.name.value;if(null==n||!n.getType(o))return t[o]?e.reportError(new r.GraphQLError(`There can be only one type named "${o}".`,{nodes:[t[o],i.name]})):t[o]=i.name,!1;e.reportError(new r.GraphQLError(`Type "${o}" already exists in the schema. It cannot also be defined in this type definition.`,{nodes:i.name}))}};var r=n(1702)},3191:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UniqueVariableNamesRule=function(e){return{OperationDefinition(t){var n;const o=null!==(n=t.variableDefinitions)&&void 0!==n?n:[],a=(0,r.groupBy)(o,(e=>e.variable.name.value));for(const[t,n]of a)n.length>1&&e.reportError(new i.GraphQLError(`There can be only one variable named "$${t}".`,{nodes:n.map((e=>e.variable.name))}))}}};var r=n(4947),i=n(1702)},7909:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValuesOfCorrectTypeRule=function(e){return{ListValue(t){const n=(0,c.getNullableType)(e.getParentInputType());if(!(0,c.isListType)(n))return p(e,t),!1},ObjectValue(t){const n=(0,c.getNamedType)(e.getInputType());if(!(0,c.isInputObjectType)(n))return p(e,t),!1;const r=(0,o.keyMap)(t.fields,(e=>e.name.value));for(const o of Object.values(n.getFields()))if(!r[o.name]&&(0,c.isRequiredInputField)(o)){const r=(0,i.inspect)(o.type);e.reportError(new s.GraphQLError(`Field "${n.name}.${o.name}" of required type "${r}" was not provided.`,{nodes:t}))}},ObjectField(t){const n=(0,c.getNamedType)(e.getParentInputType());if(!e.getInputType()&&(0,c.isInputObjectType)(n)){const i=(0,a.suggestionList)(t.name.value,Object.keys(n.getFields()));e.reportError(new s.GraphQLError(`Field "${t.name.value}" is not defined by type "${n.name}".`+(0,r.didYouMean)(i),{nodes:t}))}},NullValue(t){const n=e.getInputType();(0,c.isNonNullType)(n)&&e.reportError(new s.GraphQLError(`Expected value of type "${(0,i.inspect)(n)}", found ${(0,u.print)(t)}.`,{nodes:t}))},EnumValue:t=>p(e,t),IntValue:t=>p(e,t),FloatValue:t=>p(e,t),StringValue:t=>p(e,t),BooleanValue:t=>p(e,t)}};var r=n(2832),i=n(9657),o=n(4590),a=n(1709),s=n(1702),u=n(585),c=n(3754);function p(e,t){const n=e.getInputType();if(!n)return;const r=(0,c.getNamedType)(n);if((0,c.isLeafType)(r))try{if(void 0===r.parseLiteral(t,void 0)){const r=(0,i.inspect)(n);e.reportError(new s.GraphQLError(`Expected value of type "${r}", found ${(0,u.print)(t)}.`,{nodes:t}))}}catch(r){const o=(0,i.inspect)(n);r instanceof s.GraphQLError?e.reportError(r):e.reportError(new s.GraphQLError(`Expected value of type "${o}", found ${(0,u.print)(t)}; `+r.message,{nodes:t,originalError:r}))}else{const r=(0,i.inspect)(n);e.reportError(new s.GraphQLError(`Expected value of type "${r}", found ${(0,u.print)(t)}.`,{nodes:t}))}}},964:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VariablesAreInputTypesRule=function(e){return{VariableDefinition(t){const n=(0,a.typeFromAST)(e.getSchema(),t.type);if(void 0!==n&&!(0,o.isInputType)(n)){const n=t.variable.name.value,o=(0,i.print)(t.type);e.reportError(new r.GraphQLError(`Variable "$${n}" cannot be non-input type "${o}".`,{nodes:t.type}))}}}};var r=n(1702),i=n(585),o=n(3754),a=n(6693)},4281:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VariablesInAllowedPositionRule=function(e){let t=Object.create(null);return{OperationDefinition:{enter(){t=Object.create(null)},leave(n){const o=e.getRecursiveVariableUsages(n);for(const{node:n,type:a,defaultValue:s}of o){const o=n.name.value,p=t[o];if(p&&a){const t=e.getSchema(),l=(0,u.typeFromAST)(t,p.type);if(l&&!c(t,l,p.defaultValue,a,s)){const t=(0,r.inspect)(l),s=(0,r.inspect)(a);e.reportError(new i.GraphQLError(`Variable "$${o}" of type "${t}" used in position expecting type "${s}".`,{nodes:[p,n]}))}}}}},VariableDefinition(e){t[e.variable.name.value]=e}}};var r=n(9657),i=n(1702),o=n(7030),a=n(3754),s=n(3448),u=n(6693);function c(e,t,n,r,i){if((0,a.isNonNullType)(r)&&!(0,a.isNonNullType)(t)){if((null==n||n.kind===o.Kind.NULL)&&void 0===i)return!1;const a=r.ofType;return(0,s.isTypeSubTypeOf)(e,t,a)}return(0,s.isTypeSubTypeOf)(e,t,r)}},4555:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoDeprecatedCustomRule=function(e){return{Field(t){const n=e.getFieldDef(),o=null==n?void 0:n.deprecationReason;if(n&&null!=o){const a=e.getParentType();null!=a||(0,r.invariant)(!1),e.reportError(new i.GraphQLError(`The field ${a.name}.${n.name} is deprecated. ${o}`,{nodes:t}))}},Argument(t){const n=e.getArgument(),o=null==n?void 0:n.deprecationReason;if(n&&null!=o){const a=e.getDirective();if(null!=a)e.reportError(new i.GraphQLError(`Directive "@${a.name}" argument "${n.name}" is deprecated. ${o}`,{nodes:t}));else{const a=e.getParentType(),s=e.getFieldDef();null!=a&&null!=s||(0,r.invariant)(!1),e.reportError(new i.GraphQLError(`Field "${a.name}.${s.name}" argument "${n.name}" is deprecated. ${o}`,{nodes:t}))}}},ObjectField(t){const n=(0,o.getNamedType)(e.getParentInputType());if((0,o.isInputObjectType)(n)){const r=n.getFields()[t.name.value],o=null==r?void 0:r.deprecationReason;null!=o&&e.reportError(new i.GraphQLError(`The input field ${n.name}.${r.name} is deprecated. ${o}`,{nodes:t}))}},EnumValue(t){const n=e.getEnumValue(),a=null==n?void 0:n.deprecationReason;if(n&&null!=a){const s=(0,o.getNamedType)(e.getInputType());null!=s||(0,r.invariant)(!1),e.reportError(new i.GraphQLError(`The enum value "${s.name}.${n.name}" is deprecated. ${a}`,{nodes:t}))}}}};var r=n(1321),i=n(1702),o=n(3754)},5588:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoSchemaIntrospectionCustomRule=function(e){return{Field(t){const n=(0,i.getNamedType)(e.getType());n&&(0,o.isIntrospectionType)(n)&&e.reportError(new r.GraphQLError(`GraphQL introspection has been disabled, but the requested query contained the field "${t.name.value}".`,{nodes:t}))}}};var r=n(1702),i=n(3754),o=n(8364)},7283:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.specifiedSDLRules=t.specifiedRules=void 0;var r=n(2988),i=n(9284),o=n(6514),a=n(7372),s=n(7999),u=n(9093),c=n(5117),p=n(9130),l=n(502),d=n(944),f=n(1350),y=n(6072),m=n(4472),h=n(2457),T=n(6839),b=n(817),v=n(1672),g=n(1843),E=n(3618),N=n(1066),O=n(5566),I=n(5972),_=n(9529),L=n(1813),S=n(3084),D=n(7091),j=n(7027),A=n(5988),P=n(105),R=n(3171),w=n(3191),k=n(7909),F=n(964),x=n(4281);const G=Object.freeze([r.ExecutableDefinitionsRule,A.UniqueOperationNamesRule,p.LoneAnonymousOperationRule,E.SingleFieldSubscriptionsRule,c.KnownTypeNamesRule,o.FragmentsOnCompositeTypesRule,F.VariablesAreInputTypesRule,g.ScalarLeafsRule,i.FieldsOnCorrectTypeRule,D.UniqueFragmentNamesRule,u.KnownFragmentNamesRule,y.NoUnusedFragmentsRule,T.PossibleFragmentSpreadsRule,d.NoFragmentCyclesRule,w.UniqueVariableNamesRule,f.NoUndefinedVariablesRule,m.NoUnusedVariablesRule,s.KnownDirectivesRule,_.UniqueDirectivesPerLocationRule,a.KnownArgumentNamesRule,O.UniqueArgumentNamesRule,k.ValuesOfCorrectTypeRule,v.ProvidedRequiredArgumentsRule,x.VariablesInAllowedPositionRule,h.OverlappingFieldsCanBeMergedRule,j.UniqueInputFieldNamesRule]);t.specifiedRules=G;const V=Object.freeze([l.LoneSchemaDefinitionRule,P.UniqueOperationTypesRule,R.UniqueTypeNamesRule,L.UniqueEnumValueNamesRule,S.UniqueFieldDefinitionNamesRule,N.UniqueArgumentDefinitionNamesRule,I.UniqueDirectiveNamesRule,c.KnownTypeNamesRule,s.KnownDirectivesRule,_.UniqueDirectivesPerLocationRule,b.PossibleTypeExtensionsRule,a.KnownArgumentNamesOnDirectivesRule,O.UniqueArgumentNamesRule,j.UniqueInputFieldNamesRule,v.ProvidedRequiredArgumentsOnDirectivesRule]);t.specifiedSDLRules=V},9040:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.assertValidSDL=function(e){const t=p(e);if(0!==t.length)throw new Error(t.map((e=>e.message)).join("\n\n"))},t.assertValidSDLExtension=function(e,t){const n=p(e,t);if(0!==n.length)throw new Error(n.map((e=>e.message)).join("\n\n"))},t.validate=function(e,t,n=u.specifiedRules,p,l=new s.TypeInfo(e)){var d;const f=null!==(d=null==p?void 0:p.maxErrors)&&void 0!==d?d:100;t||(0,r.devAssert)(!1,"Must provide document."),(0,a.assertValidSchema)(e);const y=Object.freeze({}),m=[],h=new c.ValidationContext(e,t,l,(e=>{if(m.length>=f)throw m.push(new i.GraphQLError("Too many validation errors, error limit reached. Validation aborted.")),y;m.push(e)})),T=(0,o.visitInParallel)(n.map((e=>e(h))));try{(0,o.visit)(t,(0,s.visitWithTypeInfo)(l,T))}catch(e){if(e!==y)throw e}return m},t.validateSDL=p;var r=n(3028),i=n(1702),o=n(9111),a=n(9873),s=n(7485),u=n(7283),c=n(4782);function p(e,t,n=u.specifiedSDLRules){const r=[],i=new c.SDLValidationContext(e,t,(e=>{r.push(e)})),a=n.map((e=>e(i)));return(0,o.visit)(e,(0,o.visitInParallel)(a)),r}},4274:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.versionInfo=t.version=void 0,t.version="16.8.1";const n=Object.freeze({major:16,minor:8,patch:1,preReleaseTag:null});t.versionInfo=n},1609:e=>{e.exports=window.React},6087:e=>{e.exports=window.wp.element}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n(3408)})();