{"packages": [{"name": "appsero/client", "version": "v2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Appsero/client.git", "reference": "12ff65b9770286d21edf314e7acfcd26fdde3315"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Appsero/client/zipball/12ff65b9770286d21edf314e7acfcd26fdde3315", "reference": "12ff65b9770286d21edf314e7acfcd26fdde3315", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.2", "phpcompatibility/phpcompatibility-wp": "dev-master", "phpunit/phpunit": "^8.5.31", "squizlabs/php_codesniffer": "^3.7", "tareq1988/wp-php-cs-fixer": "dev-master", "wp-coding-standards/wpcs": "dev-develop"}, "time": "2024-11-25T05:58:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Appsero\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Appsero Client", "keywords": ["analytics", "plugin", "theme", "wordpress"], "support": {"issues": "https://github.com/Appsero/client/issues", "source": "https://github.com/Appsero/client/tree/v2.0.4"}, "install-path": "../appsero/client"}, {"name": "ivome/graphql-relay-php", "version": "v0.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ivome/graphql-relay-php.git", "reference": "06bd176103618d896197d85d04a3a17c91e39698"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ivome/graphql-relay-php/zipball/06bd176103618d896197d85d04a3a17c91e39698", "reference": "06bd176103618d896197d85d04a3a17c91e39698", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "webonyx/graphql-php": "^14.0 || ^15.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "satooshi/php-coveralls": "~1.0"}, "time": "2023-10-20T15:43:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A PHP port of GraphQL Relay reference implementation", "homepage": "https://github.com/ivome/graphql-relay-php", "keywords": ["<PERSON><PERSON>", "api", "graphql"], "support": {"issues": "https://github.com/ivome/graphql-relay-php/issues", "source": "https://github.com/ivome/graphql-relay-php/tree/v0.7.0"}, "install-path": "../ivome/graphql-relay-php"}, {"name": "webonyx/graphql-php", "version": "v14.11.10", "version_normalized": "**********", "source": {"type": "git", "url": "https://github.com/webonyx/graphql-php.git", "reference": "d9c2fdebc6aa01d831bc2969da00e8588cffef19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webonyx/graphql-php/zipball/d9c2fdebc6aa01d831bc2969da00e8588cffef19", "reference": "d9c2fdebc6aa01d831bc2969da00e8588cffef19", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.1 || ^8"}, "require-dev": {"amphp/amp": "^2.3", "doctrine/coding-standard": "^6.0", "nyholm/psr7": "^1.2", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "0.12.82", "phpstan/phpstan-phpunit": "0.12.18", "phpstan/phpstan-strict-rules": "0.12.9", "phpunit/phpunit": "^7.2 || ^8.5", "psr/http-message": "^1.0", "react/promise": "2.*", "simpod/php-coveralls-mirror": "^3.0"}, "suggest": {"psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "time": "2023-07-05T14:23:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP port of GraphQL reference implementation", "homepage": "https://github.com/webonyx/graphql-php", "keywords": ["api", "graphql"], "support": {"issues": "https://github.com/webonyx/graphql-php/issues", "source": "https://github.com/webonyx/graphql-php/tree/v14.11.10"}, "funding": [{"url": "https://opencollective.com/webonyx-graphql-php", "type": "open_collective"}], "install-path": "../webonyx/graphql-php"}], "dev": false, "dev-package-names": []}