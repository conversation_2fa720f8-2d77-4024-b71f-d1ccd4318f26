<?php return array(
    'root' => array(
        'name' => 'wp-graphql/wp-graphql',
        'pretty_version' => 'v1.29.3',
        'version' => '1.29.3.0',
        'reference' => 'e8b265b4cd3e33c3631defbd5ee63f71a8936f41',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'appsero/client' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'reference' => '12ff65b9770286d21edf314e7acfcd26fdde3315',
            'type' => 'library',
            'install_path' => __DIR__ . '/../appsero/client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ivome/graphql-relay-php' => array(
            'pretty_version' => 'v0.7.0',
            'version' => '0.7.0.0',
            'reference' => '06bd176103618d896197d85d04a3a17c91e39698',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ivome/graphql-relay-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webonyx/graphql-php' => array(
            'pretty_version' => 'v14.11.10',
            'version' => '14.11.10.0',
            'reference' => 'd9c2fdebc6aa01d831bc2969da00e8588cffef19',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webonyx/graphql-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wp-graphql/wp-graphql' => array(
            'pretty_version' => 'v1.29.3',
            'version' => '1.29.3.0',
            'reference' => 'e8b265b4cd3e33c3631defbd5ee63f71a8936f41',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
