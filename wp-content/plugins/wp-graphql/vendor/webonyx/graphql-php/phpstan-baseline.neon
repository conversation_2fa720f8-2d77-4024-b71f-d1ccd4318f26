parameters:
	ignoreErrors:
		-
			message: "#^Variable property access on object\\.$#"
			count: 2
			path: src/Executor/Executor.php

		-
			message: "#^Variable property access on object\\.$#"
			count: 2
			path: src/Experimental/Executor/CoroutineExecutor.php

		-
			message: "#^Variable property access on mixed\\.$#"
			count: 1
			path: src/Experimental/Executor/CoroutineExecutor.php

		-
			message: "#^Variable property access on GraphQL\\\\Language\\\\AST\\\\Node\\.$#"
			count: 1
			path: src/Language/AST/Node.php

		-
			message: "#^Method GraphQL\\\\Language\\\\AST\\\\NodeList\\:\\:splice\\(\\) should return GraphQL\\\\Language\\\\AST\\\\NodeList\\<T of GraphQL\\\\Language\\\\AST\\\\Node\\> but returns GraphQL\\\\Language\\\\AST\\\\NodeList\\<GraphQL\\\\Language\\\\AST\\\\Node\\>\\.$#"
			count: 1
			path: src/Language/AST/NodeList.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\<int, array\\|GraphQL\\\\Language\\\\AST\\\\Node\\|GraphQL\\\\Language\\\\AST\\\\NodeList\\> given\\.$#"
			count: 1
			path: src/Language/Visitor.php

		-
			message: "#^Variable property access on GraphQL\\\\Language\\\\AST\\\\Node\\|null\\.$#"
			count: 1
			path: src/Language/Visitor.php

		-
			message: "#^Variable property access on GraphQL\\\\Language\\\\AST\\\\Node\\.$#"
			count: 1
			path: src/Language/Visitor.php

		-
			message: "#^Only booleans are allowed in a negated boolean, \\(callable\\)\\|null given\\.$#"
			count: 1
			path: src/Language/Visitor.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, \\(callable\\)\\|null given\\.$#"
			count: 2
			path: src/Server/Helper.php

		-
			message: "#^Only booleans are allowed in an if condition, int given\\.$#"
			count: 1
			path: src/Server/Helper.php

		-
			message: "#^Only booleans are allowed in a negated boolean, string given\\.$#"
			count: 2
			path: src/Server/Helper.php

		-
			message: "#^Only booleans are allowed in &&, string given on the left side\\.$#"
			count: 1
			path: src/Server/Helper.php

		-
			message: "#^Only booleans are allowed in &&, string given on the right side\\.$#"
			count: 1
			path: src/Server/Helper.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, string given\\.$#"
			count: 1
			path: src/Server/Helper.php

		-
			message: "#^Only booleans are allowed in an if condition, \\(callable\\)\\|null given\\.$#"
			count: 1
			path: src/Server/Helper.php

		-
			message: "#^Only booleans are allowed in \\|\\|, \\(callable\\)\\|null given on the left side\\.$#"
			count: 1
			path: src/Server/Helper.php

		-
			message: "#^Only booleans are allowed in a negated boolean, ArrayObject\\<string, GraphQL\\\\Type\\\\Definition\\\\EnumValueDefinition\\> given\\.$#"
			count: 1
			path: src/Type/Definition/EnumType.php

		-
			message: "#^Variable property access on \\$this\\(GraphQL\\\\Type\\\\Definition\\\\FieldDefinition\\)\\.$#"
			count: 3
			path: src/Type/Definition/FieldDefinition.php

		-
			message: "#^Variable property access on \\$this\\(GraphQL\\\\Type\\\\Definition\\\\InputObjectField\\)\\.$#"
			count: 4
			path: src/Type/Definition/InputObjectField.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Language\\\\AST\\\\SelectionSetNode\\|null given\\.$#"
			count: 1
			path: src/Type/Definition/QueryPlan.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Language\\\\AST\\\\SelectionSetNode\\|null given\\.$#"
			count: 1
			path: src/Type/Definition/QueryPlan.php

		-
			message: "#^Only booleans are allowed in an if condition, string given\\.$#"
			count: 1
			path: src/Type/Definition/Type.php

		-
			message: "#^Only booleans are allowed in a negated boolean, string\\|null given\\.$#"
			count: 2
			path: src/Type/Introspection.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\<GraphQL\\\\Type\\\\Definition\\\\Type\\>\\|\\(callable\\) given\\.$#"
			count: 1
			path: src/Type/Schema.php

		-
			message: "#^Only booleans are allowed in an if condition, \\(callable\\)\\|null given\\.$#"
			count: 1
			path: src/Type/Schema.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\Type\\|null given\\.$#"
			count: 1
			path: src/Type/Schema.php

		-
			message: "#^Only booleans are allowed in an if condition, array\\<GraphQL\\\\Error\\\\Error\\|GraphQL\\\\Error\\\\InvariantViolation\\> given\\.$#"
			count: 1
			path: src/Type/Schema.php

		-
			message: "#^Only booleans are allowed in a negated boolean, \\(callable\\)\\|null given\\.$#"
			count: 1
			path: src/Type/Schema.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\ObjectType\\|null given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in &&, GraphQL\\\\Type\\\\Definition\\\\ObjectType\\|null given on the left side\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in &&, array\\<GraphQL\\\\Language\\\\AST\\\\Node\\>\\|GraphQL\\\\Language\\\\AST\\\\Node\\|GraphQL\\\\Language\\\\AST\\\\TypeDefinitionNode\\|GraphQL\\\\Language\\\\AST\\\\TypeNode\\|null given on the left side\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\OperationTypeDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Type\\\\Definition\\\\Type given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Error\\\\Error\\|null given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\InputValueDefinitionNode given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\DirectiveDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in &&, array\\<GraphQL\\\\Language\\\\AST\\\\FieldDefinitionNode\\> given on the left side\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\DirectiveDefinitionNode\\|GraphQL\\\\Language\\\\AST\\\\EnumTypeDefinitionNode\\|GraphQL\\\\Language\\\\AST\\\\InputObjectTypeDefinitionNode\\|GraphQL\\\\Language\\\\AST\\\\InterfaceTypeDefinitionNode\\|GraphQL\\\\Language\\\\AST\\\\ObjectTypeDefinitionNode\\|GraphQL\\\\Language\\\\AST\\\\SchemaDefinitionNode\\|GraphQL\\\\Language\\\\AST\\\\UnionTypeDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, array\\<GraphQL\\\\Language\\\\AST\\\\EnumTypeExtensionNode\\|GraphQL\\\\Language\\\\AST\\\\InputObjectTypeExtensionNode\\|GraphQL\\\\Language\\\\AST\\\\InterfaceTypeExtensionNode\\|GraphQL\\\\Language\\\\AST\\\\ObjectTypeExtensionNode\\|GraphQL\\\\Language\\\\AST\\\\SchemaTypeExtensionNode\\|GraphQL\\\\Language\\\\AST\\\\UnionTypeExtensionNode\\> given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Language\\\\AST\\\\InterfaceTypeDefinitionNode\\|GraphQL\\\\Language\\\\AST\\\\InterfaceTypeExtensionNode\\|GraphQL\\\\Language\\\\AST\\\\ObjectTypeDefinitionNode\\|GraphQL\\\\Language\\\\AST\\\\ObjectTypeExtensionNode given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\FieldDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in &&, GraphQL\\\\Language\\\\AST\\\\FieldDefinitionNode\\|null given on the left side\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in &&, GraphQL\\\\Language\\\\AST\\\\NodeList\\<GraphQL\\\\Language\\\\AST\\\\InputValueDefinitionNode\\> given on the right side\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\InputValueDefinitionNode\\|null given\\.$#"
			count: 2
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\FieldDefinition\\|null given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\FieldArgument\\|null given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in \\|\\|, GraphQL\\\\Type\\\\Definition\\\\FieldArgument\\|null given on the left side\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\<GraphQL\\\\Type\\\\Definition\\\\ObjectType\\> given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\<GraphQL\\\\Type\\\\Definition\\\\EnumValueDefinition\\> given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in &&, array\\<GraphQL\\\\Language\\\\AST\\\\EnumValueDefinitionNode\\> given on the left side\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\<GraphQL\\\\Type\\\\Definition\\\\InputObjectField\\> given\\.$#"
			count: 1
			path: src/Type/SchemaValidationContext.php

		-
			message: "#^Variable property access on mixed\\.$#"
			count: 2
			path: src/Utils/AST.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Language\\\\AST\\\\BooleanValueNode\\|GraphQL\\\\Language\\\\AST\\\\EnumValueNode\\|GraphQL\\\\Language\\\\AST\\\\FloatValueNode\\|GraphQL\\\\Language\\\\AST\\\\IntValueNode\\|GraphQL\\\\Language\\\\AST\\\\ListValueNode\\|GraphQL\\\\Language\\\\AST\\\\NullValueNode\\|GraphQL\\\\Language\\\\AST\\\\ObjectValueNode\\|GraphQL\\\\Language\\\\AST\\\\StringValueNode\\|null given\\.$#"
			count: 2
			path: src/Utils/AST.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\|null given\\.$#"
			count: 1
			path: src/Utils/AST.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\EnumValueDefinition\\|null given\\.$#"
			count: 1
			path: src/Utils/AST.php

		-
			message: "#^Only booleans are allowed in &&, array\\|null given on the left side\\.$#"
			count: 1
			path: src/Utils/AST.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Type\\\\Definition\\\\Type\\|null given\\.$#"
			count: 2
			path: src/Utils/AST.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Language\\\\AST\\\\NodeList\\<GraphQL\\\\Language\\\\AST\\\\DefinitionNode&GraphQL\\\\Language\\\\AST\\\\Node\\> given\\.$#"
			count: 1
			path: src/Utils/AST.php

		-
			message: "#^Only booleans are allowed in a negated boolean, string\\|null given\\.$#"
			count: 1
			path: src/Utils/AST.php

		-
			message: "#^Only booleans are allowed in an if condition, callable given\\.$#"
			count: 1
			path: src/Utils/ASTDefinitionBuilder.php

		-
			message: "#^Only booleans are allowed in &&, array\\<bool\\>\\|null given on the left side\\.$#"
			count: 1
			path: src/Utils/PairSet.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Type\\\\Definition\\\\Type\\|null given\\.$#"
			count: 1
			path: src/Utils/SchemaExtender.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Language\\\\AST\\\\SchemaDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Utils/SchemaExtender.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\<GraphQL\\\\Type\\\\Definition\\\\Type\\>\\|null given\\.$#"
			count: 1
			path: src/Utils/TypeInfo.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\Type\\|null given\\.$#"
			count: 1
			path: src/Utils/TypeInfo.php

		-
			message: "#^Only booleans are allowed in an if condition, \\(GraphQL\\\\Type\\\\Definition\\\\CompositeType&GraphQL\\\\Type\\\\Definition\\\\Type\\)\\|null given\\.$#"
			count: 1
			path: src/Utils/TypeInfo.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Type\\\\Definition\\\\FieldDefinition\\|null given\\.$#"
			count: 1
			path: src/Utils/TypeInfo.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\NamedTypeNode given\\.$#"
			count: 1
			path: src/Utils/TypeInfo.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Type\\\\Definition\\\\Directive\\|GraphQL\\\\Type\\\\Definition\\\\FieldDefinition\\|null given\\.$#"
			count: 1
			path: src/Utils/TypeInfo.php

		-
			message: "#^Only booleans are allowed in &&, GraphQL\\\\Type\\\\Definition\\\\FieldArgument\\|null given on the left side\\.$#"
			count: 1
			path: src/Utils/TypeInfo.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Type\\\\Definition\\\\InputObjectField\\|null given\\.$#"
			count: 1
			path: src/Utils/TypeInfo.php

		-
			message: "#^Only booleans are allowed in &&, GraphQL\\\\Type\\\\Definition\\\\InputObjectField\\|null given on the left side\\.$#"
			count: 1
			path: src/Utils/TypeInfo.php

		-
			message: "#^Variable property access on object\\.$#"
			count: 1
			path: src/Utils/Utils.php

		-
			message: "#^Only booleans are allowed in a negated boolean, string given\\.$#"
			count: 1
			path: src/Utils/Utils.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Error\\\\Error\\|null given\\.$#"
			count: 1
			path: src/Utils/Utils.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Type\\\\Definition\\\\EnumValueDefinition\\|null given\\.$#"
			count: 1
			path: src/Utils/Value.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, array\\<string\\> given\\.$#"
			count: 2
			path: src/Utils/Value.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, array\\<GraphQL\\\\Error\\\\Error\\> given\\.$#"
			count: 2
			path: src/Utils/Value.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, string given\\.$#"
			count: 2
			path: src/Utils/Value.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, string\\|null given\\.$#"
			count: 1
			path: src/Utils/Value.php

		-
			message: "#^Only booleans are allowed in a negated boolean, \\(GraphQL\\\\Type\\\\Definition\\\\CompositeType&GraphQL\\\\Type\\\\Definition\\\\Type\\)\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/FieldsOnCorrectType.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Type\\\\Definition\\\\FieldDefinition given\\.$#"
			count: 1
			path: src/Validator/Rules/FieldsOnCorrectType.php

		-
			message: "#^Only booleans are allowed in an if condition, array\\<string\\> given\\.$#"
			count: 1
			path: src/Validator/Rules/FieldsOnCorrectType.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Language\\\\AST\\\\NamedTypeNode given\\.$#"
			count: 1
			path: src/Validator/Rules/FragmentsOnCompositeTypes.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\Type\\|null given\\.$#"
			count: 2
			path: src/Validator/Rules/FragmentsOnCompositeTypes.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Type\\\\Schema\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/KnownDirectives.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/KnownDirectives.php

		-
			message: "#^Only booleans are allowed in a negated boolean, string given\\.$#"
			count: 1
			path: src/Validator/Rules/KnownDirectives.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Language\\\\AST\\\\FragmentDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/KnownFragmentNames.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Language\\\\AST\\\\FragmentDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/NoFragmentCycles.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\NameNode\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/OverlappingFieldsCanBeMerged.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\NamedTypeNode given\\.$#"
			count: 1
			path: src/Validator/Rules/OverlappingFieldsCanBeMerged.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\|null given\\.$#"
			count: 2
			path: src/Validator/Rules/OverlappingFieldsCanBeMerged.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Language\\\\AST\\\\ArgumentNode\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/OverlappingFieldsCanBeMerged.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Language\\\\AST\\\\Node given\\.$#"
			count: 2
			path: src/Validator/Rules/OverlappingFieldsCanBeMerged.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Language\\\\AST\\\\FragmentDefinitionNode\\|null given\\.$#"
			count: 3
			path: src/Validator/Rules/OverlappingFieldsCanBeMerged.php

		-
			message: "#^Only booleans are allowed in a negated boolean, \\(GraphQL\\\\Type\\\\Definition\\\\CompositeType&GraphQL\\\\Type\\\\Definition\\\\Type\\)\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/PossibleFragmentSpreads.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Language\\\\AST\\\\FragmentDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/PossibleFragmentSpreads.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\FieldDefinition given\\.$#"
			count: 1
			path: src/Validator/Rules/ProvidedRequiredArguments.php

		-
			message: "#^Only booleans are allowed in \\|\\|, GraphQL\\\\Language\\\\AST\\\\ArgumentNode\\|null given on the left side\\.$#"
			count: 1
			path: src/Validator/Rules/ProvidedRequiredArguments.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Type\\\\Schema\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/ProvidedRequiredArgumentsOnDirectives.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/ProvidedRequiredArgumentsOnDirectives.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Language\\\\AST\\\\FragmentDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/QuerySecurityRule.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Language\\\\AST\\\\NameNode\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/QuerySecurityRule.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\OutputType\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/ScalarLeafs.php

		-
			message: "#^Only booleans are allowed in an if condition, GraphQL\\\\Language\\\\AST\\\\SelectionSetNode\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/ScalarLeafs.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Language\\\\AST\\\\SelectionSetNode\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/ScalarLeafs.php

		-
			message: "#^Only booleans are allowed in \\|\\|, GraphQL\\\\Type\\\\Definition\\\\EnumType\\|GraphQL\\\\Type\\\\Definition\\\\InputObjectType\\|GraphQL\\\\Type\\\\Definition\\\\ListOfType\\|GraphQL\\\\Type\\\\Definition\\\\NonNull\\|GraphQL\\\\Type\\\\Definition\\\\ScalarType given on the left side\\.$#"
			count: 1
			path: src/Validator/Rules/ValuesOfCorrectType.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\EnumValueDefinition\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/ValuesOfCorrectType.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\EnumType\\|GraphQL\\\\Type\\\\Definition\\\\InputObjectType\\|GraphQL\\\\Type\\\\Definition\\\\ListOfType\\|GraphQL\\\\Type\\\\Definition\\\\NonNull\\|GraphQL\\\\Type\\\\Definition\\\\ScalarType given\\.$#"
			count: 1
			path: src/Validator/Rules/ValuesOfCorrectType.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, array\\<string\\> given\\.$#"
			count: 1
			path: src/Validator/Rules/ValuesOfCorrectType.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\Type\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/VariablesAreInputTypes.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\Type\\|null given\\.$#"
			count: 1
			path: src/Validator/Rules/VariablesInAllowedPosition.php

		-
			message: "#^Only booleans are allowed in &&, GraphQL\\\\Language\\\\AST\\\\ValueNode\\|null given on the left side\\.$#"
			count: 1
			path: src/Validator/Rules/VariablesInAllowedPosition.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Language\\\\AST\\\\FragmentDefinitionNode\\|null given\\.$#"
			count: 1
			path: src/Validator/ValidationContext.php

		-
			message: "#^Only booleans are allowed in a negated boolean, array\\<GraphQL\\\\Language\\\\AST\\\\FragmentDefinitionNode\\> given\\.$#"
			count: 1
			path: src/Validator/ValidationContext.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Schema given\\.$#"
			count: 1
			path: tests/Executor/DirectivesTest.php

		-
			message: "#^Anonymous function should have native return typehint \"class@anonymous/tests/Executor/ExecutorTest\\.php\\:1333\"\\.$#"
			count: 1
			path: tests/Executor/ExecutorTest.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\InterfaceType given\\.$#"
			count: 1
			path: tests/Executor/LazyInterfaceTest.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Definition\\\\ObjectType given\\.$#"
			count: 1
			path: tests/Executor/LazyInterfaceTest.php

		-
			message: "#^Only booleans are allowed in a negated boolean, GraphQL\\\\Type\\\\Schema given\\.$#"
			count: 1
			path: tests/Executor/ValuesTest.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, \\(GraphQL\\\\Type\\\\Definition\\\\CompositeType&GraphQL\\\\Type\\\\Definition\\\\Type\\)\\|null given\\.$#"
			count: 4
			path: tests/Language/VisitorTest.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, \\(GraphQL\\\\Type\\\\Definition\\\\OutputType&GraphQL\\\\Type\\\\Definition\\\\Type\\)\\|null given\\.$#"
			count: 4
			path: tests/Language/VisitorTest.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, GraphQL\\\\Type\\\\Definition\\\\EnumType\\|GraphQL\\\\Type\\\\Definition\\\\InputObjectType\\|GraphQL\\\\Type\\\\Definition\\\\ListOfType\\|GraphQL\\\\Type\\\\Definition\\\\NonNull\\|GraphQL\\\\Type\\\\Definition\\\\ScalarType\\|null given\\.$#"
			count: 4
			path: tests/Language/VisitorTest.php

		-
			message: "#^Access to an undefined property GraphQL\\\\Type\\\\Definition\\\\FieldDefinition\\:\\:\\$nonExistentProp\\.$#"
			count: 2
			path: tests/Type/DefinitionTest.php

		-
			message: "#^Access to an undefined property GraphQL\\\\Type\\\\Definition\\\\InputObjectField\\:\\:\\$nonExistentProp\\.$#"
			count: 1
			path: tests/Type/DefinitionTest.php

		-
			message: "#^Variable property access on \\$this\\(GraphQL\\\\Tests\\\\Type\\\\TypeLoaderTest\\)\\.$#"
			count: 1
			path: tests/Type/TypeLoaderTest.php

		-
			message: "#^Only booleans are allowed in a negated boolean, stdClass given\\.$#"
			count: 1
			path: tests/Utils/AstFromValueTest.php

