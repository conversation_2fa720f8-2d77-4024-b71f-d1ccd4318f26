{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "aa0bc8e95c40b5f41d3301f1985c9db5", "packages": [{"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": ""}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}, "time": "2019-12-27T09:44:58+00:00"}, {"name": "phpcompatibility/phpcompatibility-paragonie", "version": "1.3.2", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie.git", "reference": "bba5a9dfec7fcfbd679cfaf611d86b4d3759da26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityParagonie/zipball/bba5a9dfec7fcfbd679cfaf611d86b4d3759da26", "reference": "bba5a9dfec7fcfbd679cfaf611d86b4d3759da26", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "paragonie/random_compat": "dev-master", "paragonie/sodium_compat": "dev-master"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A set of rulesets for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by the Paragonie polyfill libraries.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "paragonie", "phpcs", "polyfill", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie"}, "time": "2022-10-25T01:46:02+00:00"}, {"name": "phpcompatibility/phpcompatibility-wp", "version": "2.1.4", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityWP.git", "reference": "b6c1e3ee1c35de6c41a511d5eb9bd03e447480a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityWP/zipball/b6c1e3ee1c35de6c41a511d5eb9bd03e447480a5", "reference": "b6c1e3ee1c35de6c41a511d5eb9bd03e447480a5", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}, "time": "2022-10-24T09:00:36+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.7.1", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "1359e176e9307e906dc3d890bcc9603ff6d90619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/1359e176e9307e906dc3d890bcc9603ff6d90619", "reference": "1359e176e9307e906dc3d890bcc9603ff6d90619", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards"], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}, "time": "2022-06-18T07:21:10+00:00"}], "packages-dev": [{"name": "automattic/vipwpcs", "version": "2.3.3", "source": {"type": "git", "url": "https://github.com/Automattic/VIP-Coding-Standards.git", "reference": "6cd0a6a82bc0ac988dbf9d6a7c2e293dc8ac640b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/VIP-Coding-Standards/zipball/6cd0a6a82bc0ac988dbf9d6a7c2e293dc8ac640b", "reference": "6cd0a6a82bc0ac988dbf9d6a7c2e293dc8ac640b", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7", "php": ">=5.4", "sirbrillig/phpcs-variable-analysis": "^2.11.1", "squizlabs/php_codesniffer": "^3.5.5", "wp-coding-standards/wpcs": "^2.3"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^0.5", "php-parallel-lint/php-parallel-lint": "^1.0", "phpcompatibility/php-compatibility": "^9", "phpcsstandards/phpcsdevtools": "^1.0", "phpunit/phpunit": "^4 || ^5 || ^6 || ^7"}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/Automattic/VIP-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress VIP minimum coding conventions", "keywords": ["phpcs", "standards", "wordpress"], "support": {"issues": "https://github.com/Automattic/VIP-Coding-Standards/issues", "source": "https://github.com/Automattic/VIP-Coding-Standards", "wiki": "https://github.com/Automattic/VIP-Coding-Standards/wiki"}, "time": "2021-09-29T16:20:23+00:00"}, {"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}, "time": "2022-02-04T12:51:07+00:00"}, {"name": "sirbrillig/phpcs-variable-analysis", "version": "v2.11.9", "source": {"type": "git", "url": "https://github.com/sirbrillig/phpcs-variable-analysis.git", "reference": "62730888d225d55a613854b6a76fb1f9f57d1618"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sirbrillig/phpcs-variable-analysis/zipball/62730888d225d55a613854b6a76fb1f9f57d1618", "reference": "62730888d225d55a613854b6a76fb1f9f57d1618", "shasum": ""}, "require": {"php": ">=5.4.0", "squizlabs/php_codesniffer": "^3.5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcsstandards/phpcsdevcs": "^1.1", "phpstan/phpstan": "^1.7", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.5 || ^7.0 || ^8.0 || ^9.0", "sirbrillig/phpcs-import-detection": "^1.1", "vimeo/psalm": "^0.2 || ^0.3 || ^1.1 || ^4.24 || ^5.0@beta"}, "type": "phpcodesniffer-standard", "autoload": {"psr-4": {"VariableAnalysis\\": "VariableAnalysis/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A PHPCS sniff to detect problems with variables.", "keywords": ["phpcs", "static analysis"], "support": {"issues": "https://github.com/sirbrillig/phpcs-variable-analysis/issues", "source": "https://github.com/sirbrillig/phpcs-variable-analysis", "wiki": "https://github.com/sirbrillig/phpcs-variable-analysis/wiki"}, "time": "2022-10-05T23:31:46+00:00"}, {"name": "wp-coding-standards/wpcs", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "7da1894633f168fe244afc6de00d141f27517b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/7da1894633f168fe244afc6de00d141f27517b62", "reference": "7da1894633f168fe244afc6de00d141f27517b62", "shasum": ""}, "require": {"php": ">=5.4", "squizlabs/php_codesniffer": "^3.3.1"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || ^0.6", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "wordpress"], "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}, "time": "2020-05-13T23:57:56+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.0.0"}