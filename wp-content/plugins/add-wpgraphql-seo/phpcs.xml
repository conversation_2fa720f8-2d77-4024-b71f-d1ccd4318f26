<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="Coding Standards" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/squizlabs/PHP_CodeSniffer/master/phpcs.xsd">
	<description>Coding standards for the WPGraphQL Yoast SEO plugin</description>

	<file>.</file>
	<!-- Exclude Composer vendor directory. -->
	<exclude-pattern>*/.git/*</exclude-pattern>
	<exclude-pattern>*/vendor/*</exclude-pattern>
	<exclude-pattern>*/wp-content/*</exclude-pattern>

	<arg value="sp"/> <!-- Show sniff and progress -->
	<arg name="basepath" value="."/><!-- Strip the file paths down to the relevant bit -->
	<arg name="colors"/>
	<arg name="extensions" value="php"/>
	<arg name="parallel" value="20"/>

	<rule ref="WordPressVIPMinimum">
		<exclude name="Generic.Arrays.DisallowShortArraySyntax"/>
		<exclude name="WordPress.PHP.DisallowShortTernary.Found"/>
		<!-- Will need to fix this -->
		<exclude name="WordPressVIPMinimum.Functions.RestrictedFunctions.attachment_url_to_postid_attachment_url_to_postid"/>
	</rule>

	<!-- Tests for PHP version compatibility -->
	<rule ref="PHPCompatibilityWP">
		<config name="testVersion" value="7.0-"/>
	</rule>

  </ruleset>